<?php

use craft\elements\{Entry};

function generateDeliveryDays($deliveryDaysObjects): array {
    $deliveryDays = [];
    foreach ($deliveryDaysObjects as $deliveryDay) {
        $deliveryDays = [
            'mon' => $deliveryDay->mon,
            'tue' => $deliveryDay->tue,
            'wed' => $deliveryDay->wed,
            'thu' => $deliveryDay->thu,
            'fri' => $deliveryDay->fri
        ];
    }
    return $deliveryDays;
}

return [
    'endpoints' => [
        'postal-code-ranges/<postalCode:[^/]+>' => function($postalCode) {
            return [
                'elementType' => Entry::class,
                'criteria' => [
                    'section' => 'postalCodeRanges',
                    'postalCodeFrom' => "<= {$postalCode}",
                    'postalCodeTill' => ">= {$postalCode}"
                ],
                'paginate' => false,
                'pretty' => true,
                'cache' => false,
                'excludes' => 'author',
                'transformer' => function(Entry $entry) {
                    return [
                        'deliveryDays' => generateDeliveryDays($entry->deliveryDays),
                        'from' => $entry->postalCodeFrom,
                        'till' => $entry->postalCodeTill,
                    ];
                },
            ];
        }
    ]
];
