<?php
/**
 * General Configuration
 *
 * All of your system's general configuration settings go in here. You can see a
 * list of the available settings in vendor/craftcms/cms/src/config/GeneralConfig.php.
 *
 * @see \craft\config\GeneralConfig
 */

use craft\config\GeneralConfig;
use craft\helpers\App;

$isDev = App::env('CRAFT_ENVIRONMENT') === 'dev';
$isProd = App::env('CRAFT_ENVIRONMENT') === 'production';
$secKey = App::env('CRAFT_SECURITY_KEY') ?: '';
$cpTrigger = App::env('CP_TRIGGER') ?: 'admin';
$siteUrl = App::env('PRIMARY_SITE_URL');

return GeneralConfig::create()
    // Set the default week start day for date pickers (0 = Sunday, 1 = Monday, etc.)
    ->defaultWeekStartDay(1)
    // Prevent generated URLs from including "index.php"
    ->omitScriptNameInUrls()
    // The secure key Craft will use for hashing and encrypting data
    ->securityKey($secKey)
    // The URI segment that tells Craft to load the control panel
    ->cpTrigger($cpTrigger)
    // Enable Dev Mode on the dev environment (see https://craftcms.com/guides/what-dev-mode-does)
    ->devMode($isDev)
    // Only allow administrative changes on the dev environment
    ->allowAdminChanges($isDev)
    // Disallow robots everywhere except the production environment
    ->disallowRobots(!$isProd)
    // Allow svgs to be uploaded
    ->extraFileKinds([
        'SVG' => [
            'label' => 'SVG',
            'extensions' => ['svg', 'SVG'],
        ],
    ])
    ->preloadSingles()
    ->actionTrigger('api')
    ->addTrailingSlashesToUrls(true)
    ->aliases([
        '@web' => $siteUrl,
        '@webroot' => dirname(__DIR__) . '/web',
        '@assetsUrl' => '@web/uploads',
        '@assetsPath' => '@webroot/uploads',
        '@files' => '@assetsPath/bestanden',
    ]);
