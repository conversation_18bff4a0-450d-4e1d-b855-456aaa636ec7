defaultPlacement: end
fieldLayouts:
  dd37633c-50aa-428d-93a5-68f7000e069b:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 497fe20c-623b-4f3f-b337-a27cef7debc8 # Icoon
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4ba66b7e-d610-4501-b57e-ca018a84c881
            userCondition: null
            warning: null
            width: 100
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            disabled: false
            elementCondition: null
            id: null
            inputType: null
            instructions: null
            label: Titel
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\TitleField
            uid: 8ff016c0-7375-443a-80cd-0d8ae1d04ad1
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            instructions: null
            label: null
            required: false
            tip: null
            type: verbb\navigation\fieldlayoutelements\NodeTypeElements
            uid: b28a8024-1b42-4a1d-9a05-884076ced5fc
            userCondition: null
            warning: null
          -
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: bd93f58e-ac43-40e8-9dfa-6d2aa59b512b
            userCondition: null
          -
            elementCondition:
              class: verbb\navigation\elements\conditions\NodeCondition
              conditionRules:
                -
                  class: craft\elements\conditions\LevelConditionRule
                  maxValue: ''
                  operator: '='
                  step: 1
                  uid: 8e41082f-591d-4d2d-9c4f-0cf146455c60
                  value: '2'
              elementType: verbb\navigation\elements\Node
              fieldContext: global
            fieldUid: 55ed2126-3e73-412b-83f0-cf705282bdba # Button type
            instructions: 'Selecteer of dit item als een button getoond moet worden. Alle links die als button getoond worden zullen onderaan de lijst getoond worden.'
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 32741693-76d3-4304-861a-e38c95b03a17
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: verbb\navigation\elements\conditions\NodeCondition
              conditionRules:
                -
                  class: craft\elements\conditions\LevelConditionRule
                  maxValue: ''
                  operator: '='
                  step: 1
                  uid: 79a5e3c3-443b-4d84-b7d3-ae827b9e10cf
                  value: '1'
              elementType: verbb\navigation\elements\Node
              fieldContext: global
            fieldUid: dca6cdbb-891d-418b-aa83-856b07504d27 # Als USP
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bd50b70d-b87f-4801-88e1-18d8b38a8d96
            userCondition: null
            warning: null
            width: 100
          -
            attribute: newWindow
            elementCondition: null
            id: null
            instructions: null
            label: 'Open in een nieuw tabblad'
            mandatory: false
            orientation: null
            requirable: true
            required: false
            tip: null
            translatable: false
            type: verbb\navigation\fieldlayoutelements\NewWindowField
            uid: e04d7a38-6e2a-4994-b7dd-8f2d37dd3700
            userCondition: null
            warning: null
            width: 100
        name: Node
        uid: 925c1167-3c46-4fee-8947-71fe68a49d00
        userCondition: null
handle: topNavigation
instructions: ''
maxNodes: null
name: 'Top navigatie'
permissions:
  craft\elements\Asset:
    enabled: ''
    permissions:
      - 'volume:70e4abf2-15a6-4646-9878-2b9319640712' # Bestanden
      - 'volume:174c5e31-a28e-4d3d-8991-630c60533e30' # Whitepapers
  craft\elements\Category:
    enabled: ''
    permissions: '*'
  craft\elements\Entry:
    enabled: '1'
    permissions:
      - 'section:5f7deaeb-defc-4bf8-9ed1-a4ef981d5646' # Pagina's
      - 'section:7289e425-5493-40c0-babb-b359ff6da264' # Artikel
      - singles
  craft\elements\Tag:
    enabled: ''
    permissions: '*'
  verbb\navigation\nodetypes\CustomType:
    enabled: '1'
  verbb\navigation\nodetypes\PassiveType:
    enabled: ''
propagationMethod: all
siteSettings:
  f6751190-ae23-49fe-a899-579e5097cd1e: # Milieu Service Nederland
    enabled: true
sortOrder: 1
structure:
  maxLevels: 2
  uid: 99224cda-c541-4f1a-84d3-2a22dca0d7d2
