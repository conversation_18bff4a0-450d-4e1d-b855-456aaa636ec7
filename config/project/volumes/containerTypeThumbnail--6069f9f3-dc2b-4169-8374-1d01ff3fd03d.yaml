fieldLayouts:
  45ca94fc-aef7-470e-9ae7-2d97e5d1dab2:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            disabled: false
            elementCondition: null
            id: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\assets\AssetTitleField
            uid: 427ec00b-27f2-4967-95fb-dc0316d1f782
            userCondition: null
            warning: null
            width: 100
          -
            attribute: alt
            class: null
            cols: null
            disabled: false
            elementCondition: null
            id: null
            instructions: null
            label: null
            name: null
            orientation: null
            placeholder: null
            readonly: false
            requirable: true
            required: false
            rows: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\assets\AltField
            uid: dba9c851-f442-497a-8180-c863053d2057
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 6fe685a2-48f0-4169-93eb-2c8c869f1e0a
        userCondition: null
fs: containerQuotationModule
handle: containerTypeThumbnail
name: 'Offerte - Containers'
sortOrder: 5
titleTranslationKeyFormat: null
titleTranslationMethod: site
transformFs: ''
transformSubpath: ''
