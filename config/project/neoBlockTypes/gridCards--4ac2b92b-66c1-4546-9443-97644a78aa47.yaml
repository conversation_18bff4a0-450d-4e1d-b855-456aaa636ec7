childBlocks: null
conditions: null
description: ''
enabled: true
field: 5dfd3367-7807-44b9-ae90-6eec5a841baa # Content blokken
fieldLayouts:
  efe7f655-9dc2-44e0-85bd-cd1a282570a8:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: ae537b0f-eb40-4072-8ece-72c012c82a7b # Quick link titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a9a471e2-8164-49c9-80db-9029eaa11715
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 6404f65d-f771-4eb1-88b8-72784f69ce6c # Marge onderzijde
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d3bfe204-6350-4a51-b79c-868b58e38a29
            userCondition: null
            warning: null
            width: 100
        name: Algemeen
        uid: c4bd9b0a-cf72-4c7f-b31b-f3b107f3b2eb
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 1e886ab1-cb5d-4715-91bf-19af03ed6b89 # Titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1fa7b6cf-8bd7-4bba-9067-e41e5aa88970
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: dd302985-fdbe-4262-9543-7eeb4070665d # Titel type
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 64a670c3-21ec-4b14-92fc-02cd5d01356c
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: babcda74-f2fd-41d3-94e5-aef3688009ee # Heading color
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7ab9e088-0390-427e-80f1-32d90957efd3
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: 9b6d6476-1f3c-4e5c-b625-5cc303c77545 # Heading als element
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 497bc7c4-5ad9-4d14-9779-8c687ef833b9
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: 3b2678f4-cf23-4a22-9a80-c8086619dfbc # Grid cards
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7eb8583d-f2c8-486a-9e21-903f694843d1
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 535dd41f-a9ba-4094-b113-83e08867f4b7
        userCondition: null
group: 73cd7ede-b980-40bb-9317-56dfe1b1a35e # Content & Text
groupChildBlockTypes: true
handle: gridCards
icon: null
iconFilename: ''
ignorePermissions: true
maxBlocks: 0
maxChildBlocks: 0
maxSiblingBlocks: 0
minBlocks: 0
minChildBlocks: 0
minSiblingBlocks: 0
name: 'Grid cards'
topLevel: true
