childBlocks: null
description: ''
enabled: true
field: 4718db95-2e11-4ce6-be65-a27552b4310b # Dropdowns
fieldLayouts:
  253869ef-119f-4f9a-998e-46263a5091bf:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: e6f50ce2-d5dd-45ac-a3ab-5ac76d79ecc7 # Tekst of link
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bf029392-2e8c-44fc-b998-cb482117c1af
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\ElementCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: e6f50ce2-d5dd-45ac-a3ab-5ac76d79ecc7 # Tekst of link
                  uid: 0391774f-7f84-4827-951a-ccaea43c36ed
                  value: true
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: f383c129-ce94-4dec-9035-791d161417c3 # Dropdown bovenste link
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: aa6387ab-224f-4585-a4ac-e08eca120499
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\ElementCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: e6f50ce2-d5dd-45ac-a3ab-5ac76d79ecc7 # Tekst of link
                  uid: 936aa556-1621-4a96-99eb-3410d8ece357
                  value: false
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 38c1efcb-f821-4570-b1e4-245f4d5d4a1f # Label
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8229b786-35d1-40d3-b76e-0cacbd160c3d
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: cb0f125b-6427-40c3-9e56-d3e4bc6b3209 # Links
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 104aac84-bbbd-4f3a-ab3a-31c7d93e08fc
            userCondition: null
            warning: null
            width: 100
        name: Algemeen
        uid: b8c967f6-75aa-4b2d-9324-f6eedbfc9e63
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 5652023e-7d4a-46dc-bdd0-61f0f2d5c44e # Doorway type
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 62d3f8df-4bc1-4e58-aa3d-899149fcadfa
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\ElementCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 5652023e-7d4a-46dc-bdd0-61f0f2d5c44e # Doorway type
                  operator: in
                  uid: f8e37b74-13b5-40da-a74d-4ae53004676d
                  values:
                    - text
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 1e013ffb-4be7-4996-a651-8f89c8f8ab57 # Doorway content
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: eacc0a84-4629-4ce7-ac6f-9308a13c3310
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\ElementCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 5652023e-7d4a-46dc-bdd0-61f0f2d5c44e # Doorway type
                  operator: in
                  uid: 09278a81-83b7-43d9-bf6f-8e05a9766763
                  values:
                    - text
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 07078825-b538-4b97-b976-c3dcf03010f9 # Button type
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4feb9b9a-0eba-4584-955a-4534b48d435c
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition:
              class: craft\elements\conditions\ElementCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 5652023e-7d4a-46dc-bdd0-61f0f2d5c44e # Doorway type
                  operator: in
                  uid: 9ab55110-a6ea-415a-b515-da19d1bda441
                  values:
                    - text
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 50488a37-2f7e-42e6-bce8-a0d8572bf2b6 # Button status
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1446cc91-bcb4-4468-b62c-43a759b2bebf
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition:
              class: craft\elements\conditions\ElementCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 5652023e-7d4a-46dc-bdd0-61f0f2d5c44e # Doorway type
                  operator: in
                  uid: a2dbeaf3-f7cd-4237-a164-fd97c58e2c9f
                  values:
                    - text
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 9a39dda6-cc47-4bd5-a2e6-de6a677c0f1b # Button icoon positie
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4fa591bf-f813-4cc1-95d6-e3d259dd0d43
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: craft\elements\conditions\ElementCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 5652023e-7d4a-46dc-bdd0-61f0f2d5c44e # Doorway type
                  operator: in
                  uid: 1eb15a24-8321-4906-a64c-fa3c52860279
                  values:
                    - text
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 47e22b40-f8f0-4220-8036-f63187242758 # Link
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b9b72b4f-450e-4d10-80ea-76f54253c919
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\ElementCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 5652023e-7d4a-46dc-bdd0-61f0f2d5c44e # Doorway type
                  operator: in
                  uid: e76fad2e-3a43-46c3-86fb-70d5df4ffee9
                  values:
                    - article
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 36974ca5-0e67-491e-905e-5260d0c63235 # Artikel doorway
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: *************-4f9b-9185-528d0fbbe03b
            userCondition: null
            warning: null
            width: 100
        name: Doorway
        uid: 0724c9e2-53f5-41e5-9b45-5ad424aafebd
        userCondition: null
group: null
handle: dropdown
maxBlocks: 0
maxChildBlocks: 0
maxSiblingBlocks: 0
minBlocks: 0
minChildBlocks: 0
minSiblingBlocks: 0
name: Dropdown
topLevel: true
