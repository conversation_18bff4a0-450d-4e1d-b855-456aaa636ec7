childBlocks: null
conditions: null
description: ''
enabled: true
field: 5dfd3367-7807-44b9-ae90-6eec5a841baa # Content blokken
fieldLayouts:
  b1c4bbe8-2eb5-42a2-8d05-481498b42bd3:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: ae537b0f-eb40-4072-8ece-72c012c82a7b # Quick link titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 495430b5-7060-4881-b0ba-4a4fb24ccb18
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 6404f65d-f771-4eb1-88b8-72784f69ce6c # Marge onderzijde
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d4acc795-ec8f-43ab-aea2-ad77d6fb80d0
            userCondition: null
            warning: null
            width: 100
        name: Algemeen
        uid: 422e7011-7b89-4317-abe6-dd3f450cd042
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: d56e2cc0-4668-4bc3-85b3-ff852572e3d5 # Doorway titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 847f18fd-a9a4-4af2-9984-830b54dfd4a2
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: dd302985-fdbe-4262-9543-7eeb4070665d # Titel type
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7590e57a-9f98-4174-b498-8394a2d61d87
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition: null
            fieldUid: 9b6d6476-1f3c-4e5c-b625-5cc303c77545 # Heading als element
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0ad42157-1044-4758-9f62-d3c44e82addd
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition: null
            fieldUid: 1b204006-9186-4364-a6ef-cf8f7eab5e0b # Button
            instructions: null
            label: Knop
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3339a9df-1a02-4d93-8d7e-36e1e8f108b0
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: dee27d17-2e7a-416a-80b0-6d8bfb469d2f
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
                  operator: in
                  uid: ad034277-7f1e-43f6-bb33-4370652d78d2
                  values:
                    - custom
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 260cb4a3-9d13-4d6a-89fc-f30c790a227e # Doorway items
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 87e4c8be-97aa-4a72-8052-85bb173a961c
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
                  operator: in
                  uid: 09de0fac-285a-4eb8-b72c-1be2997525c1
                  values:
                    - mostRecent
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 6e7cdd8e-8175-442b-b2bf-1200d6f210a3 # No follow
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e59c799d-060f-444a-b460-42bf7987c5f8
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
                  operator: in
                  uid: 5e2b2e82-80f2-4804-b4e9-14f85b7ccb67
                  values:
                    - mostRecent
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 90464013-fa7f-4fa3-9d28-b617481677f2 # Type
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 91aedf70-f2fe-4485-93d6-87b5976f5e82
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
                  operator: in
                  uid: 54c7fb4f-adca-4ad8-afd2-ddadc8b06359
                  values:
                    - mostRecent
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 4c9d7af2-80e6-4a3a-9e2b-a5a4f473afd8 # Afvalstroom
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 455ecdbe-ca87-4d04-ab90-280b7c6ce31d
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
                  operator: in
                  uid: 02a73638-4394-4a67-bb96-77faa0aaec18
                  values:
                    - mostRecent
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 78561ad9-1c32-4189-b27c-4b8001352e41 # Bedrijfstype
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 636d0c97-b7ea-4935-909a-c29db810a65d
            userCondition: null
            warning: null
            width: 25
        name: Content
        uid: 2d39212a-2497-4550-8dda-8331644b6014
        userCondition: null
group: 3915fc7a-399a-40ed-9253-658ccf60490e # Structural & Promotional
groupChildBlockTypes: true
handle: articleDoorway
icon: null
iconFilename: ''
ignorePermissions: true
maxBlocks: 0
maxChildBlocks: 0
maxSiblingBlocks: 0
minBlocks: 0
minChildBlocks: 0
minSiblingBlocks: 0
name: 'Artikel/pagina doorway'
topLevel: true
