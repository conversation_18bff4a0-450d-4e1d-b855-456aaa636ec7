childBlocks: null
conditions: null
description: ''
enabled: true
field: 5dfd3367-7807-44b9-ae90-6eec5a841baa # Content blokken
fieldLayouts:
  1c377f4b-4bc6-4b31-be91-617efa9bd462:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: ae537b0f-eb40-4072-8ece-72c012c82a7b # Quick link titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 85d89189-3e39-4666-a4fe-97cb4f69a5f6
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 6404f65d-f771-4eb1-88b8-72784f69ce6c # Marge onderzijde
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 23ee8af5-f032-45eb-be0b-0fe47dc9ad37
            userCondition: null
            warning: null
            width: 100
        name: Algemeen
        uid: e2e8455e-caab-49cd-ab97-e44581cd37e8
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 9cd08017-61f8-47a5-ad54-7a398fd5f78c # Titel
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e31da13e-f735-4d72-838a-4bb1278e7768
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: dd302985-fdbe-4262-9543-7eeb4070665d # Titel type
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c4f89732-bbab-4397-b0de-872edb36882e
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition: null
            fieldUid: 9b6d6476-1f3c-4e5c-b625-5cc303c77545 # Heading als element
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 76e1fe2d-99f1-4f62-9875-3c221e577274
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition: null
            fieldUid: 56f5a2de-3c56-472c-8837-06a8e05714c9 # USP
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3dbdb055-c21e-41a8-935e-998506652c10
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 1b204006-9186-4364-a6ef-cf8f7eab5e0b # Button
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e753bc85-9bc1-420b-8704-0858fc16cd53
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 7320717a-9808-495f-84ac-03d7403b3f35
        userCondition: null
group: 3915fc7a-399a-40ed-9253-658ccf60490e # Structural & Promotional
groupChildBlockTypes: true
handle: ctaUspBig
icon: null
iconFilename: ''
ignorePermissions: true
maxBlocks: 0
maxChildBlocks: 0
maxSiblingBlocks: 0
minBlocks: 0
minChildBlocks: 0
minSiblingBlocks: 0
name: 'CTA USP Groot'
topLevel: true
