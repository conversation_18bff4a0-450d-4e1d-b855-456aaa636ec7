childBlocks: null
conditions: null
description: ''
enabled: true
field: 5dfd3367-7807-44b9-ae90-6eec5a841baa # Content blokken
fieldLayouts:
  1f448b41-6d4c-4bf6-8911-4e9dff02222d:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: ae537b0f-eb40-4072-8ece-72c012c82a7b # Quick link titel
            instructions: 'Title of the element used in the Quick links'
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8eb6860b-56c8-4358-b47a-409ec417b709
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 6404f65d-f771-4eb1-88b8-72784f69ce6c # Marge onderzijde
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 639e1029-6b20-4ae5-aa54-706e2a43e096
            userCondition: null
            warning: null
            width: 100
        name: Algemeen
        uid: e6633287-0de2-45b7-81ff-3d927e90e73d
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 7118593c-3510-4e72-9bd7-f4c5e9b46fae # Slider titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8afb643f-fb33-4be3-901d-4272c593fec2
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: dd302985-fdbe-4262-9543-7eeb4070665d # Titel type
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c7a9bad6-ad01-49e2-b8a2-b093c8e0ff79
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition: null
            fieldUid: 9b6d6476-1f3c-4e5c-b625-5cc303c77545 # Heading als element
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d5ac100d-1e32-4c05-8f6e-db721c09898e
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition: null
            fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
            instructions: null
            label: 'Slider weergave'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3f815289-2209-4102-93be-6d55040e73b6
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
                  operator: in
                  uid: f0d16c74-9f11-4c2e-b476-1d3d0a9fe7dd
                  values:
                    - custom
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 9c952ae8-c04c-478d-a34e-8fa347e5d1ba # Slider
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e31f1a4d-3abb-4d2f-9741-eb48e36d46b9
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
                  operator: in
                  uid: cb760e71-e81a-49d7-b4f9-b0d99974880a
                  values:
                    - mostRecent
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 6e7cdd8e-8175-442b-b2bf-1200d6f210a3 # No follow
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 010ce7d7-25eb-49a8-a823-93e6b6915e13
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
                  operator: in
                  uid: d29711e1-5fea-4eb3-b6f4-d26d9328b44e
                  values:
                    - mostRecent
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 721572de-c9eb-438c-be45-4c2f1cd5d8df # Slider limiet
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: *************-484d-a4d3-af09fd774915
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
                  operator: in
                  uid: 68866d10-0b6d-48fe-842c-ade4f5c191b5
                  values:
                    - mostRecent
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 90464013-fa7f-4fa3-9d28-b617481677f2 # Type
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 846b06f7-f1e5-4f8d-97c0-02962c49939b
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
                  operator: in
                  uid: 8d0d85ec-5056-4bee-93ba-af910d567555
                  values:
                    - mostRecent
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 4c9d7af2-80e6-4a3a-9e2b-a5a4f473afd8 # Afvalstroom
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c0156004-df97-499f-88f2-25e5e57a60ae
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: ce43b68c-f50f-445b-a579-5bfa9205dd53 # Doorway weergave
                  operator: in
                  uid: a5e780c3-38a4-4808-ada3-23a50e1f7cb4
                  values:
                    - mostRecent
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: 78561ad9-1c32-4189-b27c-4b8001352e41 # Bedrijfstype
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 053cba36-2102-4e25-867e-25037c5e4879
            userCondition: null
            warning: null
            width: 25
        name: Content
        uid: a261ae35-a044-433c-bf0c-282ec6b8e9f5
        userCondition: null
group: f4966ee8-448b-4a2f-a2e1-f66e446efbb4 # Media & Visuals
groupChildBlockTypes: true
handle: articleSlider
icon: null
iconFilename: ''
ignorePermissions: true
maxBlocks: 0
maxChildBlocks: 0
maxSiblingBlocks: 0
minBlocks: 0
minChildBlocks: 0
minSiblingBlocks: 0
name: 'Artikel slider'
topLevel: true
