columnSuffix: null
contentColumnType: string
fieldGroup: 99c15b9d-32b7-4875-956c-2810c433726d # Offerte module
handle: stepContainers
instructions: null
name: 'Stap containers'
searchable: true
settings:
  blockTypeFields: 0
  changedFieldIndicator: 1178683655
  columns:
    __assoc__:
      -
        - 8f6ba21a-6b7a-4034-a81b-73754988549d # Titel
        -
          __assoc__:
            -
              - width
              - ''
      -
        - 5feb0b2a-126f-4e04-a956-19a5a3f2519f # Subtitel
        -
          __assoc__:
            -
              - width
              - ''
  contentTable: '{{%stc_stepcontainers}}'
  fieldLayout: row
  maxRows: null
  minRows: null
  propagationKeyFormat: null
  propagationMethod: all
  selectionLabel: null
  staticField: true
translationKeyFormat: null
translationMethod: none
type: verbb\supertable\fields\SuperTableField
