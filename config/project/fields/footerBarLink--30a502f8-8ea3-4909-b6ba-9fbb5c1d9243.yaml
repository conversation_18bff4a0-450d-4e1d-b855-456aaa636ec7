columnSuffix: null
contentColumnType: string
fieldGroup: 96be258d-582d-4f44-98f8-e1bb4386ee6d # Footer
handle: footerBarLink
instructions: null
name: 'Footer link'
searchable: false
settings:
  allowCustomText: true
  allowTarget: true
  autoNoReferrer: true
  customTextMaxLength: 0
  customTextRequired: false
  defaultLinkName: entry
  defaultText: ''
  enableAllLinkTypes: false
  enableAriaLabel: false
  enableElementCache: false
  enableTitle: false
  typeSettings:
    asset:
      allowCrossSiteLink: false
      allowCustomQuery: false
      enabled: false
      sources: '*'
    category:
      allowCrossSiteLink: false
      allowCustomQuery: false
      enabled: false
      sources: '*'
    custom:
      allowAliases: false
      disableValidation: false
      enabled: true
    email:
      allowAliases: false
      disableValidation: false
      enabled: false
    entry:
      allowCrossSiteLink: false
      allowCustomQuery: false
      enabled: true
      sources: '*'
    site:
      enabled: false
      sites: '*'
    tel:
      allowAliases: false
      disableValidation: false
      enabled: false
    url:
      allowAliases: false
      disableValidation: false
      enabled: true
    user:
      allowCrossSiteLink: false
      allowCustomQuery: false
      enabled: false
      sources: '*'
translationKeyFormat: null
translationMethod: none
type: lenz\linkfield\fields\LinkField
