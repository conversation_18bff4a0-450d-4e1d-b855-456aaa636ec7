columnSuffix: null
contentColumnType: boolean
fieldGroup: f3fe740f-9fb4-43f9-9335-45254b567afd # Key Visual
handle: keyVisualCloseContentGap
instructions: 'Selecteer deze optie om de marge onder de keyvisual weg te halen zodat het eerste content blok aansluit onderaan de keyvisual. Bij het type doorways en slider zal de content niet geheel aansluiten om ruimte te houden tussen de content en de doorways.'
name: 'Key Visual Laat content aansluiten'
searchable: false
settings:
  default: false
  offLabel: null
  onLabel: null
translationKeyFormat: null
translationMethod: none
type: craft\fields\Lightswitch
