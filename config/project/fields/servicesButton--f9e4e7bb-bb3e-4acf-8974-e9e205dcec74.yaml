columnSuffix: null
contentColumnType: string
fieldGroup: 88f1074a-b282-414f-91a1-080e9ae5255c # Common
handle: servicesButton
instructions: null
name: Button
searchable: false
settings:
  allowCustomText: true
  allowTarget: false
  autoNoReferrer: false
  customTextMaxLength: 0
  customTextRequired: false
  defaultLinkName: entry
  defaultText: ''
  enableAllLinkTypes: false
  enableAriaLabel: false
  enableElementCache: false
  enableTitle: false
  typeSettings:
    asset:
      allowCrossSiteLink: false
      allowCustomQuery: false
      enabled: false
      sources: '*'
    category:
      allowCrossSiteLink: false
      allowCustomQuery: false
      enabled: false
      sources: '*'
    custom:
      allowAliases: false
      disableValidation: false
      enabled: true
    email:
      allowAliases: false
      disableValidation: false
      enabled: false
    entry:
      allowCrossSiteLink: false
      allowCustomQuery: false
      enabled: true
      sources: '*'
    site:
      enabled: true
      sites: '*'
    tel:
      allowAliases: false
      disableValidation: false
      enabled: false
    url:
      allowAliases: false
      disableValidation: false
      enabled: true
    user:
      allowCrossSiteLink: false
      allowCustomQuery: false
      enabled: false
      sources: '*'
translationKeyFormat: null
translationMethod: none
type: lenz\linkfield\fields\LinkField
