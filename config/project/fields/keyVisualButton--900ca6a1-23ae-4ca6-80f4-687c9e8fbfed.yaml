columnSuffix: null
contentColumnType: string
fieldGroup: f3fe740f-9fb4-43f9-9335-45254b567afd # Key Visual
handle: keyVisualButton
instructions: 'Als deze CTA knop gelinkt wordt, dan wordt de ''scroll down'' knop hiermee vervangen. Bij ''No link'' blijft de ''scroll down'' knop zichtbaar.'
name: 'Key Visual CTA'
searchable: false
settings:
  allowCustomText: true
  allowTarget: true
  autoNoReferrer: false
  customTextMaxLength: 0
  customTextRequired: false
  defaultLinkName: entry
  defaultText: ''
  enableAllLinkTypes: false
  enableAriaLabel: false
  enableElementCache: false
  enableTitle: false
  typeSettings:
    asset:
      allowCrossSiteLink: false
      allowCustomQuery: false
      enabled: false
      sources: '*'
    category:
      allowCrossSiteLink: false
      allowCustomQuery: false
      enabled: false
      sources: '*'
    custom:
      allowAliases: false
      disableValidation: false
      enabled: false
    email:
      allowAliases: false
      disableValidation: false
      enabled: false
    entry:
      allowCrossSiteLink: false
      allowCustomQuery: false
      enabled: true
      sources:
        - singles
        - 'section:7289e425-5493-40c0-babb-b359ff6da264' # Artikel
        - 'section:b1897c02-446f-4850-8497-2b3388cab10c' # Auteurs
        - 'section:5f7deaeb-defc-4bf8-9ed1-a4ef981d5646' # Pagina's
    site:
      enabled: false
      sites: '*'
    tel:
      allowAliases: false
      disableValidation: false
      enabled: false
    url:
      allowAliases: false
      disableValidation: false
      enabled: true
    user:
      allowCrossSiteLink: false
      allowCustomQuery: false
      enabled: false
      sources: '*'
translationKeyFormat: null
translationMethod: none
type: lenz\linkfield\fields\LinkField
