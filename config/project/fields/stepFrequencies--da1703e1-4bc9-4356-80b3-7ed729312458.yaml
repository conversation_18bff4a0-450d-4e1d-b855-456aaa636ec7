columnSuffix: null
contentColumnType: string
fieldGroup: 99c15b9d-32b7-4875-956c-2810c433726d # Offerte module
handle: stepFrequencies
instructions: null
name: 'Stap leveringsfrequenties'
searchable: true
settings:
  blockTypeFields: 0
  changedFieldIndicator: 2052834266
  columns:
    __assoc__:
      -
        - c261ee56-3f51-4371-9813-88bc134cb1a6 # Titel
        -
          __assoc__:
            -
              - width
              - ''
      -
        - cc01a99e-6546-4a85-b1c9-82a27a91aaf6 # Subtitel
        -
          __assoc__:
            -
              - width
              - ''
  contentTable: '{{%stc_stepfrequencies}}'
  fieldLayout: row
  maxRows: null
  minRows: null
  propagationKeyFormat: null
  propagationMethod: all
  selectionLabel: null
  staticField: true
translationKeyFormat: null
translationMethod: none
type: verbb\supertable\fields\SuperTableField
