columnSuffix: null
contentColumnType: string
fieldGroup: 6fb681e6-bdf6-4f96-b8fd-f0a732620b63 # Media
handle: mediaImage
instructions: 'Selecteer een afbeelding. De afbeelding wordt overschreven zodra de URL wordt ingevuld.'
name: Afbeelding
searchable: false
settings:
  allowSelfRelations: false
  allowSubfolders: false
  allowUploads: true
  allowedKinds: null
  defaultUploadLocationSource: 'volume:b9ebc691-beac-462a-a36f-b24221cb8798' # Afbeeldingen
  defaultUploadLocationSubpath: null
  localizeRelations: false
  maxRelations: null
  minRelations: null
  previewMode: full
  restrictFiles: false
  restrictLocation: false
  restrictedDefaultUploadSubpath: null
  restrictedLocationSource: 'volume:7b5602dc-2373-4d41-a4f7-a7efcca8052f' # Footer 
  restrictedLocationSubpath: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Asset
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\assets\AssetCondition
  selectionLabel: null
  showSiteMenu: true
  showUnpermittedFiles: false
  showUnpermittedVolumes: false
  source: null
  sources: '*'
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: site
type: craft\fields\Assets
