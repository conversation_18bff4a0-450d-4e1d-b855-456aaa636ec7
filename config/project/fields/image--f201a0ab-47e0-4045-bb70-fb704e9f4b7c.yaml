columnSuffix: null
contentColumnType: string
fieldGroup: 128b4b30-a8d5-4ca0-bc97-e1d6fd67efef # Text image element
handle: image
instructions: 'Selecteer een afbeelding. De afbeelding wordt overschreven zodra de URL wordt ingevuld.'
name: Afbeelding
searchable: false
settings:
  allowSelfRelations: false
  allowSubfolders: false
  allowUploads: true
  allowedKinds:
    - image
  defaultUploadLocationSource: 'volume:b9ebc691-beac-462a-a36f-b24221cb8798' # Afbeeldingen
  defaultUploadLocationSubpath: null
  localizeRelations: false
  maxRelations: 1
  minRelations: null
  previewMode: full
  restrictFiles: true
  restrictLocation: false
  restrictedDefaultUploadSubpath: null
  restrictedLocationSource: 'volume:7b5602dc-2373-4d41-a4f7-a7efcca8052f' # Footer 
  restrictedLocationSubpath: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Asset
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\assets\AssetCondition
  selectionLabel: null
  showSiteMenu: true
  showUnpermittedFiles: false
  showUnpermittedVolumes: false
  source: null
  sources:
    - 'volume:b9ebc691-beac-462a-a36f-b24221cb8798' # Afbeeldingen
    - 'volume:e7f8a2e5-55cb-4703-96ab-3349fe36219c' # Content blokken
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: site
type: craft\fields\Assets
