columnSuffix: null
contentColumnType: string
fieldGroup: 957e9cac-52d9-4a69-ac46-0c5a856d8d3e # Archief vernietiging module
handle: periodicModuleReference
instructions: '<PERSON><PERSON> hier de module die u wilt gebruiken voor de afhandeling van de periodieke afvalstroom.'
name: 'Periodieke module referentie'
searchable: false
settings:
  allowSelfRelations: false
  localizeRelations: false
  maxRelations: 1
  minRelations: 1
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Entry
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\entries\EntryCondition
  selectionLabel: null
  showSiteMenu: false
  source: null
  sources:
    - singles
  targetSiteId: null
  validateRelatedElements: false
  viewMode: null
translationKeyFormat: null
translationMethod: site
type: craft\fields\Entries
