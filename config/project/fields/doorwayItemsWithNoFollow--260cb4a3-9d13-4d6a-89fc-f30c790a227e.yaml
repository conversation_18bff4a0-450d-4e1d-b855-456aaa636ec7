columnSuffix: null
contentColumnType: string
fieldGroup: b582892d-c16c-4db0-9bde-1fe44c3a09ef # Artikel/pagina doorway
handle: doorwayItemsWithNoFollow
instructions: null
name: 'Doorway items'
searchable: true
settings:
  blockTypeFields: 0
  changedFieldIndicator: 172688410
  columns:
    __assoc__:
      -
        - c8cb14ab-2289-47cd-b43d-43b5d3d3f362 # Doorway item
        -
          __assoc__:
            -
              - width
              - ''
      -
        - 544638a8-30eb-475e-acad-a345a441559f # Doorway no follow
        -
          __assoc__:
            -
              - width
              - ''
  contentTable: '{{%stc_doorwayitemswithnofollow}}'
  fieldLayout: table
  maxRows: 3
  minRows: null
  propagationKeyFormat: null
  propagationMethod: all
  selectionLabel: 'Voeg een doorway item toe'
  staticField: null
translationKeyFormat: null
translationMethod: none
type: verbb\supertable\fields\SuperTableField
