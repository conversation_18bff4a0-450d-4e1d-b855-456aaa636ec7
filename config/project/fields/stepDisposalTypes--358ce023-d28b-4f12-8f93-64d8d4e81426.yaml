columnSuffix: null
contentColumnType: string
fieldGroup: 99c15b9d-32b7-4875-956c-2810c433726d # Offerte module
handle: stepDisposalTypes
instructions: null
name: 'Stap afvalstromen'
searchable: true
settings:
  blockTypeFields: 0
  changedFieldIndicator: 135809564
  columns:
    __assoc__:
      -
        - 7512a1cc-76b9-4588-9b8a-8a5726ce2c9b # Titel
        -
          __assoc__:
            -
              - width
              - ''
      -
        - 8df02f5b-57a8-4e06-b403-0b51191fbebe # Subtitel
        -
          __assoc__:
            -
              - width
              - ''
  contentTable: '{{%stc_stepdisposaltypes}}'
  fieldLayout: row
  maxRows: null
  minRows: null
  propagationKeyFormat: null
  propagationMethod: all
  selectionLabel: null
  staticField: true
translationKeyFormat: null
translationMethod: none
type: verbb\supertable\fields\SuperTableField
