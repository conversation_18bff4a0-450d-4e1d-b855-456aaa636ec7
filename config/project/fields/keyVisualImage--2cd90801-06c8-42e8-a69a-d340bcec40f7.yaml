columnSuffix: null
contentColumnType: string
fieldGroup: f3fe740f-9fb4-43f9-9335-45254b567afd # Key Visual
handle: keyVisualImage
instructions: 'Dit veld is verplicht omdat bij een video deze afbeelding als placeholder wordt gebruikt.'
name: 'Key Visual afbeelding'
searchable: false
settings:
  allowSelfRelations: false
  allowSubfolders: false
  allowUploads: true
  allowedKinds:
    - image
  defaultUploadLocationSource: 'volume:7b5602dc-2373-4d41-a4f7-a7efcca8052f' # Footer 
  defaultUploadLocationSubpath: null
  localizeRelations: false
  maxRelations: 1
  minRelations: null
  previewMode: full
  restrictFiles: true
  restrictLocation: false
  restrictedDefaultUploadSubpath: null
  restrictedLocationSource: 'volume:7b5602dc-2373-4d41-a4f7-a7efcca8052f' # Footer 
  restrictedLocationSubpath: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Asset
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\assets\AssetCondition
  selectionLabel: 'Voeg een afbeelding toe'
  showSiteMenu: true
  showUnpermittedFiles: false
  showUnpermittedVolumes: false
  source: null
  sources:
    - 'volume:b9ebc691-beac-462a-a36f-b24221cb8798' # Afbeeldingen
    - 'volume:e7f8a2e5-55cb-4703-96ab-3349fe36219c' # Content blokken
    - 'volume:174c5e31-a28e-4d3d-8991-630c60533e30' # Whitepapers
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: site
type: craft\fields\Assets
