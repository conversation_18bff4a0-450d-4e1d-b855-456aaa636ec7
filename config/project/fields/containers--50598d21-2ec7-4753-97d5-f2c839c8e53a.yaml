columnSuffix: null
contentColumnType: string
fieldGroup: a8f98187-f2b2-4be4-abc6-ffc7f6638c25 # Afvalstroom
handle: containers
instructions: 'Selecteer de containers die bij deze afvalstroom horen. Als er meer dan 7 containers zijn word er een "Toon meer" balk toegevoegd. <PERSON>gor<PERSON> hier, van boven naar beneden, bepa<PERSON> de volgorde in de configurator.'
name: Containers
searchable: false
settings:
  blockTypeFields: 0
  changedFieldIndicator: 1908588714
  columns:
    __assoc__:
      -
        - e872de09-105d-4026-9064-fdd4e95ddc84 # Afbeelding
        -
          __assoc__:
            -
              - width
              - ''
      -
        - 23117823-7f50-44b1-84f1-94627e07d87c # Container
        -
          __assoc__:
            -
              - width
              - ''
      -
        - 3281935c-0a7c-48f0-98eb-fc32678f2b83 # Meest gekozen?
        -
          __assoc__:
            -
              - width
              - ''
  contentTable: '{{%stc_containers}}'
  fieldLayout: table
  maxRows: null
  minRows: 1
  propagationKeyFormat: null
  propagationMethod: all
  selectionLabel: 'Voeg een container toe'
  staticField: null
translationKeyFormat: null
translationMethod: none
type: verbb\supertable\fields\SuperTableField
