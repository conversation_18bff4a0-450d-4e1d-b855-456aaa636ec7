columnSuffix: null
contentColumnType: string
fieldGroup: 99c15b9d-32b7-4875-956c-2810c433726d # Offerte module
handle: successCtas
instructions: null
name: 'Succes CTAs'
searchable: false
settings:
  blockTypeFields: 0
  changedFieldIndicator: 1118176378
  columns:
    __assoc__:
      -
        - 5718d04d-60d9-4f8a-91c7-b0a57d994398 # Icoon
        -
          __assoc__:
            -
              - width
              - ''
      -
        - 6dc51fee-dd51-4fa8-98a5-ff06d9dc2c35 # Titel
        -
          __assoc__:
            -
              - width
              - ''
      -
        - 2340d081-58d8-4869-8c3a-81857f72a189 # Subtitel
        -
          __assoc__:
            -
              - width
              - ''
  contentTable: '{{%stc_successctas}}'
  fieldLayout: matrix
  maxRows: 4
  minRows: null
  propagationKeyFormat: null
  propagationMethod: all
  selectionLabel: 'Voeg een CTA toe'
  staticField: null
translationKeyFormat: null
translationMethod: none
type: verbb\supertable\fields\SuperTableField
