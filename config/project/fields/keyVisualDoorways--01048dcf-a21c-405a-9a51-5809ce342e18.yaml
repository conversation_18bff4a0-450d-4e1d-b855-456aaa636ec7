columnSuffix: null
contentColumnType: string
fieldGroup: f3fe740f-9fb4-43f9-9335-45254b567afd # Key Visual
handle: keyVisualDoorways
instructions: null
name: 'Key Visual doorways'
searchable: true
settings:
  blockTypeFields: 0
  changedFieldIndicator: 965994197
  columns:
    __assoc__:
      -
        - f9d2d0bb-6329-4249-9de3-2e3b2a2824d2 # Icoon
        -
          __assoc__:
            -
              - width
              - ''
      -
        - 7eb4b8fc-2b2e-44cd-8dd5-f3f014086e2a # Titel
        -
          __assoc__:
            -
              - width
              - ''
      -
        - 102eb153-1af2-4eb1-b4d5-45203369113c # Intro
        -
          __assoc__:
            -
              - width
              - ''
      -
        - ca63f1db-38c3-4b3f-87ef-e6476da447a3 # Link
        -
          __assoc__:
            -
              - width
              - ''
      -
        - a9321c80-8217-4414-92f4-936ee667071b # No follow
        -
          __assoc__:
            -
              - width
              - ''
      -
        - 581a5fb4-f97f-4a31-8eec-a926d118602d # Hover kleur
        -
          __assoc__:
            -
              - width
              - ''
  contentTable: '{{%stc_keyvisualdoorways}}'
  fieldLayout: matrix
  maxRows: 3
  minRows: 0
  propagationKeyFormat: null
  propagationMethod: all
  selectionLabel: 'Voeg een doorway item toe'
  staticField: null
translationKeyFormat: null
translationMethod: none
type: verbb\supertable\fields\SuperTableField
