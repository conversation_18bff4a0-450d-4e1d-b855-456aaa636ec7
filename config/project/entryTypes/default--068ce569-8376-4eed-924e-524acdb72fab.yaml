fieldLayouts:
  df6dc0d1-bfed-4d32-9e1d-64139904e8e1:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            disabled: false
            elementCondition: null
            id: null
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 2a5b1e44-3880-40a1-96df-d44d142ecfb3
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: ce40eeab-0638-499f-ba19-ec77f4f2d9a2 # Meenemen in zoekresultaten
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: fc689092-08c4-4cb3-9ab1-04d110a8b1e1
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: 96a9cc4d-f27c-4591-b596-9daf1094c431 # Toon bestelknop
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b2430af1-f520-4429-80b4-be14d003cf18
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: bc86bd8c-84e1-480d-96d3-851abea330d6 # Thumbnail
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 829ec123-cdf9-41a1-ae07-34743baab7aa
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: 25deec0e-51c4-42eb-9a6c-3301d7f71cf7 # Footer
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ec860a7c-6942-4bfc-a83b-c8dabf059665
            userCondition: null
            warning: null
            width: 50
        name: Algemeen
        uid: b69a5a15-9a23-46b9-b56c-5c0a3797bd5f
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: d0c2d555-4020-481c-ad37-f92f6ab83488 # SEO
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 18dd0a5a-78f8-436c-8479-07710dfd1211
            userCondition: null
            warning: null
            width: 100
        name: SEO
        uid: e2211272-7280-485c-b376-efc00ad33904
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
            instructions: null
            label: 'Key visual type'
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 24169834-a702-4078-8a91-3d46ce395037
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 912c5988-6030-4f4b-89e2-ae7c50b479c8 # Key Visual Laat content aansluiten
            instructions: null
            label: 'Laat content aansluiten'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0c0f7fbb-3f4f-403a-8551-d32b81b4a0fe
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: fabee1b7-bcae-4fa7-b5cf-8e1d16133188
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: e4a71150-ee41-44a4-8f92-9f5bed69fdc9 # Groene achtergrond
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8b29dd96-a7b1-4eea-acc7-434372daeed3
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: 7ba67565-29fe-4c46-bd72-b6dac8888844
            userCondition: null
          -
            elementCondition: null
            fieldUid: 0b448480-767d-4080-aaaa-a50f96827cb5 # Key Visual titel
            instructions: null
            label: Titel
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3e756ba0-efa1-4d77-bed2-a9fb46674846
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: ni
                  uid: 97b1c289-4892-422c-894c-31ea7780099c
                  values:
                    - sliderDoorways
                    - fullWidth
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 8e47a086-8b54-4819-8209-8f12ceab7730 # Key Visual gemarkeerde woorden
            instructions: 'Vul de woorden in die in groen getoond moeten worden'
            label: 'Gemarkeerde woorden'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: dba15d10-7a43-41d0-a6cf-c5066970f4bd
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: f4dabf90-f743-40f2-a155-bc22e923b3f6 # Key Visual intro
            instructions: null
            label: Intro
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 95745df1-b8c2-49b4-a5f4-79a8bad398d7
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: f495c46e-d27b-4b6d-896f-01fb5f096b26
                  values:
                    - basic
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: db330955-7cf2-4de7-944f-b22efef18a49 # Key Visual uitlijning
            instructions: null
            label: 'Uitlijning titel en intro'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 26248274-ce10-4105-93ba-a57a18e5662c
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: aadf9e37-1990-4799-91a1-7575ba8166c2
                  values:
                    - doorways
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 01048dcf-a21c-405a-9a51-5809ce342e18 # Key Visual doorways
            instructions: null
            label: Doorways
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b1df64e7-dca1-4630-9832-5d4d6f18eb92
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 039cd7ff-8ab0-4523-b76a-42056339a221
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: e486a935-0b76-4092-8cbd-acc476e2c3b8 # Key visual slider afbeeldingen
            instructions: null
            label: Afbeeldingen
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 94c8456d-9707-4f29-bef4-ccfd87fe6382
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: b2bff745-90a4-420a-be5c-75332dbf8643
                  values:
                    - media
                    - fullWidth
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 2cd90801-06c8-42e8-a69a-d340bcec40f7 # Key Visual afbeelding
            instructions: null
            label: Afbeelding
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bebaf891-b6a8-4502-9b2e-8bd1083bff9e
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 5586f5c1-bfa9-4025-9503-323a00912a12
                  values:
                    - media
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 4756cf29-331b-4cf9-ba78-d088f585ef10 # Key Visual video
            instructions: null
            label: Video
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b60b4c77-7ccd-422c-a775-46ef1bd53063
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: ni
                  uid: bc357eec-234b-4d9d-ad81-064e00f388f3
                  values:
                    - sliderDoorways
                    - fullWidth
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 900ca6a1-23ae-4ca6-80f4-687c9e8fbfed # Key Visual CTA
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 735d665a-dca9-43c0-af40-297ca42b0623
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: ni
                  uid: 896630fb-9fad-4a89-96a4-4b5add34a361
                  values:
                    - sliderDoorways
                    - fullWidth
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 101f9198-3e34-420c-9907-fde2a2fbe291 # Key Visual CTA no follow
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 342c45ec-5a78-430f-ad73-77be52b6f1f6
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 43f6fccb-3211-4956-a446-c62166fd887e
                  values:
                    - media
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 62f2612f-7ae2-410d-8c97-632092b5780e # Key Visual scrol verder tekst
            instructions: null
            label: 'Scroll verder tekst'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 374f4f48-d054-4a6f-897c-55535bb1c38c
            userCondition: null
            warning: null
            width: 100
        name: 'Key Visual'
        uid: f27a403c-8435-401c-8523-9da910412b96
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 5dfd3367-7807-44b9-ae90-6eec5a841baa # Content blokken
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8b4b2bd8-2d97-4ff0-8f24-d03203d077d3
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: bbbbb522-0f7a-488c-8374-b177ecbb9723
        userCondition: null
handle: default
hasTitleField: true
name: Default
section: 5f7deaeb-defc-4bf8-9ed1-a4ef981d5646 # Pagina's
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
sortOrder: 1
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
