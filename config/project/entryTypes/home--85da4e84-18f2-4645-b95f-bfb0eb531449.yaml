fieldLayouts:
  2e2c3fa3-93bf-4e06-95d8-e02a821143e7:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            disabled: false
            elementCondition: null
            id: null
            inputType: null
            instructions: null
            label: Titel
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 5c25f9b0-1a67-4544-856d-aa28bd1c04cd
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: ce40eeab-0638-499f-ba19-ec77f4f2d9a2 # Meenemen in zoekresultaten
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5489b53a-6885-403e-87db-2b60747ea60a
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: 96a9cc4d-f27c-4591-b596-9daf1094c431 # Toon bestelknop
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 61429d2b-84ca-45a6-acb0-203ce93238b4
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: bc86bd8c-84e1-480d-96d3-851abea330d6 # Thumbnail
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5d25e834-8266-4512-a8d3-77c74f2002e2
            userCondition: null
            warning: null
            width: 100
        name: Algemeen
        uid: 16245801-898d-45d7-adcb-0b00cba9188e
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ecb78e45-a7ff-480d-98b3-27016d8d22ed
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 912c5988-6030-4f4b-89e2-ae7c50b479c8 # Key Visual Laat content aansluiten
            instructions: null
            label: 'Laat content aansluiten'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8aae4dad-aeb8-4201-9829-61ad53cd64ef
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 823bb4bf-1111-461d-b936-90d7b2580b38
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: e4a71150-ee41-44a4-8f92-9f5bed69fdc9 # Groene achtergrond
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ca2da4d8-f39f-42b3-83c6-1f2a940ae2bc
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: 8efba61a-e17b-4cd6-a7c6-75f561d09521
            userCondition: null
          -
            elementCondition: null
            fieldUid: 0b448480-767d-4080-aaaa-a50f96827cb5 # Key Visual titel
            instructions: null
            label: Titel
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f6877526-3aa3-4a0c-ad65-3bcb60233e5a
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: ni
                  uid: aa20b97a-b2b9-4ffd-9080-14c0292f4a48
                  values:
                    - sliderDoorways
                    - fullWidth
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 8e47a086-8b54-4819-8209-8f12ceab7730 # Key Visual gemarkeerde woorden
            instructions: null
            label: 'Gemarkeerde woorden'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b82fa773-f9e2-4d8c-9b16-b1f0986ac1f3
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: f4dabf90-f743-40f2-a155-bc22e923b3f6 # Key Visual intro
            instructions: null
            label: Intro
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0f44c331-daff-4698-a9b0-66fce55a93cd
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: e8ac1743-d113-4f5b-b682-eedde9d92e71
                  values:
                    - basic
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: db330955-7cf2-4de7-944f-b22efef18a49 # Key Visual uitlijning
            instructions: null
            label: 'Uitlijning titel en intro'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8b22a884-8fdf-41f7-8750-4ba11bd39b29
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 0baedf1f-9842-4062-bb8f-0a110625cf70
                  values:
                    - sliderDoorways
                    - doorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 01048dcf-a21c-405a-9a51-5809ce342e18 # Key Visual doorways
            instructions: null
            label: Doorways
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3ac69033-f07a-4465-98cf-0b9c27ea1160
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 443c5324-0226-4dd4-aa48-cfd7bedcce37
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: e486a935-0b76-4092-8cbd-acc476e2c3b8 # Key visual slider afbeeldingen
            instructions: null
            label: Afbeeldingen
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a11147ad-ec63-4963-93e0-ff86bb995d2f
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 449c3f99-82a2-48a3-85f1-a18d2de8246c
                  values:
                    - media
                    - fullWidth
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 2cd90801-06c8-42e8-a69a-d340bcec40f7 # Key Visual afbeelding
            instructions: null
            label: Afbeelding
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c56dd21d-784b-4854-b2e1-4317329d593c
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 6f290ce9-a554-42d0-89f6-20dc21fc2bc1
                  values:
                    - media
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 4756cf29-331b-4cf9-ba78-d088f585ef10 # Key Visual video
            instructions: null
            label: Video
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 43c751b6-5da9-461b-a5dd-9831d1bc3820
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: d1e1bef5-4f8c-4fe4-b461-ae0b95729177
                  values:
                    - media
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 62f2612f-7ae2-410d-8c97-632092b5780e # Key Visual scrol verder tekst
            instructions: null
            label: 'Scroll verder tekst'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1f477502-de03-4896-8278-5e4634276672
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            heading: 'Call to action button'
            type: craft\fieldlayoutelements\Heading
            uid: a269e2e8-f3a3-43ad-b2be-288180de1581
            userCondition: null
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: ni
                  uid: 0bf76bd6-0fae-4c6f-b0d7-bda9fdc65b68
                  values:
                    - fullWidth
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 900ca6a1-23ae-4ca6-80f4-687c9e8fbfed # Key Visual CTA
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6c5bf276-283e-48ea-9ed4-a2ed2eb9c970
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: ni
                  uid: ebde8399-ab41-45e9-970b-21b060aa4f85
                  values:
                    - fullWidth
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 101f9198-3e34-420c-9907-fde2a2fbe291 # Key Visual CTA no follow
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 65d1d86d-4a4f-4702-8f4d-b5238dde4dea
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: afe7e96f-726d-412e-98cb-3c4619ff7125
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 22fbd7b7-5ea6-4550-b5fc-0c22bbc6bc52 # CTA button type
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 62adfcf2-3b04-48a4-afd6-23cb46f3c20d
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 9e2c523e-8bea-4b04-b74d-d3cfeeee06ad
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: ea55d9e3-f1f5-4d6c-980e-034740c692e3 # CTA tonen op
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 63b96ba5-b24e-4f95-aee0-780a77afcd03
            userCondition: null
            warning: null
            width: 50
        name: 'Key Visual'
        uid: d06638c8-c44b-4f59-83d0-306e763a216a
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 5dfd3367-7807-44b9-ae90-6eec5a841baa # Content blokken
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b311e668-38e4-4e79-8303-d1a5fd1eb52d
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 32ea8f2a-05fb-40c0-9963-cf908f20876a
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: d0c2d555-4020-481c-ad37-f92f6ab83488 # SEO
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: cfbe8679-0564-47c4-b806-8ada5b022bd7
            userCondition: null
            warning: null
            width: 100
        name: SEO
        uid: 520648f4-7362-427a-aa36-784a484017fa
        userCondition: null
handle: home
hasTitleField: true
name: Home
section: 103878b9-17f3-4025-acd2-8e85ee3313dd # Home
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
sortOrder: 1
titleFormat: '{section.name|raw}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
