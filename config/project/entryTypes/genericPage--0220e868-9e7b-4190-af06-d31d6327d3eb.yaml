fieldLayouts:
  41c36af2-33ca-43e1-977e-e8c5aef50356:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            disabled: false
            elementCondition: null
            id: null
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: d27a8e2a-2b4f-4562-93bc-1b853b420519
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: ce40eeab-0638-499f-ba19-ec77f4f2d9a2 # Meenemen in zoekresultaten
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e15afed0-f22f-46f6-b2f3-d174462283d5
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: 96a9cc4d-f27c-4591-b596-9daf1094c431 # Toon bestelknop
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: fea89860-f4c5-4a2e-a2ff-4f908ea9cef2
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: bc86bd8c-84e1-480d-96d3-851abea330d6 # Thumbnail
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8de79fb2-ca79-4659-a742-08e06eb386fc
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: 25deec0e-51c4-42eb-9a6c-3301d7f71cf7 # Footer
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5f45694e-e831-44fc-8fc8-d305bd66691d
            userCondition: null
            warning: null
            width: 50
        name: Content
        uid: e35f0679-86ba-479a-9756-32e72c450647
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: d0c2d555-4020-481c-ad37-f92f6ab83488 # SEO
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d370b0f0-6d0e-4f8b-8bde-ed89ab099597
            userCondition: null
            warning: null
            width: 100
        name: SEO
        uid: f1da0bf9-c27d-4997-8e2c-71732106c35d
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
            instructions: null
            label: 'Key visual type'
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2776be2f-a861-4dc8-b69e-c8ee71b2fc4b
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 912c5988-6030-4f4b-89e2-ae7c50b479c8 # Key Visual Laat content aansluiten
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e2d0dd85-fda0-4c59-b687-1f10a2db650e
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 0298dbc7-b9c4-4d25-8dad-ae4a8b1a4ee5
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: e4a71150-ee41-44a4-8f92-9f5bed69fdc9 # Groene achtergrond
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 803718e2-cb2c-4670-9e83-6597674fc656
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: f4ed56d3-d410-4595-8eea-2858b0d4c2ff
            userCondition: null
          -
            elementCondition: null
            fieldUid: 0b448480-767d-4080-aaaa-a50f96827cb5 # Key Visual titel
            instructions: null
            label: Titel
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b4683806-2a19-41ab-9045-746991c2eed1
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: ni
                  uid: 0e3c2ea9-11be-470d-a50c-34bfed43fbc6
                  values:
                    - sliderDoorways
                    - fullWidth
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 8e47a086-8b54-4819-8209-8f12ceab7730 # Key Visual gemarkeerde woorden
            instructions: 'Vul de woorden in die in groen getoond moeten worden'
            label: 'Gemarkeerde woorden'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 462e2d31-8dca-49b5-8d07-22826fcf8081
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: f4dabf90-f743-40f2-a155-bc22e923b3f6 # Key Visual intro
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5b510c8e-5421-4409-b4ae-6d7efecf8e0c
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 8d7f3c65-ac90-4256-8498-5d66d418f629
                  values:
                    - basic
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: db330955-7cf2-4de7-944f-b22efef18a49 # Key Visual uitlijning
            instructions: null
            label: 'Uitlijning titel en intro'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b18f8716-036b-49f5-8fc8-6a03ee4072b7
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 2298dcb1-2830-463a-9bf8-b038e2222162
                  values:
                    - doorways
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 01048dcf-a21c-405a-9a51-5809ce342e18 # Key Visual doorways
            instructions: null
            label: Doorways
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6d1264a9-a1b2-4f04-90ec-9162bb772f59
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 27d53b54-5cb0-4e2c-8dbc-78020aa8360e
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: e486a935-0b76-4092-8cbd-acc476e2c3b8 # Key visual slider afbeeldingen
            instructions: null
            label: Afbeeldingen
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 01773fe7-33ca-4ea6-a362-8b760b8820ce
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 59e3093d-4b36-4625-826f-7a96c4caf51f
                  values:
                    - media
                    - fullWidth
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 2cd90801-06c8-42e8-a69a-d340bcec40f7 # Key Visual afbeelding
            instructions: null
            label: Afbeelding
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9cf75068-9102-4a67-bcae-ada5ed0e36b7
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 367bff49-816a-4418-8185-4637b863a91b
                  values:
                    - media
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 4756cf29-331b-4cf9-ba78-d088f585ef10 # Key Visual video
            instructions: null
            label: Video
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4503575f-3f2e-450d-8ded-7eae1faf414e
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: ni
                  uid: 20f31316-451e-4da6-b15d-1431a5648802
                  values:
                    - sliderDoorways
                    - fullWidth
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 900ca6a1-23ae-4ca6-80f4-687c9e8fbfed # Key Visual CTA
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: dc86ae37-e00e-4f92-91bd-cea35b469ca1
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: ni
                  uid: a330f152-9281-4df5-b188-5bb3805aff75
                  values:
                    - sliderDoorways
                    - fullWidth
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 101f9198-3e34-420c-9907-fde2a2fbe291 # Key Visual CTA no follow
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8a2498b9-a8a5-4aa8-88de-339576ae94f5
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: 7830626c-c75d-47dd-842c-6e326626ac0b
                  values:
                    - media
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 62f2612f-7ae2-410d-8c97-632092b5780e # Key Visual scrol verder tekst
            instructions: null
            label: 'Scroll verder tekst'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7ebefec6-8387-41b0-aefa-37b710a6e9ad
            userCondition: null
            warning: null
            width: 100
        name: 'Key Visual'
        uid: be6bf734-8b5f-4d4d-ba50-85bff145c76b
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 5dfd3367-7807-44b9-ae90-6eec5a841baa # Content blokken
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ca7e374e-87ab-4008-ba3e-aaa460bbc208
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: f04cdd5a-8ee6-4add-80b9-c81943eee0fe
        userCondition: null
handle: genericPage
hasTitleField: true
name: 'Algemene pagina'
section: 7289e425-5493-40c0-babb-b359ff6da264 # Artikel
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
sortOrder: 2
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
