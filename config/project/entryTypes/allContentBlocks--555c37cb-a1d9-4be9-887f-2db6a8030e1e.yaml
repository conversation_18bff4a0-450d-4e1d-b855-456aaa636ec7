fieldLayouts:
  8d6af205-8bc3-464d-b942-cfa565722614:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            disabled: false
            elementCondition: null
            id: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: ade4faee-9b76-49ad-a7d7-26872bf565c8
            userCondition: null
            warning: null
            width: 100
        name: Algemeen
        uid: 4c6008b7-ed62-467b-bd85-7070cec37c01
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: d0c2d555-4020-481c-ad37-f92f6ab83488 # SEO
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8725720c-4514-48c1-a148-aa615c48c141
            userCondition: null
            warning: null
            width: 100
        name: SEO
        uid: 358311f5-a2d6-4d5a-8296-d3509adaa904
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
            instructions: null
            label: 'Key visual type'
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ed624df9-4629-4db4-88c9-fe6d243f3e94
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 912c5988-6030-4f4b-89e2-ae7c50b479c8 # Key Visual Laat content aansluiten
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3cc7ad2a-84d7-4630-822e-289a99b19ac9
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: b011c906-89a3-4de9-86a8-ae9180a8d111
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: e4a71150-ee41-44a4-8f92-9f5bed69fdc9 # Groene achtergrond
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 929637f6-5646-423a-86c8-9c9d7d81f8e3
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: 33f85456-71fe-4900-a434-08a1c197d242
            userCondition: null
          -
            elementCondition: null
            fieldUid: 0b448480-767d-4080-aaaa-a50f96827cb5 # Key Visual titel
            instructions: null
            label: Titel
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9693ce56-e089-49b4-bc87-0a014c49a181
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: ni
                  uid: b99925e3-5a0e-4c0f-bdaa-bffa851592ac
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 8e47a086-8b54-4819-8209-8f12ceab7730 # Key Visual gemarkeerde woorden
            instructions: 'Vul de woorden in die in groen getoond moeten worden'
            label: 'Gemarkeerde woorden'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 979710d0-38e3-435b-9e3c-82db5fbcde1a
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: f4dabf90-f743-40f2-a155-bc22e923b3f6 # Key Visual intro
            instructions: null
            label: Intro
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 315bba82-2d1d-49bd-90e9-3e679532247d
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: e5869d18-9bba-4ee4-8b48-6868764f6d40
                  values:
                    - basic
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: db330955-7cf2-4de7-944f-b22efef18a49 # Key Visual uitlijning
            instructions: null
            label: 'Uitlijning titel en intro'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 33f7c810-06c6-49a4-8e5c-976469c7af18
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: f4be6c92-1f6f-455d-b875-6a094ca309f5
                  values:
                    - doorways
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 01048dcf-a21c-405a-9a51-5809ce342e18 # Key Visual doorways
            instructions: null
            label: Doorways
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e630b69f-eb24-4ee6-94fe-e99288da4c32
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: e260ebf3-73f8-4cf2-973a-163c57b5b646
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: e486a935-0b76-4092-8cbd-acc476e2c3b8 # Key visual slider afbeeldingen
            instructions: null
            label: Afbeeldingen
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bbe23b38-e420-42f3-abdc-13f5dbb91041
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: b1de9953-7984-4cf8-bf8b-c6896e225ebc
                  values:
                    - media
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 2cd90801-06c8-42e8-a69a-d340bcec40f7 # Key Visual afbeelding
            instructions: null
            label: Afbeelding
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f91e1f5f-9920-4633-bd00-ef7646bf09cc
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: c197b28e-3d6b-4f2b-aa82-2e1e054363fd
                  values:
                    - media
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 4756cf29-331b-4cf9-ba78-d088f585ef10 # Key Visual video
            instructions: null
            label: Video
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 515375b4-8541-460c-9c57-2acacccf0a79
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: ni
                  uid: dd4f1885-b1e2-44a3-a324-9bc56d5c4713
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 900ca6a1-23ae-4ca6-80f4-687c9e8fbfed # Key Visual CTA
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 04ad229d-c176-48cb-94fc-6828e08079e6
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: ni
                  uid: 4dd61d7d-1811-41df-8274-aa61a38454fb
                  values:
                    - sliderDoorways
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 101f9198-3e34-420c-9907-fde2a2fbe291 # Key Visual CTA no follow
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: dbf36a2e-c824-48fb-9bf2-40caf9b8ae2e
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 2d2d6e08-4b6e-4fb3-9814-4335666b4840 # Key Visual type
                  operator: in
                  uid: b6eca2a8-0a91-4ee6-a795-e7607b8eebcf
                  values:
                    - media
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 62f2612f-7ae2-410d-8c97-632092b5780e # Key Visual scrol verder tekst
            instructions: null
            label: 'Scroll verder tekst'
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f3e8b227-afa3-47e7-9fbe-f6bbbc2add81
            userCondition: null
            warning: null
            width: 100
        name: 'Key Visual'
        uid: b0508cc4-1372-404f-b734-bd38b9a0432d
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 5dfd3367-7807-44b9-ae90-6eec5a841baa # Content blokken
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 640e433f-c7f0-4f91-9d42-ade0d333a1de
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 2f918114-5229-4c0f-9daa-764e51d538ba
        userCondition: null
handle: allContentBlocks
hasTitleField: true
name: 'All content blocks'
section: aa1aacf4-0f6a-4889-90b0-65dce89f0bfc # All content blocks
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
sortOrder: 1
titleFormat: '{section.name|raw}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
