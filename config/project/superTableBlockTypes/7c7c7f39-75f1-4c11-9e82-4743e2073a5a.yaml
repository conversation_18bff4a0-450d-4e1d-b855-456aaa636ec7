changedFieldIndicator: 562604100
field: cc392ff7-07a0-46f6-b177-3ed48a1f52f3 # Visualisatie stap 6
fieldLayouts:
  cb68e753-3421-456f-be0b-7c87df62643a:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 666c6f31-aa6e-4fc8-9c90-6f67e40884bb # Titel
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 408d8f8e-3ed5-477e-8f51-67fc0c82d3d4
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: dd0b1c5d-86eb-40c9-a328-821b4a16b3dc # Tekst
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 10739525-5126-457b-b957-f5490e4f2dd9
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: bd1d1ccd-7ad4-43a9-ba2c-16902e60bf56 # Button
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d3ef1ef6-5464-44a5-abd9-d060b525e787
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 638bf72d-79c5-410c-be6f-30b817ae3b46 # Button no follow
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: dd9c8d02-9892-4028-9acd-cf0ca7600c33
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 4a8f627f-c44a-4bfd-8b6a-727fdabb3937
        userCondition: null
fields:
  638bf72d-79c5-410c-be6f-30b817ae3b46: # Button no follow
    columnSuffix: zyrtwsdp
    contentColumnType: boolean
    fieldGroup: null
    handle: stepButtonNoFollow
    instructions: 'Dit zorgt ervoor dat Google deze link niet scant met als voordeel dat er geen gigantische SEO boom ontstaat.'
    name: 'Button no follow'
    searchable: false
    settings:
      default: false
      offLabel: Follow
      onLabel: 'No follow'
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\Lightswitch
  666c6f31-aa6e-4fc8-9c90-6f67e40884bb: # Titel
    columnSuffix: sansngni
    contentColumnType: string(400)
    fieldGroup: null
    handle: stepTitle
    instructions: null
    name: Titel
    searchable: false
    settings:
      byteLimit: null
      charLimit: 100
      code: false
      columnType: null
      initialRows: 4
      multiline: false
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\PlainText
  bd1d1ccd-7ad4-43a9-ba2c-16902e60bf56: # Button
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: stepButton
    instructions: null
    name: Button
    searchable: false
    settings:
      allowCustomText: true
      allowTarget: true
      autoNoReferrer: false
      customTextMaxLength: 0
      customTextRequired: false
      defaultLinkName: asset
      defaultText: ''
      enableAllLinkTypes: false
      enableAriaLabel: false
      enableElementCache: false
      enableTitle: false
      typeSettings:
        asset:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources:
            - 'volume:174c5e31-a28e-4d3d-8991-630c60533e30' # Whitepapers
        category:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        custom:
          allowAliases: false
          disableValidation: false
          enabled: false
        email:
          allowAliases: false
          disableValidation: false
          enabled: true
        entry:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources:
            - singles
            - 'section:7289e425-5493-40c0-babb-b359ff6da264' # Artikel
            - 'section:5f7deaeb-defc-4bf8-9ed1-a4ef981d5646' # Pagina's
        site:
          enabled: false
          sites: '*'
        tel:
          allowAliases: false
          disableValidation: false
          enabled: true
        url:
          allowAliases: false
          disableValidation: false
          enabled: true
        user:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
    translationKeyFormat: null
    translationMethod: none
    type: lenz\linkfield\fields\LinkField
  dd0b1c5d-86eb-40c9-a328-821b4a16b3dc: # Tekst
    columnSuffix: xskboyux
    contentColumnType: string(800)
    fieldGroup: null
    handle: stepText
    instructions: null
    name: Tekst
    searchable: false
    settings:
      byteLimit: null
      charLimit: 200
      code: false
      columnType: null
      initialRows: 4
      multiline: false
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\PlainText
