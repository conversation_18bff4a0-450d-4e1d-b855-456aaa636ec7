field: d159e37c-8060-4e7c-8832-7e63ed50871c # <PERSON>er footer kolom
fieldLayouts:
  1a01c7f5-18a2-43f2-b24a-************:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: b7a25836-df14-4512-828e-b384a0a66d94 # Menu item
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 31ab37a5-a66f-4602-9940-c44b9b5c04a6
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: af8c0de1-8e2c-4883-8bc9-dd5721097f64
        userCondition: null
fields:
  b7a25836-df14-4512-828e-b384a0a66d94: # Menu item
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: footerMenuLink
    instructions: null
    name: 'Menu item'
    searchable: false
    settings:
      allowCustomText: true
      allowTarget: true
      autoNoReferrer: true
      customTextMaxLength: 0
      customTextRequired: false
      defaultLinkName: entry
      defaultText: ''
      enableAllLinkTypes: false
      enableAriaLabel: false
      enableElementCache: false
      enableTitle: false
      typeSettings:
        asset:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        category:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        custom:
          allowAliases: false
          disableValidation: false
          enabled: true
        email:
          allowAliases: false
          disableValidation: false
          enabled: false
        entry:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources: '*'
        site:
          enabled: true
          sites: '*'
        tel:
          allowAliases: false
          disableValidation: false
          enabled: false
        url:
          allowAliases: false
          disableValidation: false
          enabled: true
        user:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
    translationKeyFormat: null
    translationMethod: none
    type: lenz\linkfield\fields\LinkField
