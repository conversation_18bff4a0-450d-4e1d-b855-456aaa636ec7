changedFieldIndicator: 1339362288
field: 5614060f-a772-4cf2-89f8-ef25f20d7b49 # Visualisatie stap 4
fieldLayouts:
  6fe2f469-56d9-4848-898f-a0b36aa6b41b:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: ffdb0816-0a8e-48a7-9e54-1ef75ba25dc8 # Titel
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e7fd1ece-c77a-45b3-adbf-5219fd3928ee
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: b6821857-5eb6-4872-b528-40d930b03ad2 # Tekst
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 481fd234-bb57-4635-8bbc-ff254de7f6dc
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 073e9e4a-4aef-46b7-8c07-7cd0ae39468c # Button
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e7730aa9-ba87-4d71-81a4-b39a8028f3a1
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: d006e688-d967-49e5-be52-b801cdc7d5aa # Button no follow
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3ca95434-ef0c-4512-96ce-fbc48d00d008
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: d11cd7b5-ef5d-499d-a2b1-7bf605f7903a
        userCondition: null
fields:
  073e9e4a-4aef-46b7-8c07-7cd0ae39468c: # Button
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: stepButton
    instructions: null
    name: Button
    searchable: false
    settings:
      allowCustomText: true
      allowTarget: true
      autoNoReferrer: false
      customTextMaxLength: 0
      customTextRequired: false
      defaultLinkName: asset
      defaultText: ''
      enableAllLinkTypes: false
      enableAriaLabel: false
      enableElementCache: false
      enableTitle: false
      typeSettings:
        asset:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources:
            - 'volume:174c5e31-a28e-4d3d-8991-630c60533e30' # Whitepapers
        category:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        custom:
          allowAliases: false
          disableValidation: false
          enabled: false
        email:
          allowAliases: false
          disableValidation: false
          enabled: true
        entry:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources:
            - singles
            - 'section:7289e425-5493-40c0-babb-b359ff6da264' # Artikel
            - 'section:5f7deaeb-defc-4bf8-9ed1-a4ef981d5646' # Pagina's
        site:
          enabled: false
          sites: '*'
        tel:
          allowAliases: false
          disableValidation: false
          enabled: true
        url:
          allowAliases: false
          disableValidation: false
          enabled: true
        user:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
    translationKeyFormat: null
    translationMethod: none
    type: lenz\linkfield\fields\LinkField
  b6821857-5eb6-4872-b528-40d930b03ad2: # Tekst
    columnSuffix: xzobjtzf
    contentColumnType: string(800)
    fieldGroup: null
    handle: stepText
    instructions: null
    name: Tekst
    searchable: false
    settings:
      byteLimit: null
      charLimit: 200
      code: false
      columnType: null
      initialRows: 4
      multiline: false
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\PlainText
  d006e688-d967-49e5-be52-b801cdc7d5aa: # Button no follow
    columnSuffix: rxipqxva
    contentColumnType: boolean
    fieldGroup: null
    handle: stepButtonNoFollow
    instructions: 'Dit zorgt ervoor dat Google deze link niet scant met als voordeel dat er geen gigantische SEO boom ontstaat.'
    name: 'Button no follow'
    searchable: false
    settings:
      default: false
      offLabel: Follow
      onLabel: 'No follow'
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\Lightswitch
  ffdb0816-0a8e-48a7-9e54-1ef75ba25dc8: # Titel
    columnSuffix: hbjnwohm
    contentColumnType: string(400)
    fieldGroup: null
    handle: stepTitle
    instructions: null
    name: Titel
    searchable: false
    settings:
      byteLimit: null
      charLimit: 100
      code: false
      columnType: null
      initialRows: 4
      multiline: false
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\PlainText
