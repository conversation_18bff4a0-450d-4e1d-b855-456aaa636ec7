field: 7edba629-d113-4842-9e62-75f44edcb579
fieldLayouts:
  169b14d1-965e-4142-bce4-1bdfc8a05c53:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: f97cf8a8-30bd-4fbb-8db9-c544a4d0506a # Artikel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5cbd225f-d6c6-473a-a45e-981135aa906f
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: b169b538-bc69-41a3-8790-d490ee4f6aa3
        userCondition: null
fields:
  f97cf8a8-30bd-4fbb-8db9-c544a4d0506a: # Artikel
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: article
    instructions: null
    name: Artikel
    searchable: false
    settings:
      allowSelfRelations: false
      localizeRelations: false
      maxRelations: 1
      minRelations: null
      selectionCondition:
        __assoc__:
          -
            - elementType
            - craft\elements\Entry
          -
            - fieldContext
            - global
          -
            - class
            - craft\elements\conditions\entries\EntryCondition
      selectionLabel: 'Selecteer artikel'
      showSiteMenu: true
      source: null
      sources:
        - 'section:7289e425-5493-40c0-babb-b359ff6da264' # Artikel
      targetSiteId: null
      validateRelatedElements: false
      viewMode: null
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\Entries
