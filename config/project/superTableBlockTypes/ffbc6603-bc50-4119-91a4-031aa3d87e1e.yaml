changedFieldIndicator: 162146834
field: 01048dcf-a21c-405a-9a51-5809ce342e18 # Key Visual doorways
fieldLayouts:
  26c3bc74-07af-4c0c-a2ce-b11978a14455:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: f9d2d0bb-6329-4249-9de3-2e3b2a2824d2 # Icoon
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 48729ede-b1f5-46c2-ab85-b7ab865ec2a7
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 7eb4b8fc-2b2e-44cd-8dd5-f3f014086e2a # Titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8979414c-2570-412d-9f10-5bf810ea8f34
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 102eb153-1af2-4eb1-b4d5-45203369113c # Intro
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 774ce829-b3ee-47d5-984b-7933796e550a
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: ca63f1db-38c3-4b3f-87ef-e6476da447a3 # Link
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3a5fa743-4d28-488e-92ec-8b3be481acd0
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: a9321c80-8217-4414-92f4-936ee667071b # No follow
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1a331741-b25a-4be3-9b65-db619cb72271
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 581a5fb4-f97f-4a31-8eec-a926d118602d # Hover kleur
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9c09b1d4-ab9b-4a93-8fd5-4b1509ea63ca
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: e469a6f6-4990-4403-ab7b-911b8836e61c
        userCondition: null
fields:
  7eb4b8fc-2b2e-44cd-8dd5-f3f014086e2a: # Titel
    columnSuffix: null
    contentColumnType: text
    fieldGroup: null
    handle: doorwayTitle
    instructions: null
    name: Titel
    searchable: true
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: null
      initialRows: 4
      multiline: false
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\PlainText
  102eb153-1af2-4eb1-b4d5-45203369113c: # Intro
    columnSuffix: null
    contentColumnType: text
    fieldGroup: null
    handle: doorwayIntro
    instructions: null
    name: Intro
    searchable: true
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: null
      initialRows: 4
      multiline: false
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\PlainText
  581a5fb4-f97f-4a31-8eec-a926d118602d: # Hover kleur
    columnSuffix: gxhxnbwd
    contentColumnType: string(9)
    fieldGroup: null
    handle: hoverColor
    instructions: 'Deze hover kleur is alleen van toepassing bij doorways op home'
    name: 'Hover kleur'
    searchable: false
    settings:
      columnType: null
      options:
        -
          __assoc__:
            -
              - label
              - Groen
            -
              - value
              - primary
            -
              - default
              - '1'
        -
          __assoc__:
            -
              - label
              - Blauw
            -
              - value
              - secondary
            -
              - default
              - ''
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\Dropdown
  a9321c80-8217-4414-92f4-936ee667071b: # No follow
    columnSuffix: ujqewhno
    contentColumnType: boolean
    fieldGroup: null
    handle: doorwayNoFollow
    instructions: 'Dit zorgt ervoor dat Google deze link niet scant met als voordeel dat er geen gigantische SEO boom ontstaat. '
    name: 'No follow'
    searchable: false
    settings:
      default: false
      offLabel: Follow
      onLabel: 'No follow'
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\Lightswitch
  ca63f1db-38c3-4b3f-87ef-e6476da447a3: # Link
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: doorwayLink
    instructions: null
    name: Link
    searchable: true
    settings:
      allowCustomText: false
      allowTarget: true
      autoNoReferrer: false
      customTextMaxLength: 0
      customTextRequired: false
      defaultLinkName: entry
      defaultText: ''
      enableAllLinkTypes: false
      enableAriaLabel: false
      enableElementCache: false
      enableTitle: false
      typeSettings:
        asset:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        category:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        custom:
          allowAliases: false
          disableValidation: false
          enabled: false
        email:
          allowAliases: false
          disableValidation: false
          enabled: false
        entry:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources: '*'
        site:
          enabled: false
          sites: '*'
        tel:
          allowAliases: false
          disableValidation: false
          enabled: false
        url:
          allowAliases: false
          disableValidation: false
          enabled: true
        user:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
    translationKeyFormat: null
    translationMethod: none
    type: lenz\linkfield\fields\LinkField
  f9d2d0bb-6329-4249-9de3-2e3b2a2824d2: # Icoon
    columnSuffix: svimupmg
    contentColumnType: string
    fieldGroup: null
    handle: icon
    instructions: null
    name: Icoon
    searchable: false
    settings:
      columnType: text
      iconSets: '*'
      renderId: null
      showLabels: false
    translationKeyFormat: null
    translationMethod: none
    type: verbb\iconpicker\fields\IconPickerField
