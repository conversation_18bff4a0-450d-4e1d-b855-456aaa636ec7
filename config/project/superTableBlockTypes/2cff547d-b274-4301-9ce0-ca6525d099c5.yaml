field: 974569d5-40c7-4f4c-a6c9-429df712e726 # <PERSON><PERSON><PERSON> footer kolom
fieldLayouts:
  6c6e4640-92ff-41fc-9808-359b437da4fd:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 7778faae-b43a-4460-b484-4a9a0a53e714 # Menu item
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e56d2165-c0a6-4210-9696-7080dd5c2a1a
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: a0c112c8-1f5a-421f-8eb1-c82edc618bd4
        userCondition: null
fields:
  7778faae-b43a-4460-b484-4a9a0a53e714: # Menu item
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: footerMenuLink
    instructions: null
    name: 'Menu item'
    searchable: false
    settings:
      allowCustomText: true
      allowTarget: true
      autoNoReferrer: true
      customTextMaxLength: 0
      customTextRequired: false
      defaultLinkName: asset
      defaultText: ''
      enableAllLinkTypes: false
      enableAriaLabel: false
      enableElementCache: false
      enableTitle: false
      typeSettings:
        asset:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        category:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources: '*'
        custom:
          allowAliases: false
          disableValidation: false
          enabled: true
        email:
          allowAliases: false
          disableValidation: false
          enabled: false
        entry:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources: '*'
        site:
          enabled: true
          sites: '*'
        tel:
          allowAliases: false
          disableValidation: false
          enabled: false
        url:
          allowAliases: false
          disableValidation: false
          enabled: true
        user:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
    translationKeyFormat: null
    translationMethod: none
    type: lenz\linkfield\fields\LinkField
