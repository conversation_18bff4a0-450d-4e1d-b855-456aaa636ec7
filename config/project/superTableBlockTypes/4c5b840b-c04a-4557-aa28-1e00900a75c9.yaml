changedFieldIndicator: 1143601723
field: 260cb4a3-9d13-4d6a-89fc-f30c790a227e # Doorway items
fieldLayouts:
  9bd9c0eb-b5d6-4ae2-a0c3-ca4d43608f89:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: c8cb14ab-2289-47cd-b43d-43b5d3d3f362 # Doorway item
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0ee28a5b-bac5-41bc-a874-eae042fcf580
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 544638a8-30eb-475e-acad-a345a441559f # Doorway no follow
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0c3c22f5-8a5d-423e-88fc-59c092d9f905
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 160e6e8a-9c68-4ebf-8577-235b53c439d1
        userCondition: null
fields:
  544638a8-30eb-475e-acad-a345a441559f: # Doorway no follow
    columnSuffix: tjwdsiuz
    contentColumnType: boolean
    fieldGroup: null
    handle: linkNoFollow
    instructions: null
    name: 'Doorway no follow'
    searchable: false
    settings:
      default: false
      offLabel: Follow
      onLabel: 'No follow'
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\Lightswitch
  c8cb14ab-2289-47cd-b43d-43b5d3d3f362: # Doorway item
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: doorwayItem
    instructions: null
    name: 'Doorway item'
    searchable: false
    settings:
      allowSelfRelations: false
      branchLimit: null
      localizeRelations: false
      maintainHierarchy: false
      maxRelations: 1
      minRelations: null
      selectionCondition:
        __assoc__:
          -
            - elementType
            - craft\elements\Entry
          -
            - fieldContext
            - global
          -
            - class
            - craft\elements\conditions\entries\EntryCondition
      selectionLabel: null
      showSiteMenu: true
      sources:
        - 'section:7289e425-5493-40c0-babb-b359ff6da264' # Artikel
        - 'section:5f7deaeb-defc-4bf8-9ed1-a4ef981d5646' # Pagina's
      targetSiteId: null
      validateRelatedElements: false
      viewMode: null
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\Entries
