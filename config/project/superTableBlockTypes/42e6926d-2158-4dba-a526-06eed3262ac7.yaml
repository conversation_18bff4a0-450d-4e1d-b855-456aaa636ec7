changedFieldIndicator: 346024840
field: 47e22b40-f8f0-4220-8036-f63187242758 # Link
fieldLayouts:
  abc1d917-36b7-430f-b4a8-8a97b88cf00f:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: d623c575-a80a-4d79-9940-69a16487ce7d # Icoon
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5a95d35f-c83f-486d-8eb7-e5de5689f1a5
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 4721148a-86f0-466b-b384-6851b3004de3 # Link
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 45bfb911-f710-480f-bbb5-919bdd753111
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 4d9740ba-6072-4d33-8204-0ade86e30eb9
        userCondition: null
fields:
  4721148a-86f0-466b-b384-6851b3004de3: # Link
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: page
    instructions: null
    name: Link
    searchable: false
    settings:
      allowCustomText: true
      allowTarget: true
      autoNoReferrer: true
      customTextMaxLength: 0
      customTextRequired: false
      defaultLinkName: entry
      defaultText: ''
      enableAllLinkTypes: false
      enableAriaLabel: false
      enableElementCache: false
      enableTitle: false
      typeSettings:
        asset:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources: '*'
        category:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        custom:
          allowAliases: false
          disableValidation: false
          enabled: false
        email:
          allowAliases: false
          disableValidation: false
          enabled: true
        entry:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources: '*'
        site:
          enabled: false
          sites: '*'
        tel:
          allowAliases: false
          disableValidation: false
          enabled: true
        url:
          allowAliases: false
          disableValidation: false
          enabled: true
        user:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
    translationKeyFormat: null
    translationMethod: none
    type: lenz\linkfield\fields\LinkField
  d623c575-a80a-4d79-9940-69a16487ce7d: # Icoon
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: icon
    instructions: null
    name: Icoon
    searchable: false
    settings:
      columnType: text
      iconSets: '*'
      renderId: null
      showLabels: true
    translationKeyFormat: null
    translationMethod: none
    type: verbb\iconpicker\fields\IconPickerField
