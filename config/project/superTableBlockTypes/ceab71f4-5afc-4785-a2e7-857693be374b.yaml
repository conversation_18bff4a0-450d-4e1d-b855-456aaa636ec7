changedFieldIndicator: 78486404
field: af2aeaa6-be90-4abc-bc6c-b7fcf0d490b5 # USP Item
fieldLayouts:
  b63dd3f6-07e1-454a-a5e9-f351c0db2ad1:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 278ca94e-1937-4042-8ec2-67f94d8e87f6 # Icoon
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5c43287e-6e9e-4b02-8d2e-c25715333b3f
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: d5b21de5-6f60-42ea-bed6-42adc07537ca # Titel
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 13d80d79-c9fa-4544-b087-f93e8c410306
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 57926a28-1c3f-4269-87b4-f90e363e508b # Tekst
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6aa3559d-07b4-4acc-a275-603da93bcede
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: a921af88-cc01-4e25-8988-1bdca91e283d
        userCondition: null
fields:
  278ca94e-1937-4042-8ec2-67f94d8e87f6: # Icoon
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: uspItemIcon
    instructions: null
    name: Icoon
    searchable: false
    settings:
      columnType: text
      iconSets: '*'
      renderId: null
      showLabels: true
    translationKeyFormat: null
    translationMethod: none
    type: verbb\iconpicker\fields\IconPickerField
  57926a28-1c3f-4269-87b4-f90e363e508b: # Tekst
    columnSuffix: null
    contentColumnType: text
    fieldGroup: null
    handle: uspItemText
    instructions: null
    name: Tekst
    searchable: true
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: null
      initialRows: 4
      multiline: false
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\PlainText
  d5b21de5-6f60-42ea-bed6-42adc07537ca: # Titel
    columnSuffix: null
    contentColumnType: text
    fieldGroup: null
    handle: uspItemTitle
    instructions: null
    name: Titel
    searchable: true
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: null
      initialRows: 4
      multiline: false
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\PlainText
