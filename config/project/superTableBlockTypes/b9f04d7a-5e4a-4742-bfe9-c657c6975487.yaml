changedFieldIndicator: 1000263304
field: 9c952ae8-c04c-478d-a34e-8fa347e5d1ba # Slider
fieldLayouts:
  843e76e7-e4d8-4925-83cb-385ebd1fb920:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 189b6898-3f88-432d-aea9-695954c2f159 # Artikel slide
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: df54e6ed-f270-4340-bae6-651a05244134
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 4bdc3305-6231-402b-aa31-d5f27d706374 # Artikel no follow
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a59b94c8-441b-4413-af0c-e97d275a0579
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 053b8903-7bb6-4928-a8e6-567f5f66f9f2
        userCondition: null
fields:
  4bdc3305-6231-402b-aa31-d5f27d706374: # Artikel no follow
    columnSuffix: elhmacmj
    contentColumnType: boolean
    fieldGroup: null
    handle: linkNoFollow
    instructions: null
    name: 'Artikel no follow'
    searchable: false
    settings:
      default: false
      offLabel: Follow
      onLabel: 'No follow'
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\Lightswitch
  189b6898-3f88-432d-aea9-695954c2f159: # Artikel slide
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: articleSlide
    instructions: null
    name: 'Artikel slide'
    searchable: false
    settings:
      allowSelfRelations: false
      localizeRelations: false
      maxRelations: 1
      minRelations: null
      selectionCondition:
        __assoc__:
          -
            - elementType
            - craft\elements\Entry
          -
            - fieldContext
            - global
          -
            - class
            - craft\elements\conditions\entries\EntryCondition
      selectionLabel: null
      showSiteMenu: true
      source: null
      sources:
        - 'section:7289e425-5493-40c0-babb-b359ff6da264' # Artikel
      targetSiteId: null
      validateRelatedElements: false
      viewMode: null
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\Entries
