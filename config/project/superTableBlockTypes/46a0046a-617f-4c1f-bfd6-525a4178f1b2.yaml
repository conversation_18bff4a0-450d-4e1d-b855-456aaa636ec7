changedFieldIndicator: 990295791
field: 4a9e2201-55ef-4cfd-8111-a17dcddaa1c5 # FAQ quick links
fieldLayouts:
  8c17a84e-27d1-4747-b3e3-ca0b00e04632:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 4a9238b7-9f03-47cd-b91a-1a9daaad2ebd # Titel
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bcaeb9f1-e51b-410a-a230-681c7347e958
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 0ac595b8-adf0-4d16-a137-b37b9b8e4ad3 # Target
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6f79a8b3-8d92-42e1-846f-a491ca8df470
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 74b41946-5f3e-4c6e-9a02-441421c09bd6 # Icoon
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 528f6474-72eb-4f54-9503-a746e8b3154c
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: c2e118cf-3bfb-4060-822b-f5a20bac7f86
        userCondition: null
fields:
  0ac595b8-adf0-4d16-a137-b37b9b8e4ad3: # Target
    columnSuffix: yhktfpjy
    contentColumnType: text
    fieldGroup: null
    handle: target
    instructions: null
    name: Target
    searchable: false
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: null
      initialRows: 4
      multiline: false
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\PlainText
  4a9238b7-9f03-47cd-b91a-1a9daaad2ebd: # Titel
    columnSuffix: pdsfojad
    contentColumnType: text
    fieldGroup: null
    handle: label
    instructions: null
    name: Titel
    searchable: false
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: null
      initialRows: 4
      multiline: false
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\PlainText
  74b41946-5f3e-4c6e-9a02-441421c09bd6: # Icoon
    columnSuffix: gainechm
    contentColumnType: string
    fieldGroup: null
    handle: icon
    instructions: null
    name: Icoon
    searchable: false
    settings:
      columnType: text
      iconSets: '*'
      renderId: null
      showLabels: true
    translationKeyFormat: null
    translationMethod: none
    type: verbb\iconpicker\fields\IconPickerField
