changedFieldIndicator: 1523398417
field: de256a2e-ec43-4e3b-88c8-cf867985d9c2 # Buttons
fieldLayouts:
  2649f6c3-3713-4a71-9618-820b984a38b1:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 319eebfb-1773-436d-bcab-40a94ece1347 # Button stijl
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a0e9564c-61e0-4f42-8749-d04a63baec7a
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: b9e73469-2251-4299-8ce1-24efd473322d # Button link
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: aedba9b0-5085-4773-bb9c-f6c08a3d23f8
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: c041eb0a-33e5-471a-ac35-de925c8da78f # Button no follow
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: cc12236a-84c3-4e3b-8763-32710d670124
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: ad5009e9-d1cb-4d59-8101-e898406bfadb
        userCondition: null
fields:
  319eebfb-1773-436d-bcab-40a94ece1347: # Button stijl
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: buttonType
    instructions: null
    name: 'Button stijl'
    searchable: false
    settings:
      options:
        -
          __assoc__:
            -
              - label
              - Groen
            -
              - value
              - primary
            -
              - default
              - '1'
        -
          __assoc__:
            -
              - label
              - Blauw
            -
              - value
              - secondary
            -
              - default
              - ''
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\Dropdown
  b9e73469-2251-4299-8ce1-24efd473322d: # Button link
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: buttonLink
    instructions: null
    name: 'Button link'
    searchable: true
    settings:
      allowCustomText: true
      allowTarget: true
      autoNoReferrer: true
      customTextMaxLength: 0
      customTextRequired: false
      defaultLinkName: entry
      defaultText: ''
      enableAllLinkTypes: false
      enableAriaLabel: false
      enableElementCache: false
      enableTitle: false
      typeSettings:
        asset:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources: '*'
        category:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        custom:
          allowAliases: false
          disableValidation: false
          enabled: false
        email:
          allowAliases: false
          disableValidation: false
          enabled: true
        entry:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources: '*'
        site:
          enabled: false
          sites: '*'
        tel:
          allowAliases: false
          disableValidation: false
          enabled: true
        url:
          allowAliases: false
          disableValidation: false
          enabled: true
        user:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
    translationKeyFormat: null
    translationMethod: none
    type: lenz\linkfield\fields\LinkField
  c041eb0a-33e5-471a-ac35-de925c8da78f: # Button no follow
    columnSuffix: gvqeivqm
    contentColumnType: boolean
    fieldGroup: null
    handle: linkNoFollow
    instructions: 'Dit zorgt ervoor dat Google deze link niet scant met als voordeel dat er geen gigantische SEO boom ontstaat.'
    name: 'Button no follow'
    searchable: false
    settings:
      default: false
      offLabel: Follow
      onLabel: 'No follow'
    translationKeyFormat: null
    translationMethod: none
    type: craft\fields\Lightswitch
