data:
  dataRetention: forever
  dataRetentionValue: ''
  fileUploadsAction: retain
  notifications:
    -
      attachAssets: null
      attachFiles: true
      attachPdf: ''
      bcc: null
      cc: null
      conditions: null
      content: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Email content"}]}]'
      enableConditions: false
      enabled: true
      formId: null
      from: <EMAIL>
      fromName: 'Milieu Service Nederland'
      id: stenciltZlC7jsR6WkEVBKG
      name: 'Archief vernietiging'
      pdfTemplateId: null
      recipients: email
      replyTo: null
      replyToName: null
      sender: null
      subject: 'Archief vernietigingsaanvraag via website ({field.bedrijfsnaam})'
      templateId: 1
      to: '<EMAIL>,{field.emailAdres}'
      toConditions: null
      uid: null
  pages:
    -
      id: new650773305
      label: 'Page 1'
      notificationFlag: true
      rows:
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: contactgegevens
              hasLabel: false
              id: new1549119888
              isNested: false
              label: Contactgegevens
              settings:
                handle: contactgegevens
                headingSize: h5
                instructionsPosition: ''
                label: Contactgegevens
                labelPosition: ''
              type: verbb\formie\fields\formfields\Heading
              vid: new6422-7525
          id: new8353-2383
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: uuidinput
              hasLabel: true
              id: new1285803729
              isNested: false
              label: uuidInput
              settings:
                columnType: ''
                defaultOption: custom
                defaultValue: '{submissionId}'
                handle: uuidinput
                includeInEmail: true
                instructionsPosition: ''
                label: uuidInput
                labelPosition: verbb\formie\positions\Hidden
              type: verbb\formie\fields\formfields\Hidden
              vid: new7801-7630
          id: new5671-1509
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: naam
              hasLabel: true
              id: new1277426361
              isNested: false
              label: Naam
              settings:
                firstNameCollapsed: true
                firstNameDefaultValue: ''
                firstNameEnabled: true
                firstNameLabel: 'First Name'
                firstNamePrePopulate: ''
                handle: naam
                instructionsPosition: verbb\formie\positions\AboveInput
                label: Naam
                labelPosition: ''
                lastNameCollapsed: true
                lastNameDefaultValue: ''
                lastNameEnabled: true
                lastNameLabel: 'Last Name'
                lastNamePrePopulate: ''
                middleNameCollapsed: true
                middleNameDefaultValue: ''
                middleNameEnabled: false
                middleNameLabel: 'Middle Name'
                middleNamePrePopulate: ''
                prefixCollapsed: true
                prefixDefaultValue: ''
                prefixEnabled: false
                prefixLabel: Prefix
                prefixPrePopulate: ''
                required: true
                visibility: ''
              type: verbb\formie\fields\formfields\Name
              vid: new3425-9457
          id: new6368-9930
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: bedrijfsnaam
              hasLabel: true
              id: new1186245770
              isNested: false
              label: Bedrijfsnaam
              settings:
                firstNameCollapsed: true
                firstNameDefaultValue: ''
                firstNameEnabled: true
                firstNameLabel: 'First Name'
                firstNamePrePopulate: ''
                handle: bedrijfsnaam
                instructionsPosition: verbb\formie\positions\AboveInput
                label: Bedrijfsnaam
                labelPosition: ''
                lastNameCollapsed: true
                lastNameDefaultValue: ''
                lastNameEnabled: true
                lastNameLabel: 'Last Name'
                lastNamePrePopulate: ''
                middleNameCollapsed: true
                middleNameDefaultValue: ''
                middleNameEnabled: false
                middleNameLabel: 'Middle Name'
                middleNamePrePopulate: ''
                prefixCollapsed: true
                prefixDefaultValue: ''
                prefixEnabled: false
                prefixLabel: Prefix
                prefixPrePopulate: ''
                required: true
                visibility: ''
              type: verbb\formie\fields\formfields\Name
              vid: new2428-2296
            -
              brandNewField: false
              columnWidth: 6
              handle: kvkNummer
              hasLabel: true
              id: new715197516
              isNested: false
              label: 'KVK nummer'
              settings:
                handle: kvkNummer
                instructionsPosition: ''
                label: 'KVK nummer'
                labelPosition: ''
                maxType: characters
                minType: characters
                visibility: ''
              type: verbb\formie\fields\formfields\SingleLineText
              vid: new1570-5116
          id: new2160-4736
        -
          fields:
            -
              brandNewField: false
              columnWidth: 6
              handle: telefoonnummer
              hasLabel: true
              id: new994978178
              isNested: false
              label: Telefoonnummer
              settings:
                handle: telefoonnummer
                instructionsPosition: ''
                label: Telefoonnummer
                labelPosition: ''
                maxType: characters
                minType: characters
                required: true
                visibility: ''
              type: verbb\formie\fields\formfields\SingleLineText
              vid: new1904-9180
            -
              brandNewField: false
              columnWidth: 6
              handle: emailAdres
              hasLabel: true
              id: new1969588416
              isNested: false
              label: 'Email adres'
              settings:
                handle: emailAdres
                instructionsPosition: ''
                label: 'Email adres'
                labelPosition: ''
                required: true
                visibility: ''
              type: verbb\formie\fields\formfields\Email
              vid: new5156-4972
          id: new6681-5689
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: ledegingsgegevens
              hasLabel: false
              id: new547105068
              isNested: false
              label: Ledigingsgegevens
              settings:
                handle: ledegingsgegevens
                headingSize: h5
                instructionsPosition: ''
                label: Ledigingsgegevens
                labelPosition: ''
              type: verbb\formie\fields\formfields\Heading
              vid: new4-7751
          id: new4495-6306
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: straat
              hasLabel: true
              id: new1281693378
              isNested: false
              label: Straat
              settings:
                handle: straat
                instructionsPosition: ''
                label: Straat
                labelPosition: ''
                maxType: characters
                minType: characters
                required: true
                visibility: ''
              type: verbb\formie\fields\formfields\SingleLineText
              vid: new2324-5362
          id: new1619-9763
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: huisnummer
              hasLabel: true
              id: new674932696
              isNested: false
              label: Huisnummer
              settings:
                handle: huisnummer
                instructionsPosition: ''
                label: Huisnummer
                labelPosition: ''
                required: true
                visibility: ''
              type: verbb\formie\fields\formfields\Number
              vid: new6374-947
            -
              brandNewField: false
              columnWidth: 6
              handle: toevoeging
              hasLabel: true
              id: new1578596457
              isNested: false
              label: Toevoeging
              settings:
                handle: toevoeging
                instructionsPosition: ''
                label: Toevoeging
                labelPosition: ''
                maxType: characters
                minType: characters
                visibility: ''
              type: verbb\formie\fields\formfields\SingleLineText
              vid: new9938-2832
          id: new8774-1564
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: postcode
              hasLabel: true
              id: new285604922
              isNested: false
              label: Postcode
              settings:
                handle: postcode
                instructionsPosition: ''
                label: Postcode
                labelPosition: ''
                maxType: characters
                minType: characters
                required: true
                visibility: ''
              type: verbb\formie\fields\formfields\SingleLineText
              vid: new7033-8190
            -
              brandNewField: false
              columnWidth: 6
              handle: plaats
              hasLabel: true
              id: new902126414
              isNested: false
              label: Plaats
              settings:
                handle: plaats
                instructionsPosition: ''
                label: Plaats
                labelPosition: ''
                maxType: characters
                minType: characters
                required: true
                visibility: ''
              type: verbb\formie\fields\formfields\SingleLineText
              vid: new7189-5115
          id: new930-3152
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: factuurgegevensAndersDanLedigingsgegevens
              hasLabel: true
              id: new889737731
              isNested: false
              label: 'Factuurgegevens anders dan ledigingsgegevens'
              settings:
                checkedValue: Ja
                defaultValue: false
                description: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Factuurgegevens anders dan ledigingsgegevens"}]}]'
                handle: factuurgegevensAndersDanLedigingsgegevens
                instructionsPosition: ''
                label: 'Factuurgegevens anders dan ledigingsgegevens'
                labelPosition: verbb\formie\positions\Hidden
                uncheckedValue: Nee
              type: verbb\formie\fields\formfields\Agree
              vid: new7290-5380
          id: new5669-3217
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: factuurgegevens1
              hasLabel: false
              id: new1849193600
              isNested: false
              label: Factuurgegevens
              settings:
                conditions: '{"showRule":"show","conditionRule":"all","conditions":[{"id":"new1064-4957","field":"{factuurgegevensAndersDanLedigingsgegevens}","condition":"=","value":"1"}]}'
                enableConditions: true
                handle: factuurgegevens1
                headingSize: h5
                instructionsPosition: ''
                label: Factuurgegevens
                labelPosition: ''
              type: verbb\formie\fields\formfields\Heading
              vid: new4567-6704
          id: new9273-1835
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: straat1
              hasLabel: true
              id: new1514345203
              isNested: false
              label: Straat
              settings:
                conditions: '{"showRule":"show","conditionRule":"all","conditions":[{"id":"new230-5641","field":"{factuurgegevensAndersDanLedigingsgegevens}","condition":"=","value":"1"}]}'
                enableConditions: true
                handle: straat1
                instructionsPosition: ''
                label: Straat
                labelPosition: ''
                maxType: characters
                minType: characters
                required: true
                visibility: ''
              type: verbb\formie\fields\formfields\SingleLineText
              vid: new4895-9511
          id: new749-6774
        -
          fields:
            -
              brandNewField: false
              columnWidth: 6
              handle: huisnummer1
              hasLabel: true
              id: new1732748216
              isNested: false
              label: Huisnummer
              settings:
                conditions: '{"showRule":"show","conditionRule":"all","conditions":[{"id":"new5306-8671","field":"{factuurgegevensAndersDanLedigingsgegevens}","condition":"=","value":"1"}]}'
                enableConditions: true
                handle: huisnummer1
                instructionsPosition: ''
                label: Huisnummer
                labelPosition: ''
                required: true
                visibility: ''
              type: verbb\formie\fields\formfields\Number
              vid: new3030-3418
            -
              brandNewField: false
              columnWidth: 12
              handle: toevoeging1
              hasLabel: true
              id: new573126982
              isNested: false
              label: Toevoeging
              settings:
                conditions: '{"showRule":"show","conditionRule":"all","conditions":[{"id":"new5077-8730","field":"{factuurgegevensAndersDanLedigingsgegevens}","condition":"=","value":"1"}]}'
                enableConditions: true
                handle: toevoeging1
                instructionsPosition: ''
                label: Toevoeging
                labelPosition: ''
                maxType: characters
                minType: characters
                visibility: ''
              type: verbb\formie\fields\formfields\SingleLineText
              vid: new6022-5790
          id: new75-4974
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: postcode1
              hasLabel: true
              id: new488330837
              isNested: false
              label: Postcode
              settings:
                conditions: '{"showRule":"show","conditionRule":"all","conditions":[{"id":"new5688-816","field":"{factuurgegevensAndersDanLedigingsgegevens}","condition":"=","value":"1"}]}'
                enableConditions: true
                handle: postcode1
                instructionsPosition: ''
                label: Postcode
                labelPosition: ''
                maxType: characters
                minType: characters
                required: true
                visibility: ''
              type: verbb\formie\fields\formfields\SingleLineText
              vid: new9669-9931
            -
              brandNewField: false
              columnWidth: 6
              handle: plaats1
              hasLabel: true
              id: new1669372506
              isNested: false
              label: Plaats
              settings:
                conditions: '{"showRule":"show","conditionRule":"all","conditions":[{"id":"new2301-9242","field":"{factuurgegevensAndersDanLedigingsgegevens}","condition":"=","value":"1"}]}'
                enableConditions: true
                handle: plaats1
                instructionsPosition: ''
                label: Plaats
                labelPosition: ''
                maxType: characters
                minType: characters
                required: true
                visibility: ''
              type: verbb\formie\fields\formfields\SingleLineText
              vid: new6212-9836
          id: new6995-5438
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: overigeOpmerkingen
              hasLabel: true
              id: new1225693490
              isNested: false
              label: 'Overige opmerkingen'
              settings:
                handle: overigeOpmerkingen
                instructionsPosition: ''
                label: 'Overige opmerkingen'
                labelPosition: ''
                maxType: characters
                minType: characters
                richTextButtons:
                  - bold
                  - italic
                visibility: ''
              type: verbb\formie\fields\formfields\MultiLineText
              vid: new5345-4594
          id: new9646-8803
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: overig
              hasLabel: true
              id: new1431262526
              isNested: false
              label: Overig
              rows:
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: aanvullendeInformatie
                      hasLabel: false
                      isNested: true
                      label: 'Aanvullende informatie'
                      settings:
                        handle: aanvullendeInformatie
                        headingSize: h6
                        instructionsPosition: ''
                        label: 'Aanvullende informatie'
                        labelPosition: ''
                      type: verbb\formie\fields\formfields\Heading
                      vid: new4785-2484
                  id: new8427-437
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: ikHebDitGelezenEnGaHiermeeAkkoord
                      hasLabel: true
                      isNested: true
                      label: 'Ik heb dit gelezen en ga hiermee akkoord'
                      settings:
                        checkedValue: Ja
                        defaultValue: false
                        description: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Ik heb dit gelezen en ga hiermee akkoord"}]}]'
                        handle: ikHebDitGelezenEnGaHiermeeAkkoord
                        instructions: "- Wij adviseren om de documenten voor vernietiging klaar te zetten in vuilniszakken van 60 liter of in verhuisdozen. Plaats vuilniszakken of verhuisdozen op een goed bereikbare plek voor onze chauffeur. \n- Alle documenten dienen uit de ordners gehaald te worden. Tabbladen, nietjes, en paperclips hoeven niet verwijderd te worden\n- De eerste 15 minuten handelingstijd zijn inbegrepen. Extra handelingstijd voor onze chauffeur kost €30 per 15 minuten. \n- Voor de huur van een container rekenen wij €0,50 per container per dag. \n- Na aanvraag ontvang je een orderbevestiging per mail en wordt de opdracht in gang gezet. Betaling vindt achteraf plaats middels factuur."
                        instructionsPosition: verbb\formie\positions\AboveInput
                        label: 'Ik heb dit gelezen en ga hiermee akkoord'
                        labelPosition: verbb\formie\positions\Hidden
                        required: true
                        uncheckedValue: Nee
                      type: verbb\formie\fields\formfields\Agree
                      vid: new6957-7674
                  id: new303-3270
              settings:
                cssClasses: ''
                handle: overig
                instructionsPosition: ''
                label: Overig
                labelPosition: verbb\formie\positions\Hidden
                visibility: ''
              supportsNested: true
              type: verbb\formie\fields\formfields\Group
              vid: new9159-9235
          id: new6822-4759
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: configuration
              hasLabel: true
              id: new507774387
              isNested: false
              label: Configuratie
              rows:
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: containers
                      hasLabel: true
                      isNested: true
                      label: Containers
                      settings:
                        handle: containers
                        instructionsPosition: ''
                        label: Containers
                        labelPosition: ''
                        maxType: characters
                        minType: characters
                        richTextButtons:
                          - bold
                          - italic
                        visibility: ''
                      type: verbb\formie\fields\formfields\MultiLineText
                      vid: new148-5087
                  id: new4738-9786
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: fillConvenience
                      hasLabel: true
                      isNested: true
                      label: Vulgemak
                      settings:
                        checkedValue: Ja
                        defaultValue: false
                        description: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Vulgemak"}]}]'
                        handle: fillConvenience
                        instructionsPosition: ''
                        label: Vulgemak
                        labelPosition: verbb\formie\positions\Hidden
                        uncheckedValue: Nee
                      type: verbb\formie\fields\formfields\Agree
                      vid: new1006-8307
                  id: new9181-4975
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: destructionDeclaration
                      hasLabel: true
                      isNested: true
                      label: Vernietigingsverklaring
                      settings:
                        checkedValue: Ja
                        defaultValue: false
                        description: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Vernietigingsverklaring"}]}]'
                        handle: destructionDeclaration
                        instructionsPosition: ''
                        label: Vernietigingsverklaring
                        labelPosition: verbb\formie\positions\Hidden
                        uncheckedValue: Nee
                      type: verbb\formie\fields\formfields\Agree
                      vid: new3916-7393
                  id: new2386-6843
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: deliveryDate
                      hasLabel: true
                      isNested: true
                      label: Leverdatum
                      settings:
                        handle: deliveryDate
                        instructionsPosition: ''
                        label: Leverdatum
                        labelPosition: ''
                        maxType: characters
                        minType: characters
                        visibility: ''
                      type: verbb\formie\fields\formfields\SingleLineText
                      vid: new4083-4932
                  id: new9854-2827
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: retrievalType
                      hasLabel: true
                      isNested: true
                      label: 'Ophaal soort'
                      settings:
                        handle: retrievalType
                        instructionsPosition: ''
                        label: 'Ophaal soort'
                        labelPosition: ''
                        maxType: characters
                        minType: characters
                        visibility: ''
                      type: verbb\formie\fields\formfields\SingleLineText
                      vid: new7098-4945
                  id: new1966-1808
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: retrievalDate
                      hasLabel: true
                      isNested: true
                      label: Ophaaldatum
                      settings:
                        handle: retrievalDate
                        instructionsPosition: ''
                        label: Ophaaldatum
                        labelPosition: ''
                        maxType: characters
                        minType: characters
                        visibility: ''
                      type: verbb\formie\fields\formfields\SingleLineText
                      vid: new9171-1729
                  id: new5815-4816
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: containerkosten
                      hasLabel: true
                      isNested: true
                      label: Containerkosten
                      settings:
                        handle: containerkosten
                        instructionsPosition: ''
                        label: Containerkosten
                        labelPosition: ''
                        maxType: characters
                        minType: characters
                        visibility: ''
                      type: verbb\formie\fields\formfields\SingleLineText
                      vid: new6848-3100
                  id: new2767-4008
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: vernietigingsverklaringKosten
                      hasLabel: true
                      isNested: true
                      label: 'Vernietigingsverklaring kosten'
                      settings:
                        handle: vernietigingsverklaringKosten
                        instructionsPosition: ''
                        label: 'Vernietigingsverklaring kosten'
                        labelPosition: ''
                        maxType: characters
                        minType: characters
                        visibility: ''
                      type: verbb\formie\fields\formfields\SingleLineText
                      vid: new3947-5153
                  id: new1547-1544
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: transportkosten
                      hasLabel: true
                      isNested: true
                      label: Transportkosten
                      settings:
                        handle: transportkosten
                        instructionsPosition: ''
                        label: Transportkosten
                        labelPosition: ''
                        maxType: characters
                        minType: characters
                        visibility: ''
                      type: verbb\formie\fields\formfields\SingleLineText
                      vid: new961-3789
                  id: new5529-2094
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: milieutoeslag
                      hasLabel: true
                      isNested: true
                      label: Milieutoeslag
                      settings:
                        handle: milieutoeslag
                        instructionsPosition: ''
                        label: Milieutoeslag
                        labelPosition: ''
                        maxType: characters
                        minType: characters
                        visibility: ''
                      type: verbb\formie\fields\formfields\SingleLineText
                      vid: new3519-6478
                  id: new7009-2531
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: btw
                      hasLabel: true
                      isNested: true
                      label: Btw
                      settings:
                        handle: btw
                        instructionsPosition: ''
                        label: Btw
                        labelPosition: ''
                        maxType: characters
                        minType: characters
                        visibility: ''
                      type: verbb\formie\fields\formfields\SingleLineText
                      vid: new908-9983
                  id: new2290-9575
                -
                  fields:
                    -
                      brandNewField: false
                      columnWidth: 12
                      handle: totaal
                      hasLabel: true
                      isNested: true
                      label: Totaal
                      settings:
                        handle: totaal
                        instructionsPosition: ''
                        label: Totaal
                        labelPosition: ''
                        maxType: characters
                        minType: characters
                        visibility: ''
                      type: verbb\formie\fields\formfields\SingleLineText
                      vid: new9671-2376
                  id: new5033-3312
              settings:
                handle: configuration
                instructionsPosition: ''
                label: Configuratie
                labelPosition: ''
                visibility: hidden
              supportsNested: true
              type: verbb\formie\fields\formfields\Group
              vid: new7961-2730
          id: new1798-9017
        -
          fields:
            -
              brandNewField: false
              columnWidth: 12
              handle: gclid
              hasLabel: true
              id: new896884462
              isNested: false
              label: gclid
              settings:
                columnType: ''
                defaultOption: custom
                handle: gclid
                includeInEmail: true
                instructionsPosition: ''
                label: gclid
                labelPosition: verbb\formie\positions\Hidden
              type: verbb\formie\fields\formfields\Hidden
              vid: new6531-9712
          id: new4905-4618
      settings:
        backButtonLabel: Back
        buttonsPosition: left
        label: 'Page 1'
        saveButtonLabel: Save
        saveButtonStyle: link
        showBackButton: false
        submitButtonLabel: 'Verstuur je aanvraag'
      sortOrder: 0
  settings:
    collectIp: false
    collectUser: false
    dataRetention: null
    dataRetentionValue: null
    defaultEmailTemplateId: null
    defaultInstructionsPosition: verbb\formie\positions\AboveInput
    defaultLabelPosition: verbb\formie\positions\AboveInput
    disableCaptchas: false
    displayCurrentPageTitle: false
    displayFormTitle: false
    displayPageProgress: false
    displayPageTabs: false
    errorMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Couldn’t save submission due to errors."}]}]'
    errorMessagePosition: top-form
    fileUploadsAction: null
    integrations:
      javascript:
        enabled: ''
        showAllPages: ''
      zapierWebhook:
        enabled: '1'
        webhook: 'https://hooks.zapier.com/hooks/catch/10221676/b0v8l9l/'
    limitSubmissions: false
    limitSubmissionsMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    limitSubmissionsNumber: null
    limitSubmissionsType: total
    loadingIndicator: null
    loadingIndicatorText: null
    progressPosition: end
    redirectUrl: null
    requireUser: false
    requireUserMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    scheduleForm: false
    scheduleFormEnd: null
    scheduleFormExpiredMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    scheduleFormPendingMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    scheduleFormStart: null
    scrollToTop: true
    submissionTitleFormat: '{timestamp}'
    submitAction: message
    submitActionFormHide: true
    submitActionMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Je offerteaanvraag is verzonden"}]}]'
    submitActionMessagePosition: top-form
    submitActionMessageTimeout: null
    submitActionTab: null
    submitActionUrl: null
    submitMethod: ajax
    validationOnFocus: false
    validationOnSubmit: true
  userDeletedAction: retain
defaultStatus: aaa202d7-24d8-47d2-9717-c8b718c4b0f9 # New
handle: archiveDestruction
name: 'Archief vernietiging'
submitActionEntry: null
template: null
