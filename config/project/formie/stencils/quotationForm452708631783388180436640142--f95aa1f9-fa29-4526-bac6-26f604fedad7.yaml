data:
  dataRetention: forever
  dataRetentionValue: ''
  fileUploadsAction: retain
  notifications:
    -
      attachAssets: null
      attachFiles: true
      attachPdf: ''
      bcc: null
      cc: null
      conditions: null
      content: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Test"}]}]'
      enableConditions: false
      enabled: true
      formId: null
      from: <EMAIL>
      fromName: 'Milieu Service Nederland'
      id: stencilHIuNE9EUGuU1uWxu
      name: 'Offerte aanvraag'
      pdfTemplateId: null
      recipients: email
      replyTo: null
      replyToName: null
      sender: null
      subject: 'Offerteaanvraag via website ({field.company})'
      templateId: 2
      to: '<EMAIL>,{field.email}'
      toConditions: null
      uid: null
  pages:
    -
      id: new9476-1233
      label: 'Page 1'
      rows:
        -
          fields:
            -
              brandNewField: false
              columnSuffix: ymqpbnms
              handle: uuidInput
              hasConditions: false
              hasLabel: true
              id: new2004253453
              isCosmetic: false
              isNested: false
              isSynced: false
              label: uuidInput
              name: uuid
              settings:
                columnType: null
                conditions: null
                containerAttributes: null
                cookieName: null
                cssClasses: null
                defaultOption: custom
                defaultValue: '{submissionId}'
                enableConditions: false
                enableContentEncryption: false
                errorMessage: null
                formId: null
                handle: uuidInput
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                isNested: false
                label: uuidInput
                labelPosition: verbb\formie\positions\Hidden
                matchField: null
                placeholder: null
                prePopulate: null
                queryParameter: null
                required: false
                visibility: null
              supportsNested: false
              type: verbb\formie\fields\formfields\Hidden
              vid: new6246-4500
          id: new527-5048
        -
          fields:
            -
              columnSuffix: ygqnzcvn
              handle: clientName
              hasConditions: false
              hasLabel: true
              hasSubfields: false
              id: new1879258067
              isCosmetic: false
              isNested: false
              isSynced: false
              label: Naam
              name: Naam
              settings:
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: null
                enableConditions: false
                enableContentEncryption: false
                errorMessage: null
                firstNameCollapsed: true
                firstNameDefaultValue: null
                firstNameEnabled: true
                firstNameErrorMessage: null
                firstNameLabel: 'First Name'
                firstNamePlaceholder: null
                firstNamePrePopulate: null
                firstNameRequired: false
                formId: null
                handle: clientName
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: verbb\formie\positions\AboveInput
                isNested: false
                label: Naam
                labelPosition: null
                lastNameCollapsed: true
                lastNameDefaultValue: null
                lastNameEnabled: true
                lastNameErrorMessage: null
                lastNameLabel: 'Last Name'
                lastNamePlaceholder: null
                lastNamePrePopulate: null
                lastNameRequired: false
                matchField: null
                middleNameCollapsed: true
                middleNameDefaultValue: null
                middleNameEnabled: false
                middleNameErrorMessage: null
                middleNameLabel: 'Middle Name'
                middleNamePlaceholder: null
                middleNamePrePopulate: null
                middleNameRequired: false
                placeholder: null
                prePopulate: null
                prefixCollapsed: true
                prefixDefaultValue: null
                prefixEnabled: false
                prefixErrorMessage: null
                prefixLabel: Prefix
                prefixPlaceholder: null
                prefixPrePopulate: null
                prefixRequired: false
                required: true
                subfieldLabelPosition: null
                useMultipleFields: false
                visibility: null
              subfieldOptions:
                -
                  handle: prefix
                  label: Prefix
                -
                  handle: firstName
                  label: 'First Name'
                -
                  handle: middleName
                  label: 'Middle Name'
                -
                  handle: lastName
                  label: 'Last Name'
              supportsNested: false
              type: verbb\formie\fields\formfields\Name
              vid: new5303-3136
          id: new8088-6629
        -
          fields:
            -
              columnSuffix: crhbwmrn
              handle: company
              hasConditions: false
              hasLabel: true
              id: new626366954
              isCosmetic: false
              isNested: false
              isSynced: false
              label: Organisatie
              name: Organisatie
              settings:
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: null
                enableConditions: false
                enableContentEncryption: false
                errorMessage: null
                formId: null
                handle: company
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                isNested: false
                label: Organisatie
                labelPosition: null
                limit: false
                matchField: null
                max: null
                maxType: characters
                min: null
                minType: characters
                placeholder: null
                prePopulate: null
                required: true
                visibility: null
              supportsNested: false
              type: verbb\formie\fields\formfields\SingleLineText
              vid: new5244-9773
            -
              columnSuffix: nezzuwau
              handle: city
              hasConditions: false
              hasLabel: true
              id: new201336597
              isCosmetic: false
              isNested: false
              isSynced: false
              label: Plaats
              name: Plaats
              settings:
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: null
                enableConditions: false
                enableContentEncryption: false
                errorMessage: null
                formId: null
                handle: city
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                isNested: false
                label: Plaats
                labelPosition: null
                limit: false
                matchField: null
                max: null
                maxType: characters
                min: null
                minType: characters
                placeholder: null
                prePopulate: null
                required: true
                visibility: null
              supportsNested: false
              type: verbb\formie\fields\formfields\SingleLineText
              vid: new1934-8345
          id: new656-2285
        -
          fields:
            -
              columnSuffix: feohqjhe
              handle: email
              hasConditions: false
              hasLabel: true
              id: new410960660
              isCosmetic: false
              isNested: false
              isSynced: false
              label: E-mailadres
              name: E-mailadres
              settings:
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: null
                enableConditions: false
                enableContentEncryption: false
                errorMessage: null
                formId: null
                handle: email
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                isNested: false
                label: E-mailadres
                labelPosition: null
                matchField: null
                placeholder: null
                prePopulate: null
                required: true
                uniqueValue: false
                validateDomain: false
                visibility: null
              supportsNested: false
              type: verbb\formie\fields\formfields\Email
              vid: new8543-1781
            -
              columnSuffix: aquxbryu
              handle: phone
              hasConditions: false
              hasLabel: true
              hasSubfields: false
              id: new61740562
              isCosmetic: false
              isNested: false
              isSynced: false
              label: Telefoonnummer
              name: Telefoonnummer
              settings:
                conditions: null
                containerAttributes: null
                countryCollapsed: true
                countryDefaultValue: null
                countryEnabled: false
                countryShowDialCode: true
                cssClasses: null
                defaultValue: null
                enableConditions: false
                enableContentEncryption: false
                errorMessage: null
                formId: null
                handle: phone
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                isNested: false
                label: Telefoonnummer
                labelPosition: null
                matchField: null
                placeholder: null
                prePopulate: null
                required: true
                subfieldLabelPosition: null
                visibility: null
              subfieldOptions:
                -
                  handle: country
                  label: 'Country (ISO)'
                -
                  handle: countryName
                  label: 'Country (Full)'
                -
                  handle: countryCode
                  label: 'Country Code'
                -
                  handle: number
                  label: Number
              supportsNested: false
              type: verbb\formie\fields\formfields\Phone
              vid: new5451-9756
          id: new5422-3189
        -
          fields:
            -
              columnSuffix: xrdyaeuv
              handle: comments
              hasConditions: false
              hasLabel: true
              id: new465013122
              isCosmetic: false
              isNested: false
              isSynced: false
              label: Opmerkingen
              name: Opmerkingen
              settings:
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: null
                enableConditions: false
                enableContentEncryption: false
                errorMessage: null
                formId: null
                handle: comments
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                isNested: false
                label: Opmerkingen
                labelPosition: null
                limit: false
                matchField: null
                max: null
                maxType: characters
                min: null
                minType: characters
                placeholder: null
                prePopulate: null
                required: false
                richTextButtons:
                  - bold
                  - italic
                useRichText: false
                visibility: null
              supportsNested: false
              type: verbb\formie\fields\formfields\MultiLineText
              vid: new9162-5560
          id: new179-2615
        -
          fields:
            -
              columnSuffix: bewypqzm
              handle: newsletter
              hasConditions: false
              hasLabel: true
              id: new1219223915
              isCosmetic: false
              isNested: false
              isSynced: false
              label: Nieuwsbrief
              name: Nieuwsbrief
              settings:
                checkedValue: 'Yes'
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: false
                description:
                  -
                    attrs:
                      textAlign: start
                    content:
                      -
                        text: 'Ik wil graag op de hoogte gehouden worden en schrijf mij in voor de nieuwsbrief'
                        type: text
                    type: paragraph
                enableConditions: false
                enableContentEncryption: false
                errorMessage: null
                formId: null
                handle: newsletter
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                isNested: false
                label: Nieuwsbrief
                labelPosition: verbb\formie\positions\Hidden
                matchField: null
                placeholder: null
                prePopulate: null
                required: false
                uncheckedValue: 'No'
                visibility: null
              supportsNested: false
              type: verbb\formie\fields\formfields\Agree
              vid: new7849-8514
          id: new1552-5547
        -
          fields:
            -
              columnSuffix: pskufsdw
              handle: gclid
              hasConditions: false
              hasLabel: true
              id: new2029877116
              isCosmetic: false
              isNested: false
              isSynced: false
              label: gclid
              name: gclid
              settings:
                columnType: null
                conditions: null
                containerAttributes: null
                cookieName: null
                cssClasses: null
                defaultOption: custom
                defaultValue: null
                enableConditions: false
                enableContentEncryption: false
                errorMessage: null
                formId: null
                handle: gclid
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                isNested: false
                label: gclid
                labelPosition: verbb\formie\positions\Hidden
                matchField: null
                placeholder: null
                prePopulate: null
                queryParameter: null
                required: false
                visibility: null
              supportsNested: false
              type: verbb\formie\fields\formfields\Hidden
              vid: new9108-8248
          id: new7804-5214
        -
          fields:
            -
              brandNewField: false
              columnSuffix: azfrmdbk
              handle: configuration
              hasConditions: false
              hasLabel: true
              id: new309826335
              isCosmetic: false
              isNested: false
              isSynced: false
              label: Configuratie
              name: Configuratie
              settings:
                conditions: null
                containerAttributes: null
                cssClasses: null
                defaultValue: null
                enableConditions: false
                enableContentEncryption: false
                errorMessage: null
                formId: null
                handle: configuration
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                isNested: false
                label: Configuratie
                labelPosition: null
                limit: false
                matchField: null
                max: null
                maxType: characters
                min: null
                minType: characters
                placeholder: null
                prePopulate: null
                required: false
                richTextButtons:
                  - bold
                  - italic
                useRichText: false
                visibility: hidden
              supportsNested: false
              type: verbb\formie\fields\formfields\MultiLineText
              vid: new8710-1586
          id: new8391-4184
        -
          fields:
            -
              brandNewField: false
              columnSuffix: null
              handle: configurationNew
              hasConditions: false
              hasLabel: true
              id: new756077971
              isCosmetic: false
              isElementField: true
              isNested: false
              isSynced: false
              label: 'Configuratie nieuw'
              name: 'Configuratie nieuw'
              rows:
                -
                  fields:
                    -
                      brandNewField: false
                      columnSuffix: null
                      handle: disposalType
                      hasConditions: false
                      hasError: false
                      hasLabel: true
                      icon: '<svg aria-hidden="true" focusable="false" data-prefix="far" data-icon="text-width" class="svg-inline--fa fa-text-width fa-w-14" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M0 139.131V44c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v95.131c0 6.627-5.373 12-12 12h-26.747c-6.627 0-12-5.373-12-12V80H251.238v192H300c6.627 0 12 5.373 12 12v24c0 6.627-5.373 12-12 12H148c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h48.762V80H50.125v59.131c0 6.627-5.373 12-12 12H12c-6.627 0-12-5.372-12-12zm444.241 252.145l-72.001-67.994c-7.581-7.16-20.24-1.863-20.24 8.724V376H96v-43.993c0-10.58-12.652-15.89-20.24-8.724L3.759 391.276c-5.014 4.735-5.01 12.716 0 17.448l72.001 67.994c7.581 7.16 20.24 1.863 20.24-8.724V424h256v43.993c0 10.58 12.652 15.89 20.24 8.724l72.001-67.993c5.014-4.735 5.01-12.716 0-17.448z"></path></svg>'
                      id: null
                      isCosmetic: false
                      isNested: true
                      isSynced: false
                      label: Afvalstroom
                      name: Afvalstroom
                      settings:
                        conditions: null
                        containerAttributes: null
                        cssClasses: null
                        defaultValue: null
                        enableConditions: false
                        enableContentEncryption: false
                        errorMessage: null
                        formId: null
                        handle: disposalType
                        includeInEmail: true
                        inputAttributes: null
                        instructions: null
                        instructionsPosition: null
                        isNested: true
                        label: Afvalstroom
                        labelPosition: null
                        limit: false
                        matchField: null
                        max: null
                        maxType: characters
                        min: null
                        minType: characters
                        placeholder: null
                        prePopulate: null
                        required: false
                        visibility: null
                      supportsNested: false
                      type: verbb\formie\fields\formfields\SingleLineText
                      vid: new8548-3902
                    -
                      brandNewField: false
                      columnSuffix: null
                      handle: containers
                      hasConditions: false
                      hasError: false
                      hasLabel: true
                      icon: '<svg aria-hidden="true" focusable="false" data-prefix="far" data-icon="paragraph" class="svg-inline--fa fa-paragraph fa-w-12" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path fill="currentColor" d="M372 32H165.588C74.935 32 .254 104.882.001 195.535-.252 286.177 73.415 360 164 360v108c0 6.627 5.373 12 12 12h32c6.627 0 12-5.373 12-12V88h40v380c0 6.627 5.373 12 12 12h32c6.627 0 12-5.373 12-12V88h56c6.627 0 12-5.373 12-12V44c0-6.627-5.373-12-12-12zM164 304c-59.552 0-108-48.449-108-108S104.448 88 164 88v216z"></path></svg>'
                      id: null
                      isCosmetic: false
                      isNested: true
                      isSynced: false
                      label: Containers
                      name: Containers
                      settings:
                        conditions: null
                        containerAttributes: null
                        cssClasses: null
                        defaultValue: null
                        enableConditions: false
                        enableContentEncryption: false
                        errorMessage: null
                        formId: null
                        handle: containers
                        includeInEmail: true
                        inputAttributes: null
                        instructions: null
                        instructionsPosition: null
                        isNested: true
                        label: Containers
                        labelPosition: null
                        limit: false
                        matchField: null
                        max: null
                        maxType: characters
                        min: null
                        minType: characters
                        placeholder: null
                        prePopulate: null
                        required: false
                        richTextButtons:
                          - bold
                          - italic
                        useRichText: false
                        visibility: null
                      supportsNested: false
                      type: verbb\formie\fields\formfields\MultiLineText
                      vid: new3218-2183
                    -
                      brandNewField: false
                      columnSuffix: null
                      handle: disposalFrequency
                      hasConditions: false
                      hasError: false
                      hasLabel: true
                      icon: '<svg aria-hidden="true" focusable="false" data-prefix="far" data-icon="text-width" class="svg-inline--fa fa-text-width fa-w-14" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M0 139.131V44c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v95.131c0 6.627-5.373 12-12 12h-26.747c-6.627 0-12-5.373-12-12V80H251.238v192H300c6.627 0 12 5.373 12 12v24c0 6.627-5.373 12-12 12H148c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h48.762V80H50.125v59.131c0 6.627-5.373 12-12 12H12c-6.627 0-12-5.372-12-12zm444.241 252.145l-72.001-67.994c-7.581-7.16-20.24-1.863-20.24 8.724V376H96v-43.993c0-10.58-12.652-15.89-20.24-8.724L3.759 391.276c-5.014 4.735-5.01 12.716 0 17.448l72.001 67.994c7.581 7.16 20.24 1.863 20.24-8.724V424h256v43.993c0 10.58 12.652 15.89 20.24 8.724l72.001-67.993c5.014-4.735 5.01-12.716 0-17.448z"></path></svg>'
                      id: null
                      isCosmetic: false
                      isNested: true
                      isSynced: false
                      label: Ledigingsfrequentie
                      name: Ledigingsfrequentie
                      settings:
                        conditions: null
                        containerAttributes: null
                        cssClasses: null
                        defaultValue: null
                        enableConditions: false
                        enableContentEncryption: false
                        errorMessage: null
                        formId: null
                        handle: disposalFrequency
                        includeInEmail: true
                        inputAttributes: null
                        instructions: null
                        instructionsPosition: null
                        isNested: true
                        label: Ledigingsfrequentie
                        labelPosition: null
                        limit: false
                        matchField: null
                        max: null
                        maxType: characters
                        min: null
                        minType: characters
                        placeholder: null
                        prePopulate: null
                        required: false
                        visibility: null
                      supportsNested: false
                      type: verbb\formie\fields\formfields\SingleLineText
                      vid: new3257-129
                  id: null
              settings:
                addLabel: 'Add another row'
                conditions: null
                containerAttributes: null
                contentTable: null
                cssClasses: null
                defaultValue: null
                enableConditions: false
                enableContentEncryption: false
                errorMessage: null
                formId: null
                handle: configurationNew
                includeInEmail: true
                inputAttributes: null
                instructions: null
                instructionsPosition: null
                isNested: false
                label: 'Configuratie nieuw'
                labelPosition: null
                matchField: null
                maxRows: null
                minRows: null
                placeholder: null
                prePopulate: null
                required: false
                visibility: hidden
              supportsNested: true
              type: verbb\formie\fields\formfields\Repeater
              vid: new8123-2837
          id: new290-7551
      settings:
        backButtonLabel: Back
        buttonsPosition: left
        containerAttributes: null
        cssClasses: null
        enableJsEvents: false
        enableNextButtonConditions: false
        enablePageConditions: false
        inputAttributes: null
        saveButtonLabel: Save
        saveButtonStyle: link
        showBackButton: false
        showSaveButton: false
        submitButtonLabel: 'Verstuur je aanvraag'
  settings:
    collectIp: false
    collectUser: false
    dataRetention: null
    dataRetentionValue: null
    defaultEmailTemplateId: null
    defaultInstructionsPosition: verbb\formie\positions\AboveInput
    defaultLabelPosition: verbb\formie\positions\AboveInput
    disableCaptchas: false
    displayCurrentPageTitle: false
    displayFormTitle: false
    displayPageProgress: false
    displayPageTabs: false
    errorMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Couldn’t save submission due to errors."}]}]'
    errorMessagePosition: top-form
    fileUploadsAction: null
    integrations:
      javascript:
        enabled: ''
        showAllPages: ''
      zapierWebhook:
        enabled: ''
        webhook: 'https://hooks.zapier.com/hooks/catch/10221676/b0v8l9l/'
    limitSubmissions: false
    limitSubmissionsMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    limitSubmissionsNumber: null
    limitSubmissionsType: total
    loadingIndicator: null
    loadingIndicatorText: null
    progressPosition: end
    redirectUrl: null
    requireUser: false
    requireUserMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    scheduleForm: false
    scheduleFormEnd: null
    scheduleFormExpiredMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    scheduleFormPendingMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"}}]'
    scheduleFormStart: null
    scrollToTop: true
    submissionTitleFormat: '{timestamp}'
    submitAction: message
    submitActionFormHide: true
    submitActionMessage: '[{"type":"paragraph","attrs":{"textAlign":"start"},"content":[{"type":"text","text":"Je offerteaanvraag is verzonden"}]}]'
    submitActionMessagePosition: top-form
    submitActionMessageTimeout: null
    submitActionTab: null
    submitActionUrl: null
    submitMethod: page-reload
    validationOnFocus: false
    validationOnSubmit: true
  userDeletedAction: retain
defaultStatus: aaa202d7-24d8-47d2-9717-c8b718c4b0f9 # New
handle: quotationForm452708631783388180436640142
name: 'Offerte formulier'
submitActionEntry: null
template: null
