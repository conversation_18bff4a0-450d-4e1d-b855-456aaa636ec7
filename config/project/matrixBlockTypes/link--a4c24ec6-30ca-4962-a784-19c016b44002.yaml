field: a81086a9-50a9-4665-8d8f-1a7333317bfc # Quick links
fieldLayouts:
  e9de31fa-b42b-4698-903f-42621067d77a:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: a2c0fa14-4fc7-4663-afce-27e4cf1eca12 # Link
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a2cfc9b5-3d36-4b9b-b73a-d594476b0166
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 25d1876a-4356-48a5-ba11-001867a1c1a6 # Icoon
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5a05b591-b84c-4216-b9f6-d992087c2384
            userCondition: null
            warning: null
            width: 50
        name: Content
        uid: 361e1844-5d59-4092-b090-9c664260eb0c
        userCondition: null
fields:
  25d1876a-4356-48a5-ba11-001867a1c1a6: # Icoon
    columnSuffix: ataugbhu
    contentColumnType: string
    fieldGroup: null
    handle: icon
    instructions: null
    name: Icoon
    searchable: false
    settings:
      columnType: text
      iconSets: '*'
      renderId: null
      showLabels: true
    translationKeyFormat: null
    translationMethod: none
    type: verbb\iconpicker\fields\IconPickerField
  a2c0fa14-4fc7-4663-afce-27e4cf1eca12: # Link
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: linkItem
    instructions: null
    name: Link
    searchable: false
    settings:
      allowCustomText: true
      allowTarget: false
      autoNoReferrer: false
      customTextMaxLength: 0
      customTextRequired: true
      defaultLinkName: entry
      defaultText: ''
      enableAllLinkTypes: false
      enableAriaLabel: false
      enableElementCache: false
      enableTitle: false
      typeSettings:
        asset:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        category:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        custom:
          allowAliases: false
          disableValidation: false
          enabled: true
        email:
          allowAliases: false
          disableValidation: false
          enabled: false
        entry:
          allowCrossSiteLink: false
          allowCustomQuery: true
          enabled: true
          sources: '*'
        site:
          enabled: false
          sites: '*'
        tel:
          allowAliases: false
          disableValidation: false
          enabled: false
        url:
          allowAliases: false
          disableValidation: false
          enabled: true
        user:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
    translationKeyFormat: null
    translationMethod: none
    type: lenz\linkfield\fields\LinkField
handle: link
name: Link
sortOrder: 1
