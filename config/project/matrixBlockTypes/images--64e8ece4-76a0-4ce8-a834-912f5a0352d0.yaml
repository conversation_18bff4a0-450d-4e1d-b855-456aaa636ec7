field: 349d1a78-85e5-4729-95df-8b780aeb9c37 # <PERSON>er logo's
fieldLayouts:
  3bf9e261-65bf-448c-9171-26f777b3e9fc:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 484a6404-108f-4380-a57c-cd1428193bf4 # Image
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 71cb010c-c61d-4c08-a847-2cff8cf52145
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 07657a00-9653-43d4-b9d0-75b2a4c1f27d # Image Link
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f579d746-e841-40be-ac38-ee9d29019292
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: b9ef4608-eef2-4f49-b1ca-368c6a60187f
        userCondition: null
fields:
  484a6404-108f-4380-a57c-cd1428193bf4: # Image
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: image
    instructions: null
    name: Image
    searchable: false
    settings:
      allowSelfRelations: false
      allowSubfolders: false
      allowUploads: true
      allowedKinds: null
      defaultUploadLocationSource: 'volume:7b5602dc-2373-4d41-a4f7-a7efcca8052f' # Footer 
      defaultUploadLocationSubpath: null
      localizeRelations: false
      maxRelations: null
      minRelations: null
      previewMode: full
      restrictFiles: false
      restrictLocation: false
      restrictedDefaultUploadSubpath: null
      restrictedLocationSource: 'volume:7b5602dc-2373-4d41-a4f7-a7efcca8052f' # Footer 
      restrictedLocationSubpath: null
      selectionCondition:
        __assoc__:
          -
            - elementType
            - craft\elements\Asset
          -
            - fieldContext
            - global
          -
            - class
            - craft\elements\conditions\assets\AssetCondition
      selectionLabel: 'Add an image'
      showSiteMenu: true
      showUnpermittedFiles: false
      showUnpermittedVolumes: false
      source: null
      sources:
        - 'volume:7b5602dc-2373-4d41-a4f7-a7efcca8052f' # Footer 
      targetSiteId: null
      validateRelatedElements: false
      viewMode: large
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\Assets
  07657a00-9653-43d4-b9d0-75b2a4c1f27d: # Image Link
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: imageLink
    instructions: null
    name: 'Image Link'
    searchable: false
    settings:
      allowCustomText: true
      allowTarget: true
      autoNoReferrer: true
      customTextMaxLength: 0
      customTextRequired: false
      defaultLinkName: url
      defaultText: ''
      enableAllLinkTypes: false
      enableAriaLabel: false
      enableElementCache: false
      enableTitle: false
      typeSettings:
        asset:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        category:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        custom:
          allowAliases: false
          disableValidation: false
          enabled: false
        email:
          allowAliases: false
          disableValidation: false
          enabled: false
        entry:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources: '*'
        site:
          enabled: false
          sites: '*'
        tel:
          allowAliases: false
          disableValidation: false
          enabled: false
        url:
          allowAliases: false
          disableValidation: false
          enabled: true
        user:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
    translationKeyFormat: null
    translationMethod: none
    type: lenz\linkfield\fields\LinkField
handle: images
name: Images
sortOrder: 1
