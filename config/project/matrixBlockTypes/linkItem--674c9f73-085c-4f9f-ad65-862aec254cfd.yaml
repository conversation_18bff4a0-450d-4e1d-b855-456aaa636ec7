field: cb0f125b-6427-40c3-9e56-d3e4bc6b3209 # Links
fieldLayouts:
  889f5229-f1aa-4599-a138-afba4849ef82:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 0cd670c9-cfa5-4d59-bbc1-dcd29af91bcd # Icoon
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9ee6f267-af54-4395-aff4-77906f9eb632
            userCondition: null
            warning: null
            width: 25
          -
            elementCondition: null
            fieldUid: 2df28bbc-b38b-4ffc-b079-5d025b3c5992 # Link
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a47b25f3-50cf-4c31-baca-68529099c327
            userCondition: null
            warning: null
            width: 75
        name: Content
        uid: 72040b49-17e8-419f-9452-a1c827f96b71
        userCondition: null
fields:
  0cd670c9-cfa5-4d59-bbc1-dcd29af91bcd: # Icoon
    columnSuffix: kozipylt
    contentColumnType: string
    fieldGroup: null
    handle: icon
    instructions: null
    name: Icoon
    searchable: false
    settings:
      columnType: text
      iconSets: '*'
      renderId: null
      showLabels: true
    translationKeyFormat: null
    translationMethod: none
    type: verbb\iconpicker\fields\IconPickerField
  2df28bbc-b38b-4ffc-b079-5d025b3c5992: # Link
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: page
    instructions: null
    name: Link
    searchable: false
    settings:
      allowCustomText: true
      allowTarget: true
      autoNoReferrer: true
      customTextMaxLength: 0
      customTextRequired: false
      defaultLinkName: entry
      defaultText: ''
      enableAllLinkTypes: false
      enableAriaLabel: false
      enableElementCache: false
      enableTitle: false
      typeSettings:
        asset:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        category:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        custom:
          allowAliases: false
          disableValidation: false
          enabled: false
        email:
          allowAliases: false
          disableValidation: false
          enabled: true
        entry:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources: '*'
        site:
          enabled: false
          sites: '*'
        tel:
          allowAliases: false
          disableValidation: false
          enabled: true
        url:
          allowAliases: false
          disableValidation: false
          enabled: true
        user:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
    translationKeyFormat: null
    translationMethod: none
    type: lenz\linkfield\fields\LinkField
handle: linkItem
name: Link
sortOrder: 1
