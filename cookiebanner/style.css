.cookiebanner button {
    appearance: none;
    border: 0;
    background: 0;
}

.cookiebanner {
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 1000;
    transform: translate(-50%, -50%);
    display: flex !important;
    align-items: flex-start;
    width: 100vw;
    max-width: 900px;
    max-height: 100vh;
    overflow: auto;
    border-radius: 8px;
    padding: 40px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 4px 4px 28px rgba(0, 0, 0, 0.25);
    font-family: Ubuntu Regular, ui-sans-serif, system-ui, -apple-system,
        BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
        'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
    color: #000000;
    transition: opacity 0.15s ease-in-out;
}

.cookiebanner--hidden {
    pointer-events: none;
    opacity: 0;
}

.cookiebanner__header {
    flex-shrink: 0;
    margin-right: 32px;
}

.cookiebanner__logo {
    display: block;
    width: 150px;
    height: 150px;
}

.cookiebanner__details {
    display: flex;
    flex-direction: column;
}

.cookiebanner__title {
    margin: 0 0 16px 0;
    padding: 0;
    font-size: 22px;
    font-weight: 700;
    line-height: 28px;
    color: rgb(26, 26, 26);
    font-family: Ubuntu Bold, ui-sans-serif, system-ui, -apple-system,
        BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
        'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

.cookiebanner__text {
    margin: 0;
    padding: 0;
    font-size: 20px;
    line-height: 32px;
    color: rgb(69, 69, 69);
}

.cookiebanner__options {
    margin-top: 40px;
}

.cookiebanner__options--hidden {
    display: none;
}

.cookiebanner__option {
    display: flex;
}

.cookiebanner__option + .cookiebanner__option {
    margin-top: 40px;
}

.cookiebanner__checkbox {
    flex-shrink: 0;
    width: 16px;
    height: 16px;
    margin: 8px 8px 0 0;
    border-radius: 4px;
    border: 1px solid rgb(153, 153, 153);
    user-select: none;
    appearance: none;
}

.cookiebanner__checkbox:checked {
    border-color: transparent;
    background: rgb(0, 157, 61) center no-repeat;

    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
    background-size: 100% 100%;
}

.cookiebanner__label {
    display: flex;
    flex-direction: column;
}
.cookiebanner__label-title,
.cookiebanner__label-text {
    font-size: 20px;
    line-height: 32px;
}

.cookiebanner__label-title {
    font-family: Ubuntu Bold, ui-sans-serif, system-ui, -apple-system,
        BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
        'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
    color: rgb(26, 26, 26);
}

.cookiebanner__label-text {
    color: rgb(69, 69, 69);
}

.cookiebanner__label-title {
    font-weight: 700;
}

.cookiebanner__label-text {
    margin: 5px 0 0;
}

.cookiebanner__buttons {
    display: flex;
    align-items: center;
    gap: 32px;
    margin-top: 32px;
}

.cookiebanner__buttons--hidden {
    display: none;
}

.cookiebanner__link {
    color: rgb(0, 159, 227);
    transition: color 0.1s ease-in-out;
    text-decoration: underline;
}

.cookiebanner__link:hover {
    color: rgb(0, 48, 68);
}

button.cookiebanner__button {
    overflow: hidden;
    position: relative;
    border-radius: 4px;
    padding: 16px 24px;
    background: rgb(128, 206, 158);
    color: rgb(255, 255, 255);
    text-decoration: none;
    transition: all 0.1s ease-in-out;
    font-family: Ubuntu Medium, ui-sans-serif, system-ui, -apple-system,
        BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
        'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

button.cookiebanner__button--secondary {
    padding: 15px 23px;
    border: 1px solid rgb(0, 157, 61);
}

button.cookiebanner__button--secondary:hover {
    border-color: rgb(128, 206, 158);
}

button.cookiebanner__button--secondary span {
    color: rgb(0, 157, 61);
}

button.cookiebanner__button span {
    position: relative;
    z-index: 2;
    transition: color 0.1s ease-in-out;
}

button.cookiebanner__button:hover span {
    color: rgb(0, 79, 31);
}

button.cookiebanner__button:active {
    background-color: rgb(0, 126, 49);
    color: rgb(255, 255, 255);
}

button.cookiebanner__button::before {
    position: absolute;
    left: -25%;
    top: 0px;
    z-index: 1;
    height: 100%;
    width: 150%;
    transform: skew(30deg);
    background-color: rgb(0, 157, 61);
    content: '';
    transition: transform 0.36s cubic-bezier(0.3, 1, 0.8, 1);
}

button.cookiebanner__button--secondary::before {
    background-color: rgb(255, 255, 255);
}

button.cookiebanner__button:hover::before {
    transform: translate3d(100%, 0, 0);
}

@media (max-width: 900px) {
    .cookiebanner {
        border-radius: 0;
    }

    .cookiebanner__buttons {
        flex-direction: column;
        align-items: flex-start;
        gap: 24px;
    }
}

@media (max-width: 650px) {
    .cookiebanner {
        padding: 32px;
        flex-direction: column;
    }

    .cookiebanner__header {
        align-self: center;
        margin-right: 0;
        margin-bottom: 16px;
    }

    .cookiebanner__logo {
        width: 120px;
        height: 120px;
    }

    .cookiebanner__options {
        margin-top: 32px;
    }

    .cookiebanner__option + .cookiebanner__option {
        margin-top: 24px;
    }

    .cookiebanner__title {
        margin-bottom: 12px;
        font-size: 20px;
        line-height: 24px;
    }

    .cookiebanner__text,
    .cookiebanner__label-title,
    .cookiebanner__label-text {
        font-size: 18px;
        line-height: 28px;
    }

    button.cookiebanner__buttons {
        margin-top: 24px;
    }

    .cookiebanner__link,
    button.cookiebanner__button {
        font-size: 18px;
        line-height: 22px;
    }
}
