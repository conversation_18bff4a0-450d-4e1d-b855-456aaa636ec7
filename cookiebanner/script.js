function showCookieBanner() {
    var cookiebanner = document.getElementById('cookiebanner')

    setTimeout(function() {
        cookiebanner.classList.remove('cookiebanner--hidden')
    }, 10);
}

function hideCookieBanner() {
    var cookiebanner = document.getElementById('cookiebanner')

    cookiebanner.classList.add('cookiebanner--hidden')
}

function toggleCookieBannerOptions() {
    var cookiebanner = document.getElementById('cookiebanner')
    var options = cookiebanner.querySelector('.cookiebanner__options')
    var buttonWrappers = cookiebanner.querySelectorAll('.cookiebanner__buttons')

    options.classList.toggle('cookiebanner__options--hidden')
    Array.from(buttonWrappers).forEach(function(buttonWrapper) {
        buttonWrapper.classList.toggle('cookiebanner__buttons--hidden')
    })
}
