<div
    id="cookiebanner"
    class="cookiebanner cookiebanner--hidden"
    lang="[#LANGUAGE#]"
    dir="[#TEXTDIRECTION#]"
    ng-non-bindable
    aria-role="alertdialog"
    aria-labelledby="cookiebanner-title"
    aria-describedby="cookiebanner-desc"
>
    <div class="cookiebanner__header">
        <svg
            class="cookiebanner__logo"
            width="150"
            height="150"
            viewBox="0 0 150 150"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M107.6 24L74.6 44.8L41.7 24L19 37.1V94.4L42 107.1V52.3L62 65.5V118.1L74.6 125L87.3 118.1V65.5L107.3 52.3V107.1L130.3 94.4V37.1L107.6 24Z"
                fill="#00AB4E"
            />
            <path
                d="M19 37.1001L42 52.3001V107.1L19 94.4001V37.1001Z"
                fill="url(#paint0_linear_2368_14106)"
            />
            <path
                d="M130.3 37.1001L107.3 52.2001V107L130.3 94.3001V37.1001Z"
                fill="url(#paint1_linear_2368_14106)"
            />
            <defs>
                <linearGradient
                    id="paint0_linear_2368_14106"
                    x1="54.4753"
                    y1="50.3322"
                    x2="26.5872"
                    y2="75.6084"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop offset="0.084363" />
                    <stop offset="0.1111" stop-color="#001B00" />
                    <stop offset="0.2563" stop-color="#004C1A" />
                    <stop offset="0.3997" stop-color="#00692D" />
                    <stop offset="0.538" stop-color="#00813B" />
                    <stop offset="0.6701" stop-color="#009344" />
                    <stop offset="0.7943" stop-color="#009F49" />
                    <stop offset="0.9076" stop-color="#00A74D" />
                    <stop offset="1" stop-color="#00AB4E" />
                </linearGradient>
                <linearGradient
                    id="paint1_linear_2368_14106"
                    x1="94.802"
                    y1="50.2773"
                    x2="122.69"
                    y2="75.5535"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop offset="0.084363" />
                    <stop offset="0.1111" stop-color="#001B00" />
                    <stop offset="0.2563" stop-color="#004C1A" />
                    <stop offset="0.3997" stop-color="#00692D" />
                    <stop offset="0.538" stop-color="#00813B" />
                    <stop offset="0.6701" stop-color="#009344" />
                    <stop offset="0.7943" stop-color="#009F49" />
                    <stop offset="0.9076" stop-color="#00A74D" />
                    <stop offset="1" stop-color="#00AB4E" />
                </linearGradient>
            </defs>
        </svg>
    </div>
    <div class="cookiebanner__details">
        <h2 id="cookiebanner-title" class="cookiebanner__title">[#TITLE#]</h2>
        <p id="cookiebanner-desc" class="cookiebanner__text">[#TEXT#]</p>
        <div class="cookiebanner__buttons">
            <button
                onclick="toggleCookieBannerOptions()"
                class="cookiebanner__link"
            >
                [#DETAILS#]
            </button>
            <button
                id="CybotCookiebotDialogBodyLevelButtonLevelOptinAllowAll"
                class="cookiebanner__button"
            >
                <span>[#ACCEPT#]</span>
            </button>
        </div>
        <div class="cookiebanner__options cookiebanner__options--hidden">
            <div class="cookiebanner__option">
                <input
                    type="checkbox"
                    id="CybotCookiebotDialogBodyLevelButtonNecessary"
                    class="cookiebanner__checkbox"
                    checked="checked"
                    disabled
                />
                <label
                    for="CybotCookiebotDialogBodyLevelButtonNecessary"
                    class="cookiebanner__label"
                >
                    <span class="cookiebanner__label-title">
                        [#COOKIETYPE_NECESSARY_RAW#]
                    </span>
                    <p class="cookiebanner__label-text">
                        [#COOKIETYPEINTRO_NECESSARY#]
                    </p>
                </label>
            </div>
            <div class="cookiebanner__option">
                <input
                    type="checkbox"
                    id="CybotCookiebotDialogBodyLevelButtonPreferences"
                    class="cookiebanner__checkbox"
                />
                <label
                    for="CybotCookiebotDialogBodyLevelButtonPreferences"
                    class="cookiebanner__label"
                >
                    <span class="cookiebanner__label-title">
                        [#COOKIETYPE_PREFERENCE_RAW#]
                    </span>
                    <p class="cookiebanner__label-text">
                        [#COOKIETYPEINTRO_PREFERENCE#]
                    </p>
                </label>
            </div>
            <div class="cookiebanner__option">
                <input
                    type="checkbox"
                    id="CybotCookiebotDialogBodyLevelButtonStatistics"
                    class="cookiebanner__checkbox"
                />
                <label
                    for="CybotCookiebotDialogBodyLevelButtonStatistics"
                    class="cookiebanner__label"
                >
                    <span class="cookiebanner__label-title">
                        [#COOKIETYPE_STATISTICS_RAW#]
                    </span>
                    <p class="cookiebanner__label-text">
                        [#COOKIETYPEINTRO_STATISTICS#]
                    </p>
                </label>
            </div>
            <div class="cookiebanner__option">
                <input
                    type="checkbox"
                    id="CybotCookiebotDialogBodyLevelButtonMarketing"
                    class="cookiebanner__checkbox"
                />
                <label
                    for="CybotCookiebotDialogBodyLevelButtonMarketing"
                    class="cookiebanner__label"
                >
                    <span class="cookiebanner__label-title">
                        [#COOKIETYPE_ADVERTISING_RAW#]
                    </span>
                    <p class="cookiebanner__label-text">
                        [#COOKIETYPEINTRO_ADVERTISING#]
                    </p>
                </label>
            </div>
        </div>
        <div class="cookiebanner__buttons cookiebanner__buttons--hidden">
            <button
                onclick="toggleCookieBannerOptions()"
                class="cookiebanner__link"
            >
                [#DETAILSHIDE#]
            </button>
            <button
                id="CybotCookiebotDialogBodyLevelButtonLevelOptinDeclineAll"
                class="cookiebanner__button cookiebanner__button--secondary"
            >
                <span>[#DECLINE#]</span>
            </button>
            <button
                id="CybotCookiebotDialogBodyLevelButtonLevelOptinAllowallSelection"
                class="cookiebanner__button"
            >
                <span>[#LEVELOPTIN_ALLOW_SELECTION#]</span>
            </button>
        </div>
    </div>
</div>
