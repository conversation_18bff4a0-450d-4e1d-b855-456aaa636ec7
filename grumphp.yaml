grumphp:
  ascii:
    failed: ~
    succeeded: ~
  hide_circumvention_tip: true
  process_timeout: 120
  stop_on_failure: true
  tasks:
    git_blacklist:
      # catch not allowed keywords
      match_word: true
      keywords:
        - "<<<<<<<"
        - "======="
        - "DebuggerUtility"
        - "console.log("
        - "dd("
        - "die("
        - "die;"
        - "dump("
        - "exit("
        - "exit;"
        - "phpinfo"
        - "phpinfo("
        - "print_r("
        - "var_dump("
      triggered_by: ['php', 'js', 'html', 'phtml', 'twig']
    phplint:
      ignore_patterns:
        - '/^(?!modules)/'
    phpstan:
      configuration: 'phpstan.neon'
      memory_limit: "-1"
      triggered_by: ['php']
      ignore_patterns:
        - '/^(?!modules)/'
    twigcs:
      path: 'templates'
      ruleset: 'App\TwigCs\TwigCsRuleset'
      severity: 'error'
    composer: ~
