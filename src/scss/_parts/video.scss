.video__wrapper {
    @apply relative h-0 w-full pt-[56.25%];

    iframe,
    .video__placeholder {
        @apply absolute top-0 left-0 h-full w-full rounded;
    }

    iframe {
        @apply z-2;

        & + .video__placeholder {
            @apply hidden;
        }
    }
}

.video__placeholder {
    @apply z-1;
}

.video__placeholder-image-wrapper {
    @apply absolute top-1/2 left-1/2 h-full w-full -translate-x-1/2 -translate-y-1/2 overflow-hidden rounded bg-basic-white;
}
.video__placeholder-image {
    @apply absolute top-1/2 left-1/2 block h-[125%] w-[125%] -translate-x-1/2 -translate-y-1/2 object-cover opacity-75;

    filter: blur(20px);
}

.video__placeholder-details {
    @apply absolute top-[calc(50%+1rem)] left-1/2 flex w-full -translate-x-1/2 -translate-y-1/2 flex-col items-center;
}

.video__placeholder-play {
    @apply mb-4 flex h-14 w-14 items-center justify-center rounded-full bg-primary-500 text-28 text-basic-white;
    @apply md:mb-8;

    @screen md {
        @apply h-20 w-20 text-40;
    }
}

.video__placeholder-message {
    @apply w-full px-4 text-center text-14 leading-22;

    @screen md {
        @apply text-16;
    }
}

.video__placeholder-button {
    @apply absolute top-0 left-0 z-10 h-full w-full rounded;
}
