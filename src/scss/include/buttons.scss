.btn {
    @apply overflow-hidden text-20 font-medium leading-23 tracking-normal;
    @apply relative cursor-pointer rounded px-6 py-4 disabled:cursor-not-allowed;
    display: inline-block;

    &[href][disabled] {
        @apply bg-tertiary-300 text-tertiary-700;
        @apply hover:bg-tertiary-300 hover:text-tertiary-700;
        @apply focus-visible:bg-tertiary-300 focus-visible:text-tertiary-700;
        @apply active:bg-tertiary-300 active:text-tertiary-700;
        @apply cursor-not-allowed;
    }

    .icon {
        @apply scale-[1.25];
    }

    &.has-icon-left {
        .icon {
            @apply mr-2;
        }
    }

    &.has-icon-right {
        .icon {
            @apply ml-2;
        }
    }

    &-primary {
        @apply bg-primary-500 text-white;
        @apply hover:bg-primary-300 hover:text-primary-900;
        @apply focus-visible:bg-primary-300 focus-visible:text-primary-900;
        @apply active:bg-primary-700 active:text-white;
        @apply disabled:bg-tertiary-300 disabled:text-tertiary-700;
    }

    &-secondary {
        @apply bg-secondary-500 text-white;
        @apply hover:bg-secondary-300 hover:text-secondary-900;
        @apply focus-visible:bg-secondary-300 focus-visible:text-secondary-900;
        @apply active:bg-secondary-700 active:text-white;
        @apply disabled:bg-tertiary-300 disabled:text-tertiary-700;
    }

    &-white {
        @apply bg-transparent text-base;
        @apply disabled:opacity-50;
    }
}

.btn-text {
    @apply underline decoration-from-font underline-offset-4 transition-colors;

    &-primary {
        @apply text-primary-500;
        @apply hover:text-primary-900;
        @apply focus-visible:text-primary-900;
        @apply active:text-primary-900;
        @apply disabled:text-tertiary-500;
    }

    &-secondary {
        @apply text-secondary-500;
        @apply hover:text-secondary-900;
        @apply focus-visible:text-secondary-900;
        @apply active:text-secondary-900;
        @apply disabled:text-tertiary-500;
    }

    &[disabled] {
        @apply cursor-not-allowed;
    }
}

.btn-animation {
    @apply bg-tertiary-500;

    &.btn-primary {
        @apply bg-primary-300 text-white;
        @apply hover:bg-primary-300 hover:text-primary-900;
        @apply active:bg-primary-700 active:text-white;
        @apply disabled:bg-tertiary-300 disabled:text-tertiary-700;

        &::before {
            @apply bg-primary-500;
        }
    }

    &.btn-secondary {
        @apply bg-secondary-300 text-white;
        @apply hover:bg-secondary-300 hover:text-secondary-900;
        @apply active:bg-secondary-700 active:text-white;
        @apply disabled:bg-tertiary-300 disabled:text-tertiary-700;

        &::before {
            @apply bg-secondary-500;
        }
    }

    &::before {
        @apply absolute -left-[25%] top-0 z-1 h-full w-[150%] skew-x-[30deg] bg-tertiary-300 content-[''];
        transition: transform 360ms cubic-bezier(0.3, 1, 0.8, 1);
    }

    &:hover::before {
        transform: translate3d(100%, 0, 0);
    }

    span,
    button.fui-submit {
        @apply relative z-2;
    }

    &[disabled]::before {
        @apply bg-tertiary-300 text-tertiary-700;
        transform: unset;
    }
}
