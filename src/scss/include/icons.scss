@font-face {
    font-family: 'Icomoon';
    src: url('/dist/fonts/icomoon.eot');
    src: url('/dist/fonts/icomoon.eot#iefix') format('embedded-opentype'),
        url('/dist/fonts/icomoon.woff2') format('woff2'),
        url('/dist/fonts/icomoon.ttf') format('truetype'),
        url('/dist/fonts/icomoon.woff') format('woff'),
        url('/dist/fonts/icomoon.svg#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

.icon {
    font-family: 'Icomoon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-calendar_month_google_fonts:before {
  content: "\e95e";
}
.icon-search_insights_google_fonts:before {
  content: "\e95f";
}
.icon-compost_google_fonts:before {
  content: "\e960";
}
.icon-apartment_google_fonts:before {
  content: "\e935";
}
.icon-arrow_back_google_fonts:before {
  content: "\e936";
}
.icon-arrow_forward_google_fonts:before {
  content: "\e937";
}
.icon-auto_delete_google_fonts:before {
  content: "\e93a";
}
.icon-check_circle_google_fonts:before {
  content: "\e93b";
}
.icon-close_google_fonts:before {
  content: "\e93c";
}
.icon-co2_google_fonts:before {
  content: "\e93d";
}
.icon-cycle_google_fonts:before {
  content: "\e93e";
}
.icon-delete_google_fonts:before {
  content: "\e93f";
}
.icon-delete_forever_google_fonts:before {
  content: "\e940";
}
.icon-delete_sweep_google_fonts:before {
  content: "\e941";
}
.icon-diversity_3_google_fonts:before {
  content: "\e942";
}
.icon-done_google_fonts:before {
  content: "\e943";
}
.icon-eco_google_fonts:before {
  content: "\e944";
}
.icon-emoji_objects_google_fonts:before {
  content: "\e945";
}
.icon-favorite_google_fonts:before {
  content: "\e946";
}
.icon-home_google_fonts:before {
  content: "\e947";
}
.icon-key_google_fonts:before {
  content: "\e948";
}
.icon-local_shipping_google_fonts:before {
  content: "\e949";
}
.icon-location_on_google_fonts:before {
  content: "\e94a";
}
.icon-lock_google_fonts:before {
  content: "\e94b";
}
.icon-person_google_fonts:before {
  content: "\e94c";
}
.icon-psychology_alt_google_fonts:before {
  content: "\e94d";
}
.icon-recycling_google_fonts:before {
  content: "\e94e";
}
.icon-savings_google_fonts:before {
  content: "\e94f";
}
.icon-speed_google_fonts:before {
  content: "\e950";
}
.icon-star_google_fonts:before {
  content: "\e951";
}
.icon-support_agent_google_fonts:before {
  content: "\e952";
}
.icon-water_drop_google_fonts:before {
  content: "\e953";
}
.icon-door_front_google_fonts:before {
  content: "\e954";
}
.icon-euro_symbol_google_fonts:before {
  content: "\e955";
}
.icon-leaderboard_google_fonts:before {
  content: "\e956";
}
.icon-partner_exchange_google_fonts:before {
  content: "\e957";
}
.icon-sell_google_fonts:before {
  content: "\e958";
}
.icon-settings_account_box_google_fonts:before {
  content: "\e959";
}
.icon-trending_down_google_fonts:before {
  content: "\e95a";
}
.icon-visibility_google_fonts:before {
  content: "\e95b";
}
.icon-forward_circle_google_fonts:before {
  content: "\e95c";
}
.icon-note_stack_google_fonts:before {
  content: "\e95d";
}
.icon-link:before {
  content: "\e90f";
}
.icon-whatsapp:before {
  content: "\e925";
}
.icon-container:before {
  content: "\e930";
  color: #646464;
}
.icon-calendar:before {
  content: "\e931";
}
.icon-length:before {
  content: "\e932";
}
.icon-advice:before {
  content: "\e933";
}
.icon-trash:before {
  content: "\e934";
}
.icon-arrow-circle-down:before {
  content: "\e900";
}
.icon-arrow-down:before {
  content: "\e901";
}
.icon-arrow-left:before {
  content: "\e902";
}
.icon-arrow-right:before {
  content: "\e903";
}
.icon-arrow-up:before {
  content: "\e904";
}
.icon-at:before {
  content: "\e905";
}
.icon-attachment:before {
  content: "\e906";
}
.icon-check:before {
  content: "\e907";
}
.icon-chevron-down:before {
  content: "\e908";
}
.icon-chevron-left:before {
  content: "\e909";
}
.icon-chevron-right:before {
  content: "\e90a";
}
.icon-chevron-up:before {
  content: "\e90b";
}
.icon-clock:before {
  content: "\e90c";
}
.icon-close:before {
  content: "\e90d";
}
.icon-direction:before {
  content: "\e90e";
}
.icon-download:before {
  content: "\e938";
}
.icon-edit:before {
  content: "\e910";
}
.icon-error:before {
  content: "\e911";
}
.icon-facebook-outline:before {
  content: "\e912";
}
.icon-facebook:before {
  content: "\e913";
}
.icon-heart:before {
  content: "\e914";
}
.icon-help:before {
  content: "\e915";
}
.icon-info:before {
  content: "\e916";
}
.icon-instagram-outline:before {
  content: "\e917";
}
.icon-instagram:before {
  content: "\e918";
}
.icon-linkedin-outline:before {
  content: "\e919";
}
.icon-linkedin:before {
  content: "\e91a";
}
.icon-location:before {
  content: "\e91b";
}
.icon-mail:before {
  content: "\e91c";
}
.icon-menu:before {
  content: "\e91d";
}
.icon-minus:before {
  content: "\e91e";
}
.icon-mute:before {
  content: "\e91f";
}
.icon-person:before {
  content: "\e920";
}
.icon-phone:before {
  content: "\e921";
}
.icon-play:before {
  content: "\e922";
}
.icon-plus:before {
  content: "\e923";
}
.icon-recycling:before {
  content: "\e924";
}
.icon-savings:before {
  content: "\e939";
}
.icon-search:before {
  content: "\e926";
}
.icon-send:before {
  content: "\e927";
}
.icon-share:before {
  content: "\e928";
}
.icon-sound:before {
  content: "\e929";
}
.icon-support:before {
  content: "\e92a";
}
.icon-trash-time:before {
  content: "\e92b";
}
.icon-upload:before {
  content: "\e92c";
}
.icon-verified:before {
  content: "\e92d";
}
.icon-warning:before {
  content: "\e92e";
}
.icon-youtube:before {
  content: "\e92f";
}
