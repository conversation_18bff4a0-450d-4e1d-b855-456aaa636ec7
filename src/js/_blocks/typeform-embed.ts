import { createWidget } from '@typeform/embed'
import '@typeform/embed/build/css/widget.css'

const urlParams = new URLSearchParams(window.location.search)
const hiddenFields = {}
const allowedParams = ['gclid', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content']

allowedParams.forEach(param => {
    const value = urlParams.get(param)

    if (value) hiddenFields[param] = value

})

const blocks = document.querySelectorAll<HTMLElement>('.typeform-embed')

blocks.forEach((block) => {
    const typeformId = block.dataset.formId

    if (!typeformId) return

    createWidget(
        typeformId,
        {
            container: block,
            hideFooter: false,
            hideHeaders: false,
            hidden: hiddenFields,
        }
    )
})
