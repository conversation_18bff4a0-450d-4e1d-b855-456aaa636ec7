.PHONY: build dev composer apply craft pull up install env

OP := $(shell command -v op 2> /dev/null)

build: up
	ddev exec npm run build
dev:
	ddev launch
	ddev exec npm run serve
composer: up
	ddev composer \
		$(filter-out $@,$(MAKECMDGOALS))
craft: up
	ddev exec php craft \
		$(filter-out $@,$(MAKECMDGOALS))
apply: up
	ddev exec php craft project-config/apply
	ddev exec php craft clear-caches/all
pull: up
	ddev sync \
		$(filter-out $@,$(MAKECMDGOALS))
	ddev exec php craft migrate/all --interactive=0 --no-backup=1
	ddev exec php craft project-config/apply
	ddev exec php craft clear-caches/all
install: up build
	ddev exec php craft setup/app-id \
		$(filter-out $@,$(MAKECMDGOALS))
	ddev exec php craft setup/security-key \
		$(filter-out $@,$(MAKECMDGOALS))
	ddev exec php craft install \
		$(filter-out $@,$(MAKECMDGOALS))
	ddev exec php craft plugin/install admin-bar
	ddev exec php craft plugin/install cp-field-inspect
	ddev exec php craft plugin/install dumper
	ddev exec php craft plugin/install image-resizer
	ddev exec php craft plugin/install neo
	ddev exec php craft plugin/install quick-field
	ddev exec php craft plugin/install redactor
	ddev exec php craft plugin/install retour
	ddev exec php craft plugin/install seo
	ddev exec php craft plugin/install smith
	ddev exec php craft plugin/install sprig
	ddev exec php craft plugin/install super-table
	ddev exec php craft plugin/install templatecomments
	ddev exec php craft plugin/install typedlinkfield
	ddev exec php craft plugin/install vite
env:
ifndef OP
	$(error "1password cli is not available please install using https://developer.1password.com/docs/cli/get-started")
else
	op inject -i .env.example -o .env
endif
up:
	if [ ! "$$(ddev describe | grep OK)" ]; then \
		ddev auth ssh; \
		ddev start; \
		ddev sync; \
		ddev composer install; \
		ddev exec npm install; \
		ddev exec npm run build; \
	fi
%:
	@:
# ref: https://stackoverflow.com/questions/6273608/how-to-pass-argument-to-makefile-from-command-line
