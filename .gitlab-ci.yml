include:
  - project: 'platforms/craftcms/tools/gitlab-ci'
    file: '.gitlab-ci-template.yml'

'Deploy test':
  extends: .deploy-source
  environment:
    name: test
    url: https://test.milieuservicenederland.nl.kiwicloud.nl/
  variables:
    REMOTE_USER: 'test'
    REMOTE_HOST: 'vps0493.rk.prolocation.net'
  only:
    - master
    - tags

'Deploy accept':
  extends: .deploy-source
  environment:
    name: accept
    url: https://accept.milieuservicenederland.nl.kiwicloud.nl/
  variables:
    REMOTE_USER: 'accept'
    REMOTE_HOST: 'vps0493.rk.prolocation.net'
  only:
    - tags

'Deploy production':
  extends: .deploy-source
  environment:
    name: main
    url: https://www.milieuservicenederland.nl/
  variables:
    REMOTE_USER: 'production'
    REMOTE_HOST: 'vps0494.rk.prolocation.net'
  only:
    - tags
  when: manual