{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "eed8ee3efebf1483025ee9809fa72fcb", "packages": [{"name": "automattic/woocommerce", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/woocommerce/wc-api-php.git", "reference": "d3b292f04c0b3b21dced691ebad8be073a83b4ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/woocommerce/wc-api-php/zipball/d3b292f04c0b3b21dced691ebad8be073a83b4ad", "reference": "d3b292f04c0b3b21dced691ebad8be073a83b4ad", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">= 7.1.0"}, "require-dev": {"overtrue/phplint": "7.4.x-dev", "phpunit/phpunit": "^8", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"Automattic\\WooCommerce\\": ["src/WooCommerce"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PHP wrapper for the WooCommerce REST API", "keywords": ["api", "woocommerce"], "support": {"issues": "https://github.com/woocommerce/wc-api-php/issues", "source": "https://github.com/woocommerce/wc-api-php/tree/3.1.0"}, "time": "2022-03-18T21:46:17+00:00"}, {"name": "cakephp/core", "version": "3.9.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/core/zipball/4b45635d6be8a98be175fea9c9f575de29d515b3", "reference": "4b45635d6be8a98be175fea9c9f575de29d515b3", "shasum": ""}, "require": {"cakephp/utility": "^3.6.0", "php": ">=5.6.0"}, "suggest": {"cakephp/cache": "To use Configure::store() and restore().", "cakephp/event": "To use PluginApplicationInterface or plugin applications."}, "type": "library", "autoload": {"psr-4": {"Cake\\Core\\": "."}, "files": ["functions.php"]}, "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/core/graphs/contributors"}], "description": "CakePHP Framework Core classes", "homepage": "https://cakephp.org", "keywords": ["cakephp", "core", "framework"], "time": "2020-10-21T21:21:05+00:00"}, {"name": "cakephp/utility", "version": "3.9.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/utility/zipball/e655b399b7492e517caef52fb87af9db10543112", "reference": "e655b399b7492e517caef52fb87af9db10543112", "shasum": ""}, "require": {"cakephp/core": "^3.6.0", "php": ">=5.6.0"}, "suggest": {"ext-intl": "To use Text::transliterate() or Text::slug()", "lib-ICU": "To use Text::transliterate() or Text::slug()"}, "type": "library", "autoload": {"psr-4": {"Cake\\Utility\\": "."}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/utility/graphs/contributors"}], "description": "CakePHP Utility classes such as Inflector, String, Hash, and Security", "homepage": "https://cakephp.org", "keywords": ["cakephp", "hash", "inflector", "security", "string", "utility"], "time": "2020-08-18T13:55:20+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "3.2.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/dbal": "<4.0.0 || >=5.0.0"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "time": "2024-02-09T16:56:22+00:00"}, {"name": "cebe/markdown", "version": "1.2.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/9bac5e971dd391e2802dca5400bbeacbaea9eb86", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "time": "2018-03-26T11:24:36+00:00"}, {"name": "commerceguys/addressing", "version": "v1.4.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/commerceguys/addressing/zipball/406c7b5f0fbe4f6a64155c0fe03b1adb34d01308", "reference": "406c7b5f0fbe4f6a64155c0fe03b1adb34d01308", "shasum": ""}, "require": {"doctrine/collections": "^1.2 || ^2.0", "php": ">=7.3"}, "suggest": {"symfony/validator": "to validate addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"CommerceGuys\\Addressing\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "description": "Addressing library powered by CLDR and Google's address data.", "keywords": ["address", "internationalization", "localization", "postal"], "time": "2023-02-15T10:11:14+00:00"}, {"name": "composer/ca-bundle", "version": "1.5.6", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "f65c239c970e7f072f067ab78646e9f0b2935175"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/f65c239c970e7f072f067ab78646e9f0b2935175", "reference": "f65c239c970e7f072f067ab78646e9f0b2935175", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8 || ^9", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/process": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.5.6"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-03-06T14:30:56+00:00"}, {"name": "composer/class-map-generator", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/composer/class-map-generator.git", "reference": "134b705ddb0025d397d8318a75825fe3c9d1da34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/class-map-generator/zipball/134b705ddb0025d397d8318a75825fe3c9d1da34", "reference": "134b705ddb0025d397d8318a75825fe3c9d1da34", "shasum": ""}, "require": {"composer/pcre": "^2.1 || ^3.1", "php": "^7.2 || ^8.0", "symfony/finder": "^4.4 || ^5.3 || ^6 || ^7"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-deprecation-rules": "^1 || ^2", "phpstan/phpstan-phpunit": "^1 || ^2", "phpstan/phpstan-strict-rules": "^1.1 || ^2", "phpunit/phpunit": "^8", "symfony/filesystem": "^5.4 || ^6"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\ClassMapGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Utilities to scan PHP code and generate class maps.", "keywords": ["classmap"], "support": {"issues": "https://github.com/composer/class-map-generator/issues", "source": "https://github.com/composer/class-map-generator/tree/1.6.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-03-24T13:50:44+00:00"}, {"name": "composer/composer", "version": "2.8.9", "source": {"type": "git", "url": "https://github.com/composer/composer.git", "reference": "b4e6bff2db7ce756ddb77ecee958a0f41f42bd9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/b4e6bff2db7ce756ddb77ecee958a0f41f42bd9d", "reference": "b4e6bff2db7ce756ddb77ecee958a0f41f42bd9d", "shasum": ""}, "require": {"composer/ca-bundle": "^1.5", "composer/class-map-generator": "^1.4.0", "composer/metadata-minifier": "^1.0", "composer/pcre": "^2.2 || ^3.2", "composer/semver": "^3.3", "composer/spdx-licenses": "^1.5.7", "composer/xdebug-handler": "^2.0.2 || ^3.0.3", "justinrainbow/json-schema": "^6.3.1", "php": "^7.2.5 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "react/promise": "^2.11 || ^3.2", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.2", "seld/signal-handler": "^2.0", "symfony/console": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/filesystem": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/finder": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/polyfill-php73": "^1.24", "symfony/polyfill-php80": "^1.24", "symfony/polyfill-php81": "^1.24", "symfony/process": "^5.4.35 || ^6.3.12 || ^7.0.3"}, "require-dev": {"phpstan/phpstan": "^1.11.8", "phpstan/phpstan-deprecation-rules": "^1.2.0", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.0", "phpstan/phpstan-symfony": "^1.4.0", "symfony/phpunit-bridge": "^6.4.3 || ^7.0.1"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"phpstan": {"includes": ["phpstan/rules.neon"]}, "branch-alias": {"dev-main": "2.8-dev"}}, "autoload": {"psr-4": {"Composer\\": "src/Composer/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/composer/issues", "security": "https://github.com/composer/composer/security/policy", "source": "https://github.com/composer/composer/tree/2.8.9"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-05-13T12:01:37+00:00"}, {"name": "composer/metadata-minifier", "version": "1.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/metadata-minifier/zipball/c549d23829536f0d0e984aaabbf02af91f443207", "reference": "c549d23829536f0d0e984aaabbf02af91f443207", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\MetadataMinifier\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Small utility library that handles metadata minification and expansion.", "keywords": ["composer", "compression"], "time": "2021-04-07T13:37:33+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "time": "2024-11-12T16:29:46+00:00"}, {"name": "composer/semver", "version": "3.4.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "time": "2024-09-19T14:15:21+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.9", "source": {"type": "git", "url": "https://github.com/composer/spdx-licenses.git", "reference": "edf364cefe8c43501e21e88110aac10b284c3c9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/edf364cefe8c43501e21e88110aac10b284c3c9f", "reference": "edf364cefe8c43501e21e88110aac10b284c3c9f", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/spdx-licenses/issues", "source": "https://github.com/composer/spdx-licenses/tree/1.5.9"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-05-12T21:07:07+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "time": "2024-05-06T16:37:16+00:00"}, {"name": "craftcms/cms", "version": "4.15.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/cms/zipball/f997db5601d5cd5b631ea5e5b4ff24bbc8e23707", "reference": "f997db5601d5cd5b631ea5e5b4ff24bbc8e23707", "shasum": ""}, "require": {"commerceguys/addressing": "^1.2", "composer/composer": "^2.7.0", "craftcms/plugin-installer": "~1.6.0", "craftcms/server-check": "~2.1.2", "creocoder/yii2-nested-sets": "~0.9.0", "elvanto/litemoji": "~4.3.0", "enshrined/svg-sanitize": "~0.19.0", "ext-bcmath": "*", "ext-curl": "*", "ext-dom": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-zip": "*", "guzzlehttp/guzzle": "^7.2.0", "illuminate/collections": "^9.1.0", "mikehaertl/php-shellcommand": "^1.6.3", "moneyphp/money": "^4.0", "monolog/monolog": "^2.3", "php": "^8.0.2", "pixelandtonic/imagine": "~*******", "samdark/yii2-psr-log-target": "^1.1.3", "seld/cli-prompt": "^1.0.4", "symfony/http-client": "^6.0.3|^7.0", "symfony/var-dumper": "^5.0|^6.0", "symfony/yaml": "^5.2.3", "theiconic/name-parser": "^1.2", "twig/twig": "~3.15.0", "voku/stringy": "^6.4.0", "webonyx/graphql-php": "~14.11.10", "yiisoft/yii2": "~********", "yiisoft/yii2-debug": "~********", "yiisoft/yii2-queue": "~2.3.2", "yiisoft/yii2-symfonymailer": "^2.0.0"}, "provide": {"bower-asset/inputmask": "5.0.9", "bower-asset/jquery": "3.6.1", "bower-asset/punycode": "^1.4", "bower-asset/yii2-pjax": "~2.0.1", "yii2tech/ar-softdelete": "1.0.4"}, "suggest": {"ext-exif": "Adds support for parsing image EXIF data.", "ext-iconv": "Adds support for more character encodings than PHP’s built-in mb_convert_encoding() function, which Craft will take advantage of when converting strings to UTF-8.", "ext-imagick": "Adds support for more image processing formats and options."}, "type": "library", "autoload": {"psr-4": {"craft\\": "src/", "yii2tech\\ar\\softdelete\\": "lib/ar-softdelete/src/"}}, "license": ["proprietary"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Craft CMS", "homepage": "https://craftcms.com", "keywords": ["cms", "craftcms", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/cms/issues?state=open", "forum": "https://craftcms.stackexchange.com/", "source": "https://github.com/craftcms/cms", "docs": "https://craftcms.com/docs/4.x/", "rss": "https://github.com/craftcms/cms/releases.atom"}, "time": "2025-05-14T23:50:17+00:00"}, {"name": "craftcms/element-api", "version": "4.2.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/element-api/zipball/d102354a48f0cae0f46773978003536573f1e089", "reference": "d102354a48f0cae0f46773978003536573f1e089", "shasum": ""}, "require": {"craftcms/cms": "^4.3.0|^5.0.0-beta.1", "league/fractal": "^0.20.1"}, "type": "craft-plugin", "extra": {"name": "Element API", "handle": "element-api", "documentationUrl": "https://github.com/craftcms/element-api/blob/v2/README.md"}, "autoload": {"psr-4": {"craft\\elementapi\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Create a JSON API for your elements in Craft", "keywords": ["api", "cms", "craftcms", "json", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/element-api/issues?state=open", "source": "https://github.com/craftcms/element-api", "docs": "https://github.com/craftcms/element-api/blob/v2/README.md", "rss": "https://github.com/craftcms/element-api/commits/v2.atom"}, "time": "2025-01-20T18:58:09+00:00"}, {"name": "craftcms/feed-me", "version": "5.11.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/feed-me/zipball/a18d289019dc0e91c822ca7d29b0a35e467e7ca0", "reference": "a18d289019dc0e91c822ca7d29b0a35e467e7ca0", "shasum": ""}, "require": {"cakephp/utility": "^3.3.12", "craftcms/cms": "^4.6.0", "jakeasmith/http_build_url": "^1.0", "league/csv": "^8.2 || ^9.0", "nesbot/carbon": "^1.22 || ^2.10", "php": "^8.0.2", "seld/jsonlint": "^1.7"}, "type": "craft-plugin", "extra": {"name": "Feed Me", "handle": "feed-me", "documentationUrl": "https://docs.craftcms.com/feed-me/v4/"}, "autoload": {"psr-4": {"craft\\feedme\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Import content from XML, RSS, CSV or JSON feeds into entries, categories, Craft Commerce products, and more.", "keywords": ["cms", "craft", "craftcms", "feed me"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/feed-me/issues?state=open", "source": "https://github.com/craftcms/feed-me", "docs": "https://docs.craftcms.com/feed-me/v4/", "rss": "https://github.com/craftcms/feed-me/commits/master.atom"}, "time": "2025-03-15T04:05:40+00:00"}, {"name": "craftcms/html-field", "version": "2.2.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/html-field/zipball/b28e5d27ce38874111597e32782f1ce95f4de16a", "reference": "b28e5d27ce38874111597e32782f1ce95f4de16a", "shasum": ""}, "require": {"craftcms/cms": "^4.2.0", "league/html-to-markdown": "^5.1", "php": "^8.0.2"}, "type": "library", "autoload": {"psr-4": {"craft\\htmlfield\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Base class for Craft CMS field types with HTML values.", "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/html-field/issues?state=open", "source": "https://github.com/craftcms/html-field", "docs": "https://github.com/craftcms/html-field/blob/main/README.md", "rss": "https://github.com/craftcms/html-field/commits/main.atom"}, "time": "2025-02-14T19:24:58+00:00"}, {"name": "craftcms/plugin-installer", "version": "1.6.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/plugin-installer/zipball/bd1650e8da6d5ca7a8527068d3e51c34bc7b6b4f", "reference": "bd1650e8da6d5ca7a8527068d3e51c34bc7b6b4f", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.4"}, "type": "composer-plugin", "extra": {"class": "craft\\composer\\Plugin"}, "autoload": {"psr-4": {"craft\\composer\\": "src/"}}, "license": ["MIT"], "description": "Craft CMS Plugin Installer", "homepage": "https://craftcms.com/", "keywords": ["cms", "composer", "craftcms", "installer", "plugin"], "time": "2023-02-22T13:17:00+00:00"}, {"name": "craftcms/redactor", "version": "3.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/redactor/zipball/b12a0fbd1b0b605e2c64032152c99080b50d62a4", "reference": "b12a0fbd1b0b605e2c64032152c99080b50d62a4", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0-alpha.1", "craftcms/html-field": "^2.0.0", "php": "^8.0.2"}, "type": "craft-plugin", "extra": {"name": "Redactor", "handle": "redactor", "documentationUrl": "https://github.com/craftcms/redactor/blob/v2/README.md"}, "autoload": {"psr-4": {"craft\\redactor\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Edit rich text content in Craft CMS using Redactor by <PERSON>mperavi.", "keywords": ["cms", "craftcms", "html", "redactor", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/redactor/issues?state=open", "source": "https://github.com/craftcms/redactor", "docs": "https://github.com/craftcms/redactor/blob/v2/README.md", "rss": "https://github.com/craftcms/redactor/commits/v2.atom"}, "time": "2024-06-12T16:48:11+00:00"}, {"name": "craftcms/server-check", "version": "2.1.10", "source": {"type": "git", "url": "https://github.com/craftcms/server-check.git", "reference": "fbf31237d80d0fcf3615c0d405de346839c896c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/server-check/zipball/fbf31237d80d0fcf3615c0d405de346839c896c4", "reference": "fbf31237d80d0fcf3615c0d405de346839c896c4", "shasum": ""}, "type": "library", "autoload": {"classmap": ["server/requirements"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Craft CMS Server Check", "homepage": "https://craftcms.com/", "keywords": ["cms", "craftcms", "requirements", "yii2"], "support": {"docs": "https://github.com/craftcms/docs", "email": "<EMAIL>", "forum": "https://craftcms.stackexchange.com/", "issues": "https://github.com/craftcms/server-check/issues?state=open", "rss": "https://github.com/craftcms/server-check/releases.atom", "source": "https://github.com/craftcms/server-check"}, "time": "2025-02-11T20:01:50+00:00"}, {"name": "creocoder/yii2-nested-sets", "version": "0.9.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/creocoder/yii2-nested-sets/zipball/cb8635a459b6246e5a144f096b992dcc30cf9954", "reference": "cb8635a459b6246e5a144f096b992dcc30cf9954", "shasum": ""}, "require": {"yiisoft/yii2": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"creocoder\\nestedsets\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The nested sets behavior for the Yii framework", "keywords": ["nested sets", "yii2"], "time": "2015-01-27T10:53:51+00:00"}, {"name": "davechild/textstatistics", "version": "1.0.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/DaveChild/Text-Statistics/zipball/e83d5f82726db80e662ae305fce3b3c41299b4f5", "reference": "e83d5f82726db80e662ae305fce3b3c41299b4f5", "shasum": ""}, "require": {"php": ">=7.2.0"}, "suggest": {"ext-bcmath": "More accurate floating point calculations.", "ext-mbstring": "Handle multi-byte text properly."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"DaveChild\\TextStatistics": "src"}}, "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.addedbytes.com/", "role": "Developer"}], "description": "PHP package to measure the readability of text according to various algorithms.", "homepage": "https://github.com/DaveChild/Text-Statistics", "time": "2018-08-21T08:17:10+00:00"}, {"name": "defuse/php-encryption", "version": "v2.4.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/defuse/php-encryption/zipball/f53396c2d34225064647a05ca76c1da9d99e5828", "reference": "f53396c2d34225064647a05ca76c1da9d99e5828", "shasum": ""}, "require": {"ext-openssl": "*", "paragonie/random_compat": ">= 2", "php": ">=5.6.0"}, "bin": ["bin/generate-defuse-key"], "type": "library", "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "Secure PHP Encryption Library", "keywords": ["aes", "authenticated encryption", "cipher", "crypto", "cryptography", "encrypt", "encryption", "openssl", "security", "symmetric key cryptography"], "time": "2023-06-19T06:10:36+00:00"}, {"name": "doctrine/collections", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2eb07e5953eed811ce1b309a7478a3b236f2273d", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d", "shasum": ""}, "require": {"doctrine/deprecations": "^1", "php": "^8.1", "symfony/polyfill-php84": "^1.30"}, "require-dev": {"doctrine/coding-standard": "^12", "ext-json": "*", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.3.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcollections", "type": "tidelift"}], "time": "2025-03-22T10:17:19+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "time": "2024-02-05T11:56:58+00:00"}, {"name": "dompdf/dompdf", "version": "v2.0.8", "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/dompdf/zipball/c20247574601700e1f7c8dab39310fca1964dc52", "reference": "c20247574601700e1f7c8dab39310fca1964dc52", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "masterminds/html5": "^2.0", "phenx/php-font-lib": ">=0.5.4 <1.0.0", "phenx/php-svg-lib": ">=0.5.2 <1.0.0", "php": "^7.1 || ^8.0"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}, "type": "library", "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "license": ["LGPL-2.1"], "authors": [{"name": "The Dompdf Community", "homepage": "https://github.com/dompdf/dompdf/blob/master/AUTHORS.md"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "time": "2024-04-29T13:06:17+00:00"}, {"name": "doublesecretagency/craft-inventory", "version": "3.2.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/doublesecretagency/craft-inventory/zipball/aee2af99df55f847f357173ab5ce9c594d90494d", "reference": "aee2af99df55f847f357173ab5ce9c594d90494d", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0"}, "type": "craft-plugin", "extra": {"name": "Inventory", "handle": "inventory", "schemaVersion": "3.0.0", "changelogUrl": "https://raw.githubusercontent.com/doublesecretagency/craft-inventory/v3/CHANGELOG.md", "class": "doublesecretagency\\inventory\\Inventory"}, "autoload": {"psr-4": {"doublesecretagency\\inventory\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Double Secret Agency", "homepage": "https://www.doublesecretagency.com/plugins"}], "description": "Take stock of your field usage.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "fields", "inventory"], "support": {"docs": "https://github.com/doublesecretagency/craft-inventory/blob/v3/README.md", "issues": "https://github.com/doublesecretagency/craft-inventory/issues"}, "time": "2024-03-19T22:15:59+00:00"}, {"name": "egulias/email-validator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00"}, {"name": "elvanto/litemoji", "version": "4.3.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/elvanto/litemoji/zipball/f13cf10686f7110a3b17d09de03050d0708840b8", "reference": "f13cf10686f7110a3b17d09de03050d0708840b8", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=7.3"}, "type": "library", "autoload": {"psr-4": {"LitEmoji\\": "src/"}}, "license": ["MIT"], "description": "A PHP library simplifying the conversion of unicode, HTML and shortcode emoji.", "keywords": ["emoji", "php-emoji"], "time": "2022-10-28T02:32:19+00:00"}, {"name": "enshrined/svg-sanitize", "version": "0.19.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/e95cd17be68e45f523cbfb0fe50cdd891b0cf20e", "reference": "e95cd17be68e45f523cbfb0fe50cdd891b0cf20e", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "time": "2024-06-18T10:27:15+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.18.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/cb56001e54359df7ae76dc522d08845dc741621b", "reference": "cb56001e54359df7ae76dc522d08845dc741621b", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2024-11-01T03:51:45+00:00"}, {"name": "fakerphp/faker", "version": "v1.24.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "time": "2024-11-21T13:46:39+00:00"}, {"name": "firebase/php-jwt", "version": "v6.11.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^2.0||^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.11.1"}, "time": "2025-04-09T20:32:01+00:00"}, {"name": "giggsey/libphonenumber-for-php", "version": "8.13.55", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "6e28b3d53cf96d7f41c83d9b80b6021ecbd00537"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/6e28b3d53cf96d7f41c83d9b80b6021ecbd00537", "reference": "6e28b3d53cf96d7f41c83d9b80b6021ecbd00537", "shasum": ""}, "require": {"giggsey/locale": "^2.0", "php": "^7.4|^8.0", "symfony/polyfill-mbstring": "^1.17"}, "replace": {"giggsey/libphonenumber-for-php-lite": "self.version"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.64", "pear/pear-core-minimal": "^1.10", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.7", "phing/phing": "^3.0", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^9.6", "symfony/console": "^v5.2", "symfony/var-exporter": "^5.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "support": {"issues": "https://github.com/giggsey/libphonenumber-for-php/issues", "source": "https://github.com/giggsey/libphonenumber-for-php"}, "time": "2025-02-14T08:14:08+00:00"}, {"name": "giggsey/locale", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "1cd8b3ad2d43e04f4c2c6a240495af44780f809b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/1cd8b3ad2d43e04f4c2c6a240495af44780f809b", "reference": "1cd8b3ad2d43e04f4c2c6a240495af44780f809b", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"ext-json": "*", "friendsofphp/php-cs-fixer": "^3.66", "pear/pear-core-minimal": "^1.10", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.17.4", "php-coveralls/php-coveralls": "^2.7", "phpunit/phpunit": "^10.5.45", "symfony/console": "^6.4", "symfony/filesystem": "6.4", "symfony/finder": "^6.4", "symfony/process": "^6.4", "symfony/var-exporter": "^6.4"}, "type": "library", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "support": {"issues": "https://github.com/giggsey/Locale/issues", "source": "https://github.com/giggsey/Locale/tree/2.8.0"}, "time": "2025-03-20T14:25:27+00:00"}, {"name": "glenscott/url-normalizer", "version": "1.4.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/glenscott/url-normalizer/zipball/b8e79d3360a1bd7182398c9956bd74d219ad1b3c", "reference": "b8e79d3360a1bd7182398c9956bd74d219ad1b3c", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.3.0"}, "type": "library", "autoload": {"psr-4": {"URL\\": "src/URL"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Syntax based normalization of URL's", "time": "2015-06-11T16:06:02+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/3ba905c11371512af9d9bdd27d99b782216b6945", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "time": "2024-07-20T21:45:45+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/oauth-subscriber", "version": "0.8.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/oauth-subscriber/zipball/92b619b03bd21396e51c62e6bce83467d2ce8f53", "reference": "92b619b03bd21396e51c62e6bce83467d2ce8f53", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.9", "guzzlehttp/psr7": "^2.7", "php": "^7.2.5 || ^8.0"}, "suggest": {"ext-openssl": "Required to sign using RSA-SHA1"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Subscriber\\Oauth\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "description": "Guzzle OAuth 1.0 subscriber", "keywords": ["Guzzle", "o<PERSON>h"], "time": "2025-01-06T19:15:59+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "html2text/html2text", "version": "4.3.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/mtibben/html2text/zipball/3b443cbe302b52eb5806a21a9dbd79524203970a", "reference": "3b443cbe302b52eb5806a21a9dbd79524203970a", "shasum": ""}, "suggest": {"ext-mbstring": "For best performance", "symfony/polyfill-mbstring": "If you can't install ext-mbstring"}, "type": "library", "autoload": {"psr-4": {"Html2Text\\": "src/"}}, "license": ["GPL-2.0-or-later"], "description": "Converts HTML to formatted plain text", "time": "2024-08-20T02:43:29+00:00"}, {"name": "illuminate/collections", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/collections/zipball/d3710b0b244bfc62c288c1a87eaa62dd28352d1f", "reference": "d3710b0b244bfc62c288c1a87eaa62dd28352d1f", "shasum": ""}, "require": {"illuminate/conditionable": "^9.0", "illuminate/contracts": "^9.0", "illuminate/macroable": "^9.0", "php": "^8.0.2"}, "suggest": {"symfony/var-dumper": "Required to use the dump method (^6.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Collections package.", "homepage": "https://laravel.com", "time": "2023-06-11T21:17:10+00:00"}, {"name": "illuminate/conditionable", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/conditionable/zipball/bea24daa0fa84b7e7b0d5b84f62c71b7e2dc3364", "reference": "bea24daa0fa84b7e7b0d5b84f62c71b7e2dc3364", "shasum": ""}, "require": {"php": "^8.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Conditionable package.", "homepage": "https://laravel.com", "time": "2023-02-01T21:42:32+00:00"}, {"name": "illuminate/contracts", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/44f65d723b13823baa02ff69751a5948bde60c22", "reference": "44f65d723b13823baa02ff69751a5948bde60c22", "shasum": ""}, "require": {"php": "^8.0.2", "psr/container": "^1.1.1|^2.0.1", "psr/simple-cache": "^1.0|^2.0|^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "time": "2023-02-08T14:36:30+00:00"}, {"name": "illuminate/macroable", "version": "v9.52.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/macroable/zipball/e3bfaf6401742a9c6abca61b9b10e998e5b6449a", "reference": "e3bfaf6401742a9c6abca61b9b10e998e5b6449a", "shasum": ""}, "require": {"php": "^8.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Macroable package.", "homepage": "https://laravel.com", "time": "2022-08-09T13:29:29+00:00"}, {"name": "jakeasmith/http_build_url", "version": "1.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/jakeasmith/http_build_url/zipball/93c273e77cb1edead0cf8bcf8cd2003428e74e37", "reference": "93c273e77cb1edead0cf8bcf8cd2003428e74e37", "shasum": ""}, "type": "library", "autoload": {"files": ["src/http_build_url.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality for http_build_url() to environments without pecl_http.", "time": "2017-05-01T15:36:40+00:00"}, {"name": "jean85/pretty-package-versions", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "4d7aa5dab42e2a76d99559706022885de0e18e1a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/4d7aa5dab42e2a76d99559706022885de0e18e1a", "reference": "4d7aa5dab42e2a76d99559706022885de0e18e1a", "shasum": ""}, "require": {"composer-runtime-api": "^2.1.0", "php": "^7.4|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "jean85/composer-provided-replaced-stub-package": "^1.0", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^7.5|^8.5|^9.6", "rector/rector": "^2.0", "vimeo/psalm": "^4.3 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to get pretty versions strings of installed dependencies", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/2.1.1"}, "time": "2025-03-19T14:43:43+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "6.4.1", "source": {"type": "git", "url": "https://github.com/jsonrainbow/json-schema.git", "reference": "35d262c94959571e8736db1e5c9bc36ab94ae900"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jsonrainbow/json-schema/zipball/35d262c94959571e8736db1e5c9bc36ab94ae900", "reference": "35d262c94959571e8736db1e5c9bc36ab94ae900", "shasum": ""}, "require": {"ext-json": "*", "marc-mabe/php-enum": "^4.0", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.3.0", "json-schema/json-schema-test-suite": "1.2.0", "marc-mabe/php-enum-phpstan": "^2.0", "phpspec/prophecy": "^1.19", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "^8.5"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/jsonrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/jsonrainbow/json-schema/issues", "source": "https://github.com/jsonrainbow/json-schema/tree/6.4.1"}, "time": "2025-04-04T13:08:07+00:00"}, {"name": "league/csv", "version": "9.8.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/csv/zipball/9d2e0265c5d90f5dd601bc65ff717e05cec19b47", "reference": "9d2e0265c5d90f5dd601bc65ff717e05cec19b47", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^7.4 || ^8.0"}, "suggest": {"ext-dom": "Required to use the XMLConverter and or the HTMLConverter classes", "ext-iconv": "Needed to ease transcoding CSV using iconv stream filters"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"psr-4": {"League\\Csv\\": "src"}, "files": ["src/functions_include.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "description": "CSV data manipulation made easy in PHP", "homepage": "https://csv.thephpleague.com", "keywords": ["convert", "csv", "export", "filter", "import", "read", "transform", "write"], "time": "2022-01-04T00:13:07+00:00"}, {"name": "league/fractal", "version": "0.20.2", "source": {"type": "git", "url": "https://github.com/thephpleague/fractal.git", "reference": "573ca2e0e348a7fe573a3e8fbc29a6588ece8c4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/fractal/zipball/573ca2e0e348a7fe573a3e8fbc29a6588ece8c4e", "reference": "573ca2e0e348a7fe573a3e8fbc29a6588ece8c4e", "shasum": ""}, "require": {"php": ">=7.4"}, "require-dev": {"doctrine/orm": "^2.5", "illuminate/contracts": "~5.0", "laminas/laminas-paginator": "~2.12", "mockery/mockery": "^1.3", "pagerfanta/pagerfanta": "~1.0.0|~4.0.0", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "~3.4", "vimeo/psalm": "^4.30"}, "suggest": {"illuminate/pagination": "The Illuminate Pagination component.", "laminas/laminas-paginator": "Laminas Framework Paginator", "pagerfanta/pagerfanta": "Pagerfant<PERSON>"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.20.x-dev"}}, "autoload": {"psr-4": {"League\\Fractal\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://philsturgeon.uk/", "role": "Developer"}], "description": "Handle the output of complex data structures ready for API output.", "homepage": "http://fractal.thephpleague.com/", "keywords": ["api", "json", "league", "rest"], "support": {"issues": "https://github.com/thephpleague/fractal/issues", "source": "https://github.com/thephpleague/fractal/tree/0.20.2"}, "time": "2025-02-14T21:33:14+00:00"}, {"name": "league/html-to-markdown", "version": "5.1.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/html-to-markdown/zipball/0b4066eede55c48f38bcee4fb8f0aa85654390fd", "reference": "0b4066eede55c48f38bcee4fb8f0aa85654390fd", "shasum": ""}, "require": {"ext-dom": "*", "ext-xml": "*", "php": "^7.2.5 || ^8.0"}, "bin": ["bin/html-to-markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}, "autoload": {"psr-4": {"League\\HTMLToMarkdown\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://modernnerd.net", "role": "Original Author"}], "description": "An HTML-to-markdown conversion helper for PHP", "homepage": "https://github.com/thephpleague/html-to-markdown", "keywords": ["html", "markdown"], "time": "2023-07-12T21:21:09+00:00"}, {"name": "league/oauth1-client", "version": "v1.11.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth1-client/zipball/f9c94b088837eb1aae1ad7c4f23eb65cc6993055", "reference": "f9c94b088837eb1aae1ad7c4f23eb65cc6993055", "shasum": ""}, "require": {"ext-json": "*", "ext-openssl": "*", "guzzlehttp/guzzle": "^6.0|^7.0", "guzzlehttp/psr7": "^1.7|^2.0", "php": ">=7.1||>=8.0"}, "suggest": {"ext-simplexml": "For decoding XML-based responses."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev", "dev-develop": "2.0-dev"}}, "autoload": {"psr-4": {"League\\OAuth1\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.webcomm.com.au", "role": "Developer"}], "description": "OAuth 1.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "bitbucket", "identity", "idp", "o<PERSON>h", "oauth1", "single sign on", "trello", "tumblr", "twitter"], "time": "2024-12-10T19:59:05+00:00"}, {"name": "league/oauth2-client", "version": "2.7.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-client/zipball/160d6274b03562ebeb55ed18399281d8118b76c8", "reference": "160d6274b03562ebeb55ed18399281d8118b76c8", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0 || ^7.0", "paragonie/random_compat": "^1 || ^2 || ^9.99", "php": "^5.6 || ^7.0 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.0.x-dev"}}, "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/shadowhand", "role": "Contributor"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"], "time": "2023-04-16T18:19:15+00:00"}, {"name": "league/oauth2-google", "version": "4.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-google/zipball/1b01ba18ba31b29e88771e3e0979e5c91d4afe76", "reference": "1b01ba18ba31b29e88771e3e0979e5c91d4afe76", "shasum": ""}, "require": {"league/oauth2-client": "^2.0", "php": "^7.3 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://shadowhand.com"}], "description": "Google OAuth 2.0 Client Provider for The PHP League OAuth2-Client", "keywords": ["Authentication", "authorization", "client", "google", "o<PERSON>h", "oauth2"], "time": "2023-03-17T15:20:52+00:00"}, {"name": "lsolesen/pel", "version": "0.9.12", "dist": {"type": "zip", "url": "https://api.github.com/repos/pel/pel/zipball/b95fe29cdacf9d36330da277f10910a13648c84c", "reference": "b95fe29cdacf9d36330da277f10910a13648c84c", "shasum": ""}, "require": {"php": ">=7.1.0"}, "type": "library", "autoload": {"psr-4": {"lsolesen\\pel\\": "src/"}}, "license": ["GPL-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://intraface.dk", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://geisler.net", "role": "Developer"}], "description": "PHP Exif Library. A library for reading and writing Exif headers in JPEG and TIFF images using PHP.", "homepage": "http://pel.github.com/pel/", "keywords": ["exif", "image"], "time": "2022-02-18T13:20:54+00:00"}, {"name": "marc-mabe/php-enum", "version": "v4.7.1", "source": {"type": "git", "url": "https://github.com/marc-mabe/php-enum.git", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marc-mabe/php-enum/zipball/7159809e5cfa041dca28e61f7f7ae58063aae8ed", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed", "shasum": ""}, "require": {"ext-reflection": "*", "php": "^7.1 | ^8.0"}, "require-dev": {"phpbench/phpbench": "^0.16.10 || ^1.0.4", "phpstan/phpstan": "^1.3.1", "phpunit/phpunit": "^7.5.20 | ^8.5.22 | ^9.5.11", "vimeo/psalm": "^4.17.0 | ^5.26.1"}, "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.2-dev", "dev-master": "4.7-dev"}}, "autoload": {"psr-4": {"MabeEnum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://mabe.berlin/", "role": "Lead"}], "description": "Simple and fast implementation of enumerations with native PHP", "homepage": "https://github.com/marc-mabe/php-enum", "keywords": ["enum", "enum-map", "enum-set", "enumeration", "enumerator", "enummap", "enumset", "map", "set", "type", "type-hint", "<PERSON><PERSON>t"], "support": {"issues": "https://github.com/marc-mabe/php-enum/issues", "source": "https://github.com/marc-mabe/php-enum/tree/v4.7.1"}, "time": "2024-11-28T04:54:44+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "time": "2024-03-31T07:05:07+00:00"}, {"name": "mikehaertl/php-shellcommand", "version": "1.7.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/mikehaertl/php-shellcommand/zipball/e79ea528be155ffdec6f3bf1a4a46307bb49e545", "reference": "e79ea528be155ffdec6f3bf1a4a46307bb49e545", "shasum": ""}, "require": {"php": ">= 5.3.0"}, "type": "library", "autoload": {"psr-4": {"mikehaertl\\shellcommand\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An object oriented interface to shell commands", "keywords": ["shell"], "time": "2023-04-19T08:25:22+00:00"}, {"name": "mmikkel/cp-field-inspect", "version": "1.4.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/mmikkel/CpFieldInspect-Craft/zipball/cab2693eaad03f35192356d8971879e9b0a0e098", "reference": "cab2693eaad03f35192356d8971879e9b0a0e098", "shasum": ""}, "require": {"craftcms/cms": "^3.7.0|^4.0.0", "php": "^7.2.5|^8.0"}, "type": "craft-plugin", "extra": {"name": "CP Field Inspect", "handle": "cp-field-inspect", "schemaVersion": "1.0.0", "hasCpSettings": false, "hasCpSection": false, "changelogUrl": "https://raw.githubusercontent.com/mmikkel/CpFieldInspect-Craft/master/CHANGELOG.md", "class": "mmikkel\\cpfieldinspect\\CpFieldInspect"}, "autoload": {"psr-4": {"mmikkel\\cpfieldinspect\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON> <PERSON>", "homepage": "http://mmikkel.no"}], "description": "Inspect field handles and easily edit field and element source settings", "keywords": ["cms", "cp field inspect", "craft", "craft-plugin", "craftcms"], "support": {"docs": "https://github.com/mmikkel/CpFieldInspect-Craft/blob/master/README.md", "issues": "https://github.com/mmikkel/CpFieldInspect-Craft/issues"}, "time": "2022-05-07T15:24:35+00:00"}, {"name": "moneyphp/money", "version": "v4.7.0", "source": {"type": "git", "url": "https://github.com/moneyphp/money.git", "reference": "af048f0206d3b39b8fad9de6a230cedf765365fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/moneyphp/money/zipball/af048f0206d3b39b8fad9de6a230cedf765365fa", "reference": "af048f0206d3b39b8fad9de6a230cedf765365fa", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-filter": "*", "ext-json": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"cache/taggable-cache": "^1.1.0", "doctrine/coding-standard": "^12.0", "doctrine/instantiator": "^1.5.0 || ^2.0", "ext-gmp": "*", "ext-intl": "*", "florianv/exchanger": "^2.8.1", "florianv/swap": "^4.3.0", "moneyphp/crypto-currencies": "^1.1.0", "moneyphp/iso-currencies": "^3.4", "php-http/message": "^1.16.0", "php-http/mock-client": "^1.6.0", "phpbench/phpbench": "^1.2.5", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1.9", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5.9", "psr/cache": "^1.0.1 || ^2.0 || ^3.0", "ticketswap/phpstan-error-formatter": "^1.1"}, "suggest": {"ext-gmp": "Calculate without integer limits", "ext-intl": "Format Money objects with intl", "florianv/exchanger": "Exchange rates library for PHP", "florianv/swap": "Exchange rates library for PHP", "psr/cache-implementation": "Used for Currency caching"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Money\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://verraes.net"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP implementation of Fowler's Money pattern", "homepage": "http://moneyphp.org", "keywords": ["Value Object", "money", "vo"], "support": {"issues": "https://github.com/moneyphp/money/issues", "source": "https://github.com/moneyphp/money/tree/v4.7.0"}, "time": "2025-04-03T08:26:36+00:00"}, {"name": "monolog/monolog", "version": "2.10.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/5cf826f2991858b54d5c3809bee745560a1042a7", "reference": "5cf826f2991858b54d5c3809bee745560a1042a7", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2024-11-12T12:43:37+00:00"}, {"name": "nesbot/carbon", "version": "2.73.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9228ce90e1035ff2f0db84b40ec2e023ed802075", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "<6", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2025-01-08T20:10:23+00:00"}, {"name": "nystudio107/craft-code-editor", "version": "1.0.22", "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-code-editor/zipball/170edf71355b659e1db9ede12980b17c20eb3d1f", "reference": "170edf71355b659e1db9ede12980b17c20eb3d1f", "shasum": ""}, "require": {"craftcms/cms": "^3.0.0 || ^4.0.0 || ^5.0.0", "phpdocumentor/reflection-docblock": "^5.0.0"}, "type": "yii2-extension", "extra": {"bootstrap": "nystudio107\\codeeditor\\CodeEditor"}, "autoload": {"psr-4": {"nystudio107\\codeeditor\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com"}], "description": "Provides a code editor field with Twig & Craft API autocomplete", "keywords": ["Craft", "Monaco", "cms", "code", "craftcms", "css", "editor", "javascript", "markdown", "twig"], "time": "2024-09-23T17:20:25+00:00"}, {"name": "nystudio107/craft-eagerbeaver", "version": "4.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-eagerbeaver/zipball/76e91fb026e175e0ebc69a28e064ea77f85690a9", "reference": "76e91fb026e175e0ebc69a28e064ea77f85690a9", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0"}, "type": "craft-plugin", "extra": {"name": "Eager Beaver", "handle": "eager-beaver", "class": "nystudio107\\eagerbeaver\\EagerBeaver"}, "autoload": {"psr-4": {"nystudio107\\eagerbeaver\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com"}], "description": "Allows you to eager load elements from auto-injected Entry elements on demand from your templates.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "eager", "eager beaver", "eager loading", "loading"], "support": {"docs": "https://nystudio107.com/docs/eager-beaver/", "issues": "https://nystudio107.com/plugins/eager-beaver/support", "source": "https://github.com/nystudio107/craft-eagerbeaver"}, "time": "2022-08-17T15:57:58+00:00"}, {"name": "nystudio107/craft-plugin-vite", "version": "4.0.12", "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-plugin-vite/zipball/ddb7acea4506837928e19cd3e46e20f7ba5689d3", "reference": "ddb7acea4506837928e19cd3e46e20f7ba5689d3", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0"}, "type": "library", "autoload": {"psr-4": {"nystudio107\\pluginvite\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com"}], "description": "Plugin Vite is the conduit between Craft CMS plugins and Vite, with manifest.json & HMR support", "keywords": ["craftcms", "plugin", "vite"], "support": {"docs": "https://github.com/nystudio107/craft-plugin-vite/blob/v4/README.md", "issues": "https://github.com/nystudio107/craft-plugin-vite/issues"}, "time": "2024-08-14T02:01:17+00:00"}, {"name": "nystudio107/craft-retour", "version": "4.1.26", "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-retour/zipball/9a81801d0f3c33adc4a0338ef70f00f732c88b3c", "reference": "9a81801d0f3c33adc4a0338ef70f00f732c88b3c", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "jean85/pretty-package-versions": "^1.5 || ^2.0", "league/csv": "^8.2 || ^9.0", "nystudio107/craft-plugin-vite": "^4.0.0"}, "type": "craft-plugin", "extra": {"class": "nystudio107\\retour\\Retour", "handle": "retour", "name": "Retour"}, "autoload": {"psr-4": {"nystudio107\\retour\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com/"}], "description": "Retour allows you to intelligently redirect legacy URLs, so that you don't lose SEO value when rebuilding & restructuring a website", "keywords": ["404", "craft-plugin", "craftcms", "redirect", "retour", "statistics"], "support": {"docs": "https://nystudio107.com/docs/retour/", "issues": "https://nystudio107.com/plugins/retour/support", "source": "https://github.com/nystudio107/craft-retour"}, "time": "2025-04-22T17:42:36+00:00"}, {"name": "nystudio107/craft-seomatic", "version": "4.1.13", "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-seomatic/zipball/91a7ca8fea15ce37d36a93724355d713adbd0465", "reference": "91a7ca8fea15ce37d36a93724355d713adbd0465", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "davechild/textstatistics": "^1.0.3", "nystudio107/craft-code-editor": "^1.0.0", "nystudio107/craft-plugin-vite": "^4.0.7", "php": "^8.0.2", "php-science/textrank": "^1.0.3", "sunra/php-simple-html-dom-parser": "^1.5.2"}, "type": "craft-plugin", "extra": {"class": "nystudio107\\seomatic\\Seomatic", "handle": "seomatic", "name": "SEOmatic"}, "autoload": {"psr-4": {"nystudio107\\seomatic\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com"}], "description": "SEOmatic facilitates modern SEO best practices & implementation for Craft CMS 4. It is a turnkey SEO system that is comprehensive, powerful, and flexible.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "facebook", "json-ld", "meta", "seo", "seomatic", "sitemap", "tags", "twitter"], "support": {"docs": "https://nystudio107.com/docs/seomatic/", "issues": "https://nystudio107.com/plugins/seomatic/support", "source": "https://github.com/nystudio107/craft-seomatic"}, "time": "2025-04-24T23:55:34+00:00"}, {"name": "nystudio107/craft-templatecomments", "version": "4.0.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-templatecomments/zipball/7d28911ee5a8584743836d69cb7977b773d1a726", "reference": "7d28911ee5a8584743836d69cb7977b773d1a726", "shasum": ""}, "require": {"craftcms/cms": "^4.12.0"}, "type": "craft-plugin", "extra": {"class": "nystudio107\\templatecomments\\TemplateComments", "handle": "templatecomments", "name": "Template Comments"}, "autoload": {"psr-4": {"nystudio107\\templatecomments\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com/"}], "description": "Adds a HTML comment with performance timings to demarcate `{% block %}`s and each Twig template that is included or extended.", "keywords": ["comments", "craft-plugin", "craftcms", "debugging", "twig"], "support": {"docs": "https://nystudio107.com/docs/template-comments/", "issues": "https://nystudio107.com/plugins/template-comments/support", "source": "https://github.com/nystudio107/craft-templatecomments"}, "time": "2024-11-29T18:52:59+00:00"}, {"name": "nystudio107/craft-twig-sandbox", "version": "4.0.2", "source": {"type": "git", "url": "https://github.com/nystudio107/craft-twig-sandbox.git", "reference": "6305b51d4653cf84744075d86d376143ad2c881e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-twig-sandbox/zipball/6305b51d4653cf84744075d86d376143ad2c881e", "reference": "6305b51d4653cf84744075d86d376143ad2c881e", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "twig/twig": "^3.0.0"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main", "craftcms/rector": "dev-main", "markhuot/craft-pest-core": "^2.0.4"}, "type": "library", "autoload": {"psr-4": {"nystudio107\\crafttwigsandbox\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com"}], "description": "Allows you to easily create a sandboxed Twig environment where you can control what tags, filters, functions, and object methods/properties are allowed", "keywords": ["Craft", "cms", "craftcms", "sandbox", "security", "twig"], "support": {"docs": "https://github.com/nystudio107/craft-twig-sandbox/blob/v4/README.md", "issues": "https://github.com/nystudio107/craft-twig-sandbox/issues", "source": "https://github.com/nystudio107/craft-twig-sandbox"}, "funding": [{"url": "https://github.com/khalwat", "type": "github"}], "time": "2025-02-18T00:04:35+00:00"}, {"name": "nystudio107/craft-vite", "version": "4.0.10", "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-vite/zipball/a9069abffb8aafdb5245bd0b1ce8ef27845bd683", "reference": "a9069abffb8aafdb5245bd0b1ce8ef27845bd683", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "nystudio107/craft-plugin-vite": "^4.0.12"}, "type": "craft-plugin", "extra": {"changelogUrl": "https://raw.githubusercontent.com/nystudio107/craft-vite/v4/CHANGELOG.md", "class": "nystudio107\\vite\\Vite", "handle": "vite", "name": "Vite"}, "autoload": {"psr-4": {"nystudio107\\vite\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com"}], "description": "Allows the use of the Vite.js next generation frontend tooling with Craft CMS", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "vite"], "support": {"docs": "https://nystudio107.com/docs/vite/", "issues": "https://nystudio107.com/plugins/vite/support", "source": "https://github.com/nystudio107/craft-vite"}, "time": "2024-08-14T02:04:28+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2020-10-15T08:29:30+00:00"}, {"name": "phenx/php-font-lib", "version": "0.5.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-font-lib/zipball/a1681e9793040740a405ac5b189275059e2a9863", "reference": "a1681e9793040740a405ac5b189275059e2a9863", "shasum": ""}, "require": {"ext-mbstring": "*"}, "type": "library", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/PhenX/php-font-lib", "time": "2024-01-29T14:45:26+00:00"}, {"name": "phenx/php-svg-lib", "version": "0.5.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-svg-lib/zipball/46b25da81613a9cf43c83b2a8c2c1bdab27df691", "reference": "46b25da81613a9cf43c83b2a8c2c1bdab27df691", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0", "sabberworm/php-css-parser": "^8.4"}, "type": "library", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/PhenX/php-svg-lib", "time": "2024-04-08T12:52:34+00:00"}, {"name": "php-science/textrank", "version": "1.2.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/DavidBelicza/PHP-Science-TextRank/zipball/d63788f7f7305b8dd726a367a5653cdd5dac7fba", "reference": "d63788f7f7305b8dd726a367a5653cdd5dac7fba", "shasum": ""}, "require": {"ext-ctype": "*", "ext-mbstring": "*", "php": ">=7.2"}, "type": "library", "autoload": {"psr-4": {"PhpScience\\TextRank\\": ["src/"]}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "TextRank (automatic text summarization) for PHP.", "keywords": ["ai", "artificial", "automatic", "intelligence", "php", "php8", "science", "strict", "summarization", "textrank"], "time": "2023-12-29T15:21:30+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.6.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/92dde6a5919e34835c506ac8c523ef095a95ed62", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7|^2.0", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.5 || ~1.6.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.5", "psalm/phar": "^5.26"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.2"}, "time": "2025-04-13T19:20:35+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.10.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/679e3ce485b99e84c775d28e2e96fade9a7fb50a", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "time": "2024-11-09T15:12:26+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/e3fac8b24f56113f7cb96af14958c0dd16330f54", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "time": "2024-07-20T21:41:07+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.1.0"}, "time": "2025-02-19T13:28:12+00:00"}, {"name": "pixelandtonic/imagine", "version": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/pixelandtonic/Imagine/zipball/4d9bb596ff60504e37ccf9103c0bb705dba7fec6", "reference": "4d9bb596ff60504e37ccf9103c0bb705dba7fec6", "shasum": ""}, "require": {"php": ">=5.5"}, "suggest": {"ext-exif": "to read EXIF metadata", "ext-gd": "to use the GD implementation", "ext-gmagick": "to use the Gmagick implementation", "ext-imagick": "to use the Imagick implementation"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "1.x-dev"}}, "autoload": {"psr-4": {"Imagine\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://avalanche123.com"}], "description": "Image processing for PHP 5.3", "homepage": "http://imagine.readthedocs.org/", "keywords": ["drawing", "graphics", "image manipulation", "image processing"], "time": "2023-01-03T19:18:06+00:00"}, {"name": "psr/cache", "version": "3.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2021-10-29T13:26:27+00:00"}, {"name": "putyourlightson/craft-elements-panel", "version": "2.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/putyourlightson/craft-elements-panel/zipball/7775c4fb0eb996544c6365863e14a986c0159592", "reference": "7775c4fb0eb996544c6365863e14a986c0159592", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0.2"}, "type": "craft-plugin", "extra": {"name": "Elements Panel", "handle": "elements-panel", "developer": "PutYourLightsOn", "developerUrl": "https://putyourlightson.com/", "documentationUrl": "https://putyourlightson.com/plugins/elements-panel", "changelogUrl": "https://raw.githubusercontent.com/putyourlightson/craft-elements-panel/v2/CHANGELOG.md", "class": "putyourlightson\\elementspanel\\ElementsPanel"}, "autoload": {"psr-4": {"putyourlightson\\elementspanel\\": "src/"}}, "license": ["mit"], "description": "Adds an Elements and an Eager-Loading panel to the debug toolbar.", "support": {"docs": "https://putyourlightson.com/plugins/elements-panel", "source": "https://github.com/putyourlightson/craft-elements-panel", "issues": "https://github.com/putyourlightson/craft-elements-panel/issues"}, "time": "2022-05-04T18:44:34+00:00"}, {"name": "putyourlightson/craft-sprig", "version": "2.14.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/putyourlightson/craft-sprig/zipball/9be26c11fd687a8d8b6146ae054fc3a55462dacd", "reference": "9be26c11fd687a8d8b6146ae054fc3a55462dacd", "shasum": ""}, "require": {"craftcms/cms": "^4.0", "nystudio107/craft-code-editor": "^1.0.0", "nystudio107/craft-twig-sandbox": "^4.0.2", "php": "^8.0.2", "putyourlightson/craft-sprig-core": "^2.13.0"}, "type": "craft-plugin", "extra": {"name": "<PERSON><PERSON><PERSON>", "handle": "sprig", "developer": "PutYourLightsOn", "developerUrl": "https://putyourlightson.com/", "documentationUrl": "https://putyourlightson.com/plugins/sprig", "changelogUrl": "https://raw.githubusercontent.com/putyourlightson/craft-sprig/v2/CHANGELOG.md", "class": "putyourlightson\\sprig\\plugin\\Sprig"}, "autoload": {"psr-4": {"putyourlightson\\sprig\\plugin\\": "src/"}}, "license": ["mit"], "description": "A reactive Twig component framework for Craft.", "support": {"docs": "https://putyourlightson.com/plugins/sprig", "source": "https://github.com/putyourlightson/craft-sprig", "issues": "https://github.com/putyourlightson/craft-sprig/issues"}, "time": "2025-04-16T14:30:07+00:00"}, {"name": "putyourlightson/craft-sprig-core", "version": "2.13.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/putyourlightson/craft-sprig-core/zipball/327ec67c9aba2d9d8aa45157d210f4e1c01b7580", "reference": "327ec67c9aba2d9d8aa45157d210f4e1c01b7580", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0.2"}, "type": "craft-module", "autoload": {"psr-4": {"putyourlightson\\sprig\\": "src/"}}, "license": ["mit"], "description": "A reactive Twig component framework for Craft.", "support": {"docs": "https://github.com/putyourlightson/craft-sprig-core", "source": "https://github.com/putyourlightson/craft-sprig-core", "issues": "https://github.com/putyourlightson/craft-sprig-core/issues"}, "time": "2025-04-09T17:54:49+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "react/promise", "version": "v3.2.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/8a164643313c71354582dc850b42b33fa12a4b63", "reference": "8a164643313c71354582dc850b42b33fa12a4b63", "shasum": ""}, "require": {"php": ">=7.1.0"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "time": "2024-05-24T10:39:05+00:00"}, {"name": "redkiwi/craft-endpoints", "version": "dev-master", "dist": {"type": "path", "url": "dev/redkiwi-endpoints", "reference": "357c3437da8b0e842eb751560b1d7d56fec97c79"}, "require": {"craftcms/cms": "^4.5"}, "type": "craft-plugin", "extra": {"name": "Craft endpoints", "handle": "craft-endpoints"}, "autoload": {"psr-4": {"redkiwi\\craftendpoints\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://redkiwi.nl"}], "description": "This plugin/package creates an endpoint to retrieve an address for a postal code and house number.", "support": {"email": "<EMAIL>"}, "transport-options": {"relative": true}}, {"name": "sabberworm/php-css-parser", "version": "v8.8.0", "source": {"type": "git", "url": "https://github.com/MyIntervals/PHP-CSS-Parser.git", "reference": "3de493bdddfd1f051249af725c7e0d2c38fed740"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/PHP-CSS-Parser/zipball/3de493bdddfd1f051249af725c7e0d2c38fed740", "reference": "3de493bdddfd1f051249af725c7e0d2c38fed740", "shasum": ""}, "require": {"ext-iconv": "*", "php": "^5.6.20 || ^7.0.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"phpunit/phpunit": "5.7.27 || 6.5.14 || 7.5.20 || 8.5.41"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "type": "library", "extra": {"branch-alias": {"dev-main": "9.0.x-dev"}}, "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/MyIntervals/PHP-CSS-Parser/issues", "source": "https://github.com/MyIntervals/PHP-CSS-Parser/tree/v8.8.0"}, "time": "2025-03-23T17:59:05+00:00"}, {"name": "samdark/yii2-psr-log-target", "version": "1.1.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/samdark/yii2-psr-log-target/zipball/5f14f21d5ee4294fe9eb3e723ec8a3908ca082ea", "reference": "5f14f21d5ee4294fe9eb3e723ec8a3908ca082ea", "shasum": ""}, "require": {"psr/log": "~1.0.2|~1.1.0|~3.0.0", "yiisoft/yii2": "~2.0.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"samdark\\log\\": "src", "samdark\\log\\tests\\": "tests"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Yii 2 log target which uses PSR-3 compatible logger", "homepage": "https://github.com/samdark/yii2-psr-log-target", "keywords": ["extension", "log", "psr-3", "yii"], "time": "2023-11-23T14:11:29+00:00"}, {"name": "sebastianlenz/craft-utils", "version": "3.0.12", "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian-lenz/craft-utils/zipball/3abd09bd950dda67926f537b0d9b4b922c205c4d", "reference": "3abd09bd950dda67926f537b0d9b4b922c205c4d", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0"}, "type": "library", "autoload": {"psr-4": {"lenz\\craft\\utils\\": "src/"}}, "license": ["MIT"], "description": "A collection of utility classes for Craft CMS development", "time": "2023-11-29T19:19:14+00:00"}, {"name": "sebastianlenz/linkfield", "version": "2.1.5", "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian-lenz/craft-linkfield/zipball/b82fb1a8f94861f08c8b119758606a2a2172aef9", "reference": "b82fb1a8f94861f08c8b119758606a2a2172aef9", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0", "sebastianlenz/craft-utils": "^3.0.5"}, "type": "craft-plugin", "extra": {"handle": "typedlinkfield", "name": "Typed link field", "developer": "<PERSON>", "developerUrl": "https://github.com/sebastian-lenz/"}, "autoload": {"classmap": ["legacy"], "psr-4": {"lenz\\linkfield\\": "src/"}}, "license": ["MIT"], "description": "A Craft field type for selecting links", "time": "2022-11-29T09:36:26+00:00"}, {"name": "seld/cli-prompt", "version": "1.0.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/cli-prompt/zipball/b8dfcf02094b8c03b40322c229493bb2884423c5", "reference": "b8dfcf02094b8c03b40322c229493bb2884423c5", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\CliPrompt\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Allows you to prompt for user input on the command line, and optionally hide the characters they type", "keywords": ["cli", "console", "hidden", "input", "prompt"], "time": "2020-12-15T21:32:01+00:00"}, {"name": "seld/jsonlint", "version": "1.11.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/1748aaf847fc731cfad7725aec413ee46f0cc3a2", "reference": "1748aaf847fc731cfad7725aec413ee46f0cc3a2", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "time": "2024-07-11T14:55:45+00:00"}, {"name": "seld/phar-utils", "version": "1.2.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "time": "2022-08-31T10:31:18+00:00"}, {"name": "seld/signal-handler", "version": "2.0.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/signal-handler/zipball/04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "reference": "04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Seld\\Signal\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Simple unix signal handler that silently fails where signals are not supported for easy cross-platform development", "keywords": ["posix", "sigint", "signal", "sigterm", "unix"], "time": "2023-09-03T09:24:00+00:00"}, {"name": "spicyweb/craft-neo", "version": "4.4.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/spicywebau/craft-neo/zipball/c65bc450543231a587d01b506e5b776bd6cbd8aa", "reference": "c65bc450543231a587d01b506e5b776bd6cbd8aa", "shasum": ""}, "require": {"craftcms/cms": "^4.5.0", "php": "^8.0.2"}, "type": "craft-plugin", "extra": {"handle": "neo", "name": "Neo", "schemaVersion": "4.0.0", "class": "benf\\neo\\Plugin", "changelogUrl": "https://github.com/spicywebau/craft-neo/blob/4.x/CHANGELOG.md", "downloadUrl": "https://github.com/spicywebau/craft-neo/archive/refs/tags/4.4.2.zip"}, "autoload": {"psr-4": {"benf\\neo\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "Spicy Web", "homepage": "https://github.com/spicywebau"}], "description": "A Matrix-like field type that uses existing fields", "keywords": ["cms", "craftcms", "field", "matrix", "neo", "plugin"], "support": {"issues": "https://github.com/spicywebau/craft-neo/issues", "source": "https://github.com/spicywebau/craft-neo", "docs": "https://github.com/spicywebau/craft-neo/blob/4.4.2/README.md", "rss": "https://github.com/spicywebau/craft-neo/commits/4.x.atom"}, "time": "2025-05-15T00:24:08+00:00"}, {"name": "spicyweb/craft-quick-field", "version": "2.0.8", "dist": {"type": "zip", "url": "https://api.github.com/repos/spicywebau/craft-quick-field/zipball/c26e86ad0877b1135a1e0e5ba6a35a2b6f3bdfb2", "reference": "c26e86ad0877b1135a1e0e5ba6a35a2b6f3bdfb2", "shasum": ""}, "require": {"craftcms/cms": "^4.1.0", "php": "^8.0.2"}, "type": "craft-plugin", "extra": {"handle": "quick-field", "name": "Quick Field", "schemaVersion": "1.0.0", "class": "spicyweb\\quickfield\\Plugin", "changelogUrl": "https://github.com/spicywebau/craft-quick-field/blob/2.x/CHANGELOG.md", "downloadUrl": "https://github.com/spicywebau/craft-quick-field/archive/refs/tags/2.0.8.zip"}, "autoload": {"psr-4": {"spicyweb\\quickfield\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Spicy Web", "homepage": "https://spicyweb.com.au/"}], "description": "Create Craft CMS fields on the fly while designing field layouts", "keywords": ["cms", "craftcms", "plugin"], "support": {"issues": "https://github.com/spicywebau/craft-quick-field/issues", "source": "https://github.com/spicywebau/craft-quick-field", "docs": "https://github.com/spicywebau/craft-quick-field/blob/2.0.8/README.md", "rss": "https://github.com/spicywebau/craft-quick-field/commits/2.x.atom"}, "time": "2024-06-16T06:46:21+00:00"}, {"name": "stripe/stripe-php", "version": "v10.21.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/stripe/stripe-php/zipball/b4ab319731958077227fad1874a3671458c5d593", "reference": "b4ab319731958077227fad1874a3671458c5d593", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=5.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Stripe\\": "lib/"}}, "license": ["MIT"], "authors": [{"name": "Stripe and contributors", "homepage": "https://github.com/stripe/stripe-php/contributors"}], "description": "Stripe PHP Library", "homepage": "https://stripe.com/", "keywords": ["api", "payment processing", "stripe"], "time": "2023-08-11T00:23:24+00:00"}, {"name": "studioespresso/craft-dumper", "version": "5.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/studioespresso/craft-dumper/zipball/3f5c1cfc44cffcefebe9c9b83e94c40b5b75dbc4", "reference": "3f5c1cfc44cffcefebe9c9b83e94c40b5b75dbc4", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0|^5.0.0-alpha"}, "type": "craft-plugin", "extra": {"name": "<PERSON><PERSON>", "handle": "dumper", "schemaVersion": "1.0.0", "hasCpSettings": false, "hasCpSection": false, "changelogUrl": "https://raw.githubusercontent.com/studioespresso/craft-dumper/master/CHANGELOG.md", "class": "studioespresso\\craftdumper\\CraftDumper"}, "autoload": {"psr-4": {"studioespresso\\craftdumper\\": "src/"}, "files": ["src/helper.php"]}, "license": ["MIT"], "authors": [{"name": "Studio Espresso", "homepage": "https://www.studioespresso.co"}], "description": "Bringing symfony/VarDumper to Craft CMS", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "dumper"], "support": {"docs": "https://github.com/studioespresso/craft-dumper/blob/master/README.md", "issues": "https://github.com/studioespresso/craft-dumper/issues"}, "time": "2024-02-21T14:48:06+00:00"}, {"name": "sunra/php-simple-html-dom-parser", "version": "v1.5.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/sunra/php-simple-html-dom-parser/zipball/75b9b1cb64502d8f8c04dc11b5906b969af247c6", "reference": "75b9b1cb64502d8f8c04dc11b5906b969af247c6", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.3.2"}, "type": "library", "autoload": {"psr-0": {"Sunra\\PhpSimple\\HtmlDomParser": "Src/"}}, "license": ["MIT"], "authors": [{"name": "Sunra", "email": "<EMAIL>", "homepage": "https://github.com/sunra"}, {"name": "<PERSON>.<PERSON><PERSON> Chen", "homepage": "http://sourceforge.net/projects/simplehtmldom/"}], "description": "Composer adaptation of: A HTML DOM parser written in PHP5+ let you manipulate HTML in a very easy way! Require PHP 5+. Supports invalid HTML. Find tags on an HTML page with selectors just like jQuery. Extract contents from HTML in a single line.", "homepage": "https://github.com/sunra/php-simple-html-dom-parser", "keywords": ["dom", "html", "parser"], "time": "2016-11-22T22:57:47+00:00"}, {"name": "symfony/cache", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "d1abcf763a7414f2e572f676f22da7a06c8cd9ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/d1abcf763a7414f2e572f676f22da7a06c8cd9ee", "reference": "d1abcf763a7414f2e572f676f22da7a06c8cd9ee", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.3.6|^7.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/var-dumper": "<5.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-08T08:21:20+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.5.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/15a4f8e5cd3bce9aeafc882b1acab39ec8de2c1b", "reference": "15a4f8e5cd3bce9aeafc882b1acab39ec8de2c1b", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/console", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "a3011c7b7adb58d89f6c0d822abb641d7a5f9719"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/a3011c7b7adb58d89f6c0d822abb641d7a5f9719", "reference": "a3011c7b7adb58d89f6c0d822abb641d7a5f9719", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-07T15:42:41+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.4.13", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/expression-language", "version": "v5.4.45", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/a784b66edc4c151eb05076d04707906ee2c209a9", "reference": "a784b66edc4c151eb05076d04707906ee2c209a9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an engine that can compile and evaluate expressions", "homepage": "https://symfony.com", "time": "2024-10-04T14:55:40+00:00"}, {"name": "symfony/filesystem", "version": "v6.4.13", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/4856c9cf585d5a0313d8d35afd681a526f038dd3", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "time": "2024-10-25T15:07:50+00:00"}, {"name": "symfony/finder", "version": "v6.4.17", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "time": "2024-12-29T13:51:37+00:00"}, {"name": "symfony/http-client", "version": "v6.4.19", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "3294a433fc9d12ae58128174896b5b1822c28dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/3294a433fc9d12ae58128174896b5b1822c28dad", "reference": "3294a433fc9d12ae58128174896b5b1822c28dad", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.4|^3.5.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-foundation": "<6.3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-13T09:55:13+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.5.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/ee8d807ab20fcb51267fdace50fbe3494c31e645", "reference": "ee8d807ab20fcb51267fdace50fbe3494c31e645", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}, "exclude-from-classmap": ["/Test/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2024-12-07T08:49:48+00:00"}, {"name": "symfony/mailer", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "ada2809ccd4ec27aba9fc344e3efdaec624c6438"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/ada2809ccd4ec27aba9fc344e3efdaec624c6438", "reference": "ada2809ccd4ec27aba9fc344e3efdaec624c6438", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.1", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/mime": "^6.2|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/messenger": "<6.2", "symfony/mime": "<6.2", "symfony/twig-bridge": "<6.2.1"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/messenger": "^6.2|^7.0", "symfony/twig-bridge": "^6.2|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-26T23:47:35+00:00"}, {"name": "symfony/mime", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "fec8aa5231f3904754955fad33c2db50594d22d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/fec8aa5231f3904754955fad33c2db50594d22d1", "reference": "fec8aa5231f3904754955fad33c2db50594d22d1", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.4|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T13:27:38+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "5f3b930437ae03ae5dff61269024d8ea1b3774aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/5f3b930437ae03ae5dff61269024d8ea1b3774aa", "reference": "5f3b930437ae03ae5dff61269024d8ea1b3774aa", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-17T14:58:18+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.31.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "metapackage", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T08:10:11+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php84", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php84.git", "reference": "000df7860439609837bbe28670b0be15783b7fbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php84/zipball/000df7860439609837bbe28670b0be15783b7fbf", "reference": "000df7860439609837bbe28670b0be15783b7fbf", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php84\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.4+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php84/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-20T12:04:08+00:00"}, {"name": "symfony/process", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "e2a61c16af36c9a07e5c9906498b73e091949a20"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/e2a61c16af36c9a07e5c9906498b73e091949a20", "reference": "e2a61c16af36c9a07e5c9906498b73e091949a20", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-10T17:11:00+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/string", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "73e2c6966a5aef1d4892873ed5322245295370c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/73e2c6966a5aef1d4892873ed5322245295370c6", "reference": "73e2c6966a5aef1d4892873ed5322245295370c6", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/intl": "^6.2|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-18T15:23:29+00:00"}, {"name": "symfony/translation", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "bb92ea5588396b319ba43283a5a3087a034cb29c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/bb92ea5588396b319ba43283a5a3087a034cb29c", "reference": "bb92ea5588396b319ba43283a5a3087a034cb29c", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-07T19:02:30+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.5.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/4667ff3bd513750603a09c8dedbea942487fb07c", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.48", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/42f18f170aa86d612c3559cfb3bd11a375df32c8", "reference": "42f18f170aa86d612c3559cfb3bd11a375df32c8", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/console": "<4.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "time": "2024-11-08T15:21:10+00:00"}, {"name": "symfony/var-exporter", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "717e7544aa99752c54ecba5c0e17459c48317472"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/717e7544aa99752c54ecba5c0e17459c48317472", "reference": "717e7544aa99752c54ecba5c0e17459c48317472", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T21:06:26+00:00"}, {"name": "symfony/yaml", "version": "v5.4.45", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/a454d47278cc16a5db371fe73ae66a78a633371e", "reference": "a454d47278cc16a5db371fe73ae66a78a633371e", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.3"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "time": "2024-09-25T14:11:13+00:00"}, {"name": "theiconic/name-parser", "version": "v1.2.11", "dist": {"type": "zip", "url": "https://api.github.com/repos/theiconic/name-parser/zipball/9a54a713bf5b2e7fd990828147d42de16bf8a253", "reference": "9a54a713bf5b2e7fd990828147d42de16bf8a253", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "autoload": {"psr-4": {"TheIconic\\NameParser\\": ["src/", "tests/"]}}, "license": ["MIT"], "authors": [{"name": "The Iconic", "email": "<EMAIL>"}], "description": "PHP library for parsing a string containing a full name into its parts", "time": "2019-11-14T14:08:48+00:00"}, {"name": "thenetworg/oauth2-azure", "version": "v2.2.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/TheNetworg/oauth2-azure/zipball/be204a5135f016470a9c33e82ab48785bbc11af2", "reference": "be204a5135f016470a9c33e82ab48785bbc11af2", "shasum": ""}, "require": {"ext-json": "*", "ext-openssl": "*", "firebase/php-jwt": "~3.0||~4.0||~5.0||~6.0", "league/oauth2-client": "~2.0", "php": "^7.1|^8.0"}, "type": "library", "autoload": {"psr-4": {"TheNetworg\\OAuth2\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://thenetw.org"}], "description": "Azure Active Directory OAuth 2.0 Client Provider for The PHP League OAuth2-Client", "keywords": ["SSO", "aad", "authorization", "azure", "azure active directory", "client", "microsoft", "o<PERSON>h", "oauth2", "windows azure"], "time": "2023-12-19T12:10:48+00:00"}, {"name": "twig/twig", "version": "v3.15.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/2d5b3964cc21d0188633d7ddce732dc8e874db02", "reference": "2d5b3964cc21d0188633d7ddce732dc8e874db02", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php81": "^1.29"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "time": "2024-11-17T15:59:19+00:00"}, {"name": "verbb/base", "version": "2.0.10", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/verbb-base/zipball/47a2671ef8862236b19609fe8409f11843aee1cf", "reference": "47a2671ef8862236b19609fe8409f11843aee1cf", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0.2"}, "type": "yii-module", "autoload": {"psr-4": {"verbb\\base\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Common utilities and building-blocks for Verbb plugins for Craft CMS.", "time": "2024-11-13T00:05:42+00:00"}, {"name": "verbb/field-manager", "version": "3.0.9", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/field-manager/zipball/ea9f6d505a695cb2544a8a202c08a2e2c21a58c4", "reference": "ea9f6d505a695cb2544a8a202c08a2e2c21a58c4", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0.2", "verbb/base": "^2.0.0"}, "type": "craft-plugin", "extra": {"name": "Field Manager", "handle": "field-manager", "changelogUrl": "https://raw.githubusercontent.com/verbb/field-manager/craft-4/CHANGELOG.md", "class": "verbb\\fieldmanager\\FieldManager"}, "autoload": {"psr-4": {"verbb\\fieldmanager\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Manage your fields and field groups with ease.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "field manager"], "support": {"email": "<EMAIL>", "issues": "https://github.com/verbb/field-manager/issues?state=open", "source": "https://github.com/verbb/field-manager", "docs": "https://github.com/verbb/field-manager", "rss": "https://github.com/verbb/field-manager/commits/v2.atom"}, "time": "2024-07-21T11:32:49+00:00"}, {"name": "verbb/formie", "version": "2.1.47", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/formie/zipball/0eaf74e58e7266fe176f3c4fce83d289bbc3e976", "reference": "0eaf74e58e7266fe176f3c4fce83d289bbc3e976", "shasum": ""}, "require": {"commerceguys/addressing": "^1.2", "craftcms/cms": "^4.3.2", "dompdf/dompdf": "^1.0.2 || ^2.0.4", "ezyang/htmlpurifier": "^4.13", "fakerphp/faker": "^1.9.1", "giggsey/libphonenumber-for-php": "^8.12", "guzzlehttp/oauth-subscriber": "^0.4.0 || ^0.5.0 || ^0.6.0 || ^0.7.0 || ^0.8.1", "html2text/html2text": "^4.3", "league/html-to-markdown": "^4.10 || ^5.0", "league/oauth1-client": "^1.9", "league/oauth2-client": "2.7.0", "league/oauth2-google": "^3.0 || ^4.0", "moneyphp/money": "^4.0", "nystudio107/craft-plugin-vite": "^4.0.0-beta.4", "php": "^8.0.2", "stripe/stripe-php": "^7.0 || ^8.0 || ^9.0 || ^10.0", "symfony/expression-language": "^5.3.0", "thenetworg/oauth2-azure": "^2.0", "verbb/base": "^2.0.9", "voku/anti-xss": "^4.1"}, "type": "craft-plugin", "extra": {"name": "<PERSON><PERSON>", "handle": "formie", "changelogUrl": "https://raw.githubusercontent.com/verbb/formie/craft-4/CHANGELOG.md", "class": "verbb\\formie\\Formie"}, "autoload": {"psr-4": {"verbb\\formie\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "The most user-friendly forms plugin for Craft.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "formie"], "support": {"email": "<EMAIL>", "issues": "https://github.com/verbb/formie/issues?state=open", "source": "https://github.com/verbb/formie", "docs": "https://github.com/verbb/formie", "rss": "https://github.com/verbb/formie/commits/v2.atom"}, "time": "2025-04-28T23:21:50+00:00"}, {"name": "verbb/icon-picker", "version": "2.0.20", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/icon-picker/zipball/c6ef40292e8beae6727db252d52af4e032e31ad4", "reference": "c6ef40292e8beae6727db252d52af4e032e31ad4", "shasum": ""}, "require": {"cakephp/core": "^3.5", "cakephp/utility": "^3.3.12", "craftcms/cms": "^4.0.0", "glenscott/url-normalizer": "^1.4", "nystudio107/craft-plugin-vite": "^4.0.0-beta.4", "phenx/php-font-lib": "^0.5.0", "php": "^8.0.2", "verbb/base": "^2.0.0"}, "type": "craft-plugin", "extra": {"name": "<PERSON><PERSON> Picker", "handle": "icon-picker", "changelogUrl": "https://raw.githubusercontent.com/verbb/icon-picker/craft-4/CHANGELOG.md", "class": "verbb\\iconpicker\\IconPicker"}, "autoload": {"psr-4": {"verbb\\iconpicker\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "A slick field to pick icons from. Supports SVGs, Sprites, Webfonts, Font Awesome and more.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "icon-picker"], "support": {"email": "<EMAIL>", "issues": "https://github.com/verbb/icon-picker/issues?state=open", "source": "https://github.com/verbb/icon-picker", "docs": "https://github.com/verbb/icon-picker", "rss": "https://github.com/verbb/icon-picker/commits/v2.atom"}, "time": "2025-02-02T01:09:30+00:00"}, {"name": "verbb/image-resizer", "version": "3.0.13", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/image-resizer/zipball/33ba642897780b5fb0a150abd7482a9d890918c4", "reference": "33ba642897780b5fb0a150abd7482a9d890918c4", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "lsolesen/pel": "^0.9.6", "php": "^8.0.2", "verbb/base": "^2.0.0"}, "type": "craft-plugin", "extra": {"name": "Image Resizer", "handle": "image-resizer", "description": "Image Resizer resizes your assets when they are uploaded.", "documentationUrl": "https://github.com/verbb/image-resizer", "changelogUrl": "https://raw.githubusercontent.com/verbb/image-resizer/craft-4/CHANGELOG.md", "class": "verbb\\imageresizer\\ImageResizer"}, "autoload": {"psr-4": {"verbb\\imageresizer\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Resize assets when they are uploaded.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "example"], "support": {"email": "<EMAIL>", "issues": "https://github.com/verbb/image-resizer/issues?state=open", "source": "https://github.com/verbb/image-resizer", "docs": "https://github.com/verbb/image-resizer", "rss": "https://github.com/verbb/image-resizer/commits/v2.atom"}, "time": "2025-03-04T01:58:43+00:00"}, {"name": "verbb/navigation", "version": "2.1.2", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/navigation/zipball/b65b5565c398b0b206e1aee10316f096180fb337", "reference": "b65b5565c398b0b206e1aee10316f096180fb337", "shasum": ""}, "require": {"craftcms/cms": "^4.5.0", "php": "^8.0.2", "verbb/base": "^2.0.0"}, "type": "craft-plugin", "extra": {"name": "Navigation", "handle": "navigation", "changelogUrl": "https://raw.githubusercontent.com/verbb/navigation/craft-4/CHANGELOG.md", "class": "verbb\\navigation\\Navigation"}, "autoload": {"psr-4": {"verbb\\navigation\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Create navigation menus for your site.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "menu", "navigation"], "support": {"email": "<EMAIL>", "issues": "https://github.com/verbb/navigation/issues?state=open", "source": "https://github.com/verbb/navigation", "docs": "https://github.com/verbb/navigation", "rss": "https://github.com/verbb/navigation/commits/v2.atom"}, "time": "2025-05-01T04:49:21+00:00"}, {"name": "verbb/smith", "version": "2.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/smith/zipball/c226aaab97199d8056135c3744237969cd197824", "reference": "c226aaab97199d8056135c3744237969cd197824", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0.2", "verbb/base": "^2.0.0"}, "type": "craft-plugin", "extra": {"name": "<PERSON>", "handle": "smith", "changelogUrl": "https://raw.githubusercontent.com/verbb/smith/craft-4/CHANGELOG.md", "class": "verbb\\smith\\Smith"}, "autoload": {"psr-4": {"verbb\\smith\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Add copy, paste and clone functionality to Matrix blocks.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "smith"], "support": {"email": "<EMAIL>", "issues": "https://github.com/verbb/smith/issues?state=open", "source": "https://github.com/verbb/smith", "docs": "https://github.com/verbb/smith", "rss": "https://github.com/verbb/smith/commits/v2.atom"}, "time": "2024-04-10T13:09:47+00:00"}, {"name": "verbb/super-table", "version": "3.0.15", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/super-table/zipball/ce8830aeb0cc5e8b5065d4e19925a60ddc3f3d4a", "reference": "ce8830aeb0cc5e8b5065d4e19925a60ddc3f3d4a", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0.2", "verbb/base": "^2.0.0"}, "type": "craft-plugin", "extra": {"name": "Super Table", "handle": "super-table", "description": "Super-charge your Craft workflow with Super Table. Use it to group fields together or build complex Matrix-in-Matrix solutions.", "documentationUrl": "https://github.com/verbb/super-table", "changelogUrl": "https://raw.githubusercontent.com/verbb/super-table/craft-4/CHANGELOG.md", "class": "verbb\\supertable\\SuperTable"}, "autoload": {"psr-4": {"verbb\\supertable\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Super-charge your content builders and create nested Matrix fields.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "super table"], "support": {"email": "<EMAIL>", "issues": "https://github.com/verbb/super-table/issues?state=open", "source": "https://github.com/verbb/super-table", "docs": "https://github.com/verbb/super-table", "rss": "https://github.com/verbb/super-table/commits/v2.atom"}, "time": "2024-09-13T02:39:50+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.5.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7", "reference": "1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.0.2", "php": "^7.1.3 || ^8.0", "phpoption/phpoption": "^1.8", "symfony/polyfill-ctype": "^1.23", "symfony/polyfill-mbstring": "^1.23.1", "symfony/polyfill-php80": "^1.23.1"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "time": "2022-10-16T01:01:54+00:00"}, {"name": "voku/anti-xss", "version": "4.1.42", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/anti-xss/zipball/bca1f8607e55a3c5077483615cd93bd8f11bd675", "reference": "bca1f8607e55a3c5077483615cd93bd8f11bd675", "shasum": ""}, "require": {"php": ">=7.0.0", "voku/portable-utf8": "~6.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}, "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "license": ["MIT"], "authors": [{"name": "EllisLab Dev Team", "homepage": "http://ellislab.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.moelleken.org/"}], "description": "anti xss-library", "homepage": "https://github.com/voku/anti-xss", "keywords": ["anti-xss", "clean", "security", "xss"], "time": "2023-07-03T14:40:46+00:00"}, {"name": "voku/arrayy", "version": "7.9.6", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/Arrayy/zipball/0e20b8c6eef7fc46694a2906e0eae2f9fc11cade", "reference": "0e20b8c6eef7fc46694a2906e0eae2f9fc11cade", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.0.0", "phpdocumentor/reflection-docblock": "~4.3 || ~5.0", "symfony/polyfill-mbstring": "~1.0"}, "type": "library", "autoload": {"files": ["src/Create.php"], "psr-4": {"Arrayy\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.moelleken.org/", "role": "Maintainer"}], "description": "Array manipulation library for PHP, called Arrayy!", "keywords": ["A<PERSON>yy", "array", "helpers", "manipulation", "methods", "utility", "utils"], "time": "2022-12-27T12:58:32+00:00"}, {"name": "voku/email-check", "version": "3.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/email-check/zipball/6ea842920bbef6758b8c1e619fd1710e7a1a2cac", "reference": "6ea842920bbef6758b8c1e619fd1710e7a1a2cac", "shasum": ""}, "require": {"php": ">=7.0.0", "symfony/polyfill-intl-idn": "~1.10"}, "suggest": {"ext-intl": "Use Intl for best performance"}, "type": "library", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "email-check (syntax, dns, trash, ...) library", "homepage": "https://github.com/voku/email-check", "keywords": ["check-email", "email", "mail", "mail-check", "validate-email", "validate-email-address", "validate-mail"], "time": "2021-01-27T14:14:33+00:00"}, {"name": "voku/portable-ascii", "version": "2.0.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b1d923f88091c6bf09699efcd7c8a1b1bfd7351d", "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d", "shasum": ""}, "require": {"php": ">=7.0.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "time": "2024-11-21T01:49:47+00:00"}, {"name": "voku/portable-utf8", "version": "6.0.13", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-utf8/zipball/b8ce36bf26593e5c2e81b1850ef0ffb299d2043f", "reference": "b8ce36bf26593e5c2e81b1850ef0ffb299d2043f", "shasum": ""}, "require": {"php": ">=7.0.0", "symfony/polyfill-iconv": "~1.0", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.0", "voku/portable-ascii": "~2.0.0"}, "suggest": {"ext-ctype": "Use Ctype for e.g. hexadecimal digit detection", "ext-fileinfo": "Use Fileinfo for better binary file detection", "ext-iconv": "Use iconv for best performance", "ext-intl": "Use Intl for best performance", "ext-json": "Use JSON for string detection", "ext-mbstring": "Use Mbstring for best performance"}, "type": "library", "autoload": {"files": ["bootstrap.php"], "psr-4": {"voku\\": "src/voku/"}}, "license": ["(Apache-2.0 or GPL-2.0)"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "http://pageconfig.com/"}, {"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable UTF-8 library - performance optimized (unicode) string functions for php.", "homepage": "https://github.com/voku/portable-utf8", "keywords": ["UTF", "clean", "php", "unicode", "utf-8", "utf8"], "time": "2023-03-08T08:35:38+00:00"}, {"name": "voku/stop-words", "version": "2.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/stop-words/zipball/8e63c0af20f800b1600783764e0ce19e53969f71", "reference": "8e63c0af20f800b1600783764e0ce19e53969f71", "shasum": ""}, "require": {"php": ">=7.0.0"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Stop-Words via PHP", "keywords": ["stop words", "stop-words"], "time": "2018-11-23T01:37:27+00:00"}, {"name": "voku/stringy", "version": "6.5.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/Stringy/zipball/c453c88fbff298f042c836ef44306f8703b2d537", "reference": "c453c88fbff298f042c836ef44306f8703b2d537", "shasum": ""}, "require": {"defuse/php-encryption": "~2.0", "ext-json": "*", "php": ">=7.0.0", "voku/anti-xss": "~4.1", "voku/arrayy": "~7.8", "voku/email-check": "~3.1", "voku/portable-ascii": "~2.0", "voku/portable-utf8": "~6.0", "voku/urlify": "~5.0"}, "replace": {"danielstjules/stringy": "~3.0"}, "type": "library", "autoload": {"files": ["src/Create.php"], "psr-4": {"Stringy\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.moelleken.org/", "role": "Fork-Maintainer"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "time": "2022-03-28T14:52:20+00:00"}, {"name": "voku/urlify", "version": "5.0.7", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/urlify/zipball/014b2074407b5db5968f836c27d8731934b330e4", "reference": "014b2074407b5db5968f836c27d8731934b330e4", "shasum": ""}, "require": {"php": ">=7.0.0", "voku/portable-ascii": "~2.0", "voku/portable-utf8": "~6.0", "voku/stop-words": "~2.0"}, "type": "library", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.johnnybroadway.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://moelleken.org/"}], "description": "PHP port of URLify.js from the Django project. Transliterates non-ascii characters for use in URLs.", "homepage": "https://github.com/voku/urlify", "keywords": ["encode", "iconv", "link", "slug", "translit", "transliterate", "transliteration", "url", "urlify"], "time": "2022-01-24T19:08:46+00:00"}, {"name": "wbrowar/craft-admin-bar", "version": "4.3.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/wbrowar/craft-admin-bar/zipball/0bbe369c4cde11423623cae841c210dd6c504bbe", "reference": "0bbe369c4cde11423623cae841c210dd6c504bbe", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "nystudio107/craft-code-editor": "^1.0.15"}, "type": "craft-plugin", "extra": {"name": "Admin Bar", "handle": "admin-bar", "schemaVersion": "3.1.0", "hasCpSettings": true, "hasCpSection": false, "developerUrl": "https://wbrowar.com", "documentationUrl": "https://raw.githubusercontent.com/wbrowar/craft-admin-bar/main/README.md", "changelogUrl": "https://raw.githubusercontent.com/wbrowar/craft-admin-bar/main/CHANGELOG.md", "class": "wbrowar\\adminbar\\AdminBar"}, "autoload": {"psr-4": {"wbrowar\\adminbar\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://wbrowar.com"}], "description": "Front-end shortcuts for clients logged into Craft CMS.", "keywords": ["admin bar", "admin toolbar", "admin-bar", "author experience", "ax", "cms", "craft", "craft-plugin", "craftcms", "toolbar"], "support": {"docs": "https://raw.githubusercontent.com/wbrowar/craft-admin-bar/main/README.md", "issues": "https://github.com/wbrowar/craft-admin-bar/issues"}, "time": "2024-02-10T02:17:02+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2022-06-03T18:03:27+00:00"}, {"name": "webonyx/graphql-php", "version": "v14.11.10", "dist": {"type": "zip", "url": "https://api.github.com/repos/webonyx/graphql-php/zipball/d9c2fdebc6aa01d831bc2969da00e8588cffef19", "reference": "d9c2fdebc6aa01d831bc2969da00e8588cffef19", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^7.1 || ^8"}, "suggest": {"psr/http-message": "To use standard GraphQL server", "react/promise": "To leverage async resolving on React PHP platform"}, "type": "library", "autoload": {"psr-4": {"GraphQL\\": "src/"}}, "license": ["MIT"], "description": "A PHP port of GraphQL reference implementation", "homepage": "https://github.com/webonyx/graphql-php", "keywords": ["api", "graphql"], "time": "2023-07-05T14:23:37+00:00"}, {"name": "yiisoft/yii2", "version": "2.0.52", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-framework.git", "reference": "540e7387d934c52e415614aa081fb38d04c72d9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/540e7387d934c52e415614aa081fb38d04c72d9a", "reference": "540e7387d934c52e415614aa081fb38d04c72d9a", "shasum": ""}, "require": {"bower-asset/inputmask": "^5.0.8 ", "bower-asset/jquery": "3.7.*@stable | 3.6.*@stable | 3.5.*@stable | 3.4.*@stable | 3.3.*@stable | 3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "^1.4", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0 | ~1.2.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "^4.17", "lib-pcre": "*", "php": ">=7.3.0", "yiisoft/yii2-composer": "~2.0.4"}, "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "https://www.yiiframework.com/", "keywords": ["framework", "yii2"], "support": {"forum": "https://forum.yiiframework.com/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2/issues?state=open", "source": "https://github.com/yiisoft/yii2", "wiki": "https://www.yiiframework.com/wiki"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2", "type": "tidelift"}], "time": "2025-02-13T20:02:28+00:00"}, {"name": "yiisoft/yii2-composer", "version": "2.0.11", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-composer.git", "reference": "b684b01ecb119c8287721def726a0e24fec2fef2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/b684b01ecb119c8287721def726a0e24fec2fef2", "reference": "b684b01ecb119c8287721def726a0e24fec2fef2", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 | ^2.0"}, "require-dev": {"composer/composer": "^1.0 | ^2.0@dev", "phpunit/phpunit": "<7"}, "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\composer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-composer/issues", "source": "https://github.com/yiisoft/yii2-composer", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-composer", "type": "tidelift"}], "time": "2025-02-13T20:59:36+00:00"}, {"name": "yiisoft/yii2-debug", "version": "2.1.26", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-debug.git", "reference": "e4b28a1d295fc977d8399db544336dd5b2764397"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/e4b28a1d295fc977d8399db544336dd5b2764397", "reference": "e4b28a1d295fc977d8399db544336dd5b2764397", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4", "yiisoft/yii2": "~2.0.13"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34", "yiisoft/yii2-coding-standards": "~2.0", "yiisoft/yii2-swiftmailer": "*"}, "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true}, "autoload": {"psr-4": {"yii\\debug\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "dev", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-debug/issues", "source": "https://github.com/yiisoft/yii2-debug", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-debug", "type": "tidelift"}], "time": "2025-02-13T21:27:29+00:00"}, {"name": "yiisoft/yii2-queue", "version": "2.3.7", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-queue/zipball/dbc9d4a7b2a6995cd19c3e334227482ef55e559b", "reference": "dbc9d4a7b2a6995cd19c3e334227482ef55e559b", "shasum": ""}, "require": {"php": ">=5.5.0", "symfony/process": "^3.3||^4.0||^5.0||^6.0||^7.0", "yiisoft/yii2": "~2.0.14"}, "suggest": {"aws/aws-sdk-php": "Need for aws SQS.", "enqueue/amqp-lib": "Need for AMQP interop queue.", "enqueue/stomp": "Need for Stomp queue.", "ext-gearman": "Need for Gearman queue.", "ext-pcntl": "Need for process signals.", "pda/pheanstalk": "Need for Beanstalk queue.", "php-amqplib/php-amqplib": "Need for AMQP queue.", "yiisoft/yii2-redis": "Need for Redis queue."}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\queue\\": "src", "yii\\queue\\db\\": "src/drivers/db", "yii\\queue\\sqs\\": "src/drivers/sqs", "yii\\queue\\amqp\\": "src/drivers/amqp", "yii\\queue\\file\\": "src/drivers/file", "yii\\queue\\sync\\": "src/drivers/sync", "yii\\queue\\redis\\": "src/drivers/redis", "yii\\queue\\stomp\\": "src/drivers/stomp", "yii\\queue\\gearman\\": "src/drivers/gearman", "yii\\queue\\beanstalk\\": "src/drivers/beanstalk", "yii\\queue\\amqp_interop\\": "src/drivers/amqp_interop"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "z<PERSON>av<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Yii2 Queue Extension which supported DB, Redis, RabbitMQ, Beanstalk, SQS and Gearman", "keywords": ["async", "beanstalk", "db", "gearman", "gii", "queue", "rabbitmq", "redis", "sqs", "yii"], "time": "2024-04-29T09:40:52+00:00"}, {"name": "yiisoft/yii2-symfonymailer", "version": "2.0.4", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-symfonymailer/zipball/82f5902551a160633c4734b5096977ce76a809d9", "reference": "82f5902551a160633c4734b5096977ce76a809d9", "shasum": ""}, "require": {"php": ">=7.4.0", "symfony/mailer": ">=5.4.0", "yiisoft/yii2": ">=2.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\symfonymailer\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The SymfonyMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "symfony", "symfonymailer", "yii2"], "time": "2022-09-04T10:48:21+00:00"}], "packages-dev": [{"name": "amphp/amp", "version": "v3.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/amp/zipball/7cf7fef3d667bfe4b2560bc87e67d5387a7bcde9", "reference": "7cf7fef3d667bfe4b2560bc87e67d5387a7bcde9", "shasum": ""}, "require": {"php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "type": "library", "autoload": {"files": ["src/functions.php", "src/Future/functions.php", "src/Internal/functions.php"], "psr-4": {"Amp\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A non-blocking concurrency framework for PHP applications.", "homepage": "https://amphp.org/amp", "keywords": ["async", "asynchronous", "awaitable", "concurrency", "event", "event-loop", "future", "non-blocking", "promise"], "time": "2025-01-26T16:07:39+00:00"}, {"name": "amphp/byte-stream", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/amphp/byte-stream.git", "reference": "55a6bd071aec26fa2a3e002618c20c35e3df1b46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/byte-stream/zipball/55a6bd071aec26fa2a3e002618c20c35e3df1b46", "reference": "55a6bd071aec26fa2a3e002618c20c35e3df1b46", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/parser": "^1.1", "amphp/pipeline": "^1", "amphp/serialization": "^1", "amphp/sync": "^2", "php": ">=8.1", "revolt/event-loop": "^1 || ^0.2.3"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "psalm/phar": "5.22.1"}, "type": "library", "autoload": {"files": ["src/functions.php", "src/Internal/functions.php"], "psr-4": {"Amp\\ByteStream\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A stream abstraction to make working with non-blocking I/O simple.", "homepage": "https://amphp.org/byte-stream", "keywords": ["amp", "amphp", "async", "io", "non-blocking", "stream"], "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.1.2"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2025-03-16T17:10:27+00:00"}, {"name": "amphp/cache", "version": "v2.0.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/cache/zipball/46912e387e6aa94933b61ea1ead9cf7540b7797c", "reference": "46912e387e6aa94933b61ea1ead9cf7540b7797c", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/serialization": "^1", "amphp/sync": "^2", "php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "type": "library", "autoload": {"psr-4": {"Amp\\Cache\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A fiber-aware cache API based on Amp and Revolt.", "homepage": "https://amphp.org/cache", "time": "2024-04-19T03:38:06+00:00"}, {"name": "amphp/dns", "version": "v2.4.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/dns/zipball/78eb3db5fc69bf2fc0cb503c4fcba667bc223c71", "reference": "78eb3db5fc69bf2fc0cb503c4fcba667bc223c71", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/cache": "^2", "amphp/parser": "^1", "amphp/process": "^2", "daverandom/libdns": "^2.0.2", "ext-filter": "*", "ext-json": "*", "php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Dns\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Async DNS resolution for Amp.", "homepage": "https://github.com/amphp/dns", "keywords": ["amp", "amphp", "async", "client", "dns", "resolve"], "time": "2025-01-19T15:43:40+00:00"}, {"name": "amphp/parallel", "version": "v2.3.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/parallel/zipball/5113111de02796a782f5d90767455e7391cca190", "reference": "5113111de02796a782f5d90767455e7391cca190", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/cache": "^2", "amphp/parser": "^1", "amphp/pipeline": "^1", "amphp/process": "^2", "amphp/serialization": "^1", "amphp/socket": "^2", "amphp/sync": "^2", "php": ">=8.1", "revolt/event-loop": "^1"}, "type": "library", "autoload": {"files": ["src/Context/functions.php", "src/Context/Internal/functions.php", "src/Ipc/functions.php", "src/Worker/functions.php"], "psr-4": {"Amp\\Parallel\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parallel processing component for Amp.", "homepage": "https://github.com/amphp/parallel", "keywords": ["async", "asynchronous", "concurrent", "multi-processing", "multi-threading"], "time": "2024-12-21T01:56:09+00:00"}, {"name": "amphp/parser", "version": "v1.1.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/parser/zipball/3cf1f8b32a0171d4b1bed93d25617637a77cded7", "reference": "3cf1f8b32a0171d4b1bed93d25617637a77cded7", "shasum": ""}, "require": {"php": ">=7.4"}, "type": "library", "autoload": {"psr-4": {"Amp\\Parser\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A generator parser to make streaming parsers simple.", "homepage": "https://github.com/amphp/parser", "keywords": ["async", "non-blocking", "parser", "stream"], "time": "2024-03-21T19:16:53+00:00"}, {"name": "amphp/pipeline", "version": "v1.2.3", "source": {"type": "git", "url": "https://github.com/amphp/pipeline.git", "reference": "7b52598c2e9105ebcddf247fc523161581930367"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/pipeline/zipball/7b52598c2e9105ebcddf247fc523161581930367", "reference": "7b52598c2e9105ebcddf247fc523161581930367", "shasum": ""}, "require": {"amphp/amp": "^3", "php": ">=8.1", "revolt/event-loop": "^1"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "psalm/phar": "^5.18"}, "type": "library", "autoload": {"psr-4": {"Amp\\Pipeline\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Asynchronous iterators and operators.", "homepage": "https://amphp.org/pipeline", "keywords": ["amp", "amphp", "async", "io", "iterator", "non-blocking"], "support": {"issues": "https://github.com/amphp/pipeline/issues", "source": "https://github.com/amphp/pipeline/tree/v1.2.3"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2025-03-16T16:33:53+00:00"}, {"name": "amphp/process", "version": "v2.0.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/process/zipball/52e08c09dec7511d5fbc1fb00d3e4e79fc77d58d", "reference": "52e08c09dec7511d5fbc1fb00d3e4e79fc77d58d", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/sync": "^2", "php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Process\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A fiber-aware process manager based on Amp and Revolt.", "homepage": "https://amphp.org/process", "time": "2024-04-19T03:13:44+00:00"}, {"name": "amphp/serialization", "version": "v1.0.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/serialization/zipball/693e77b2fb0b266c3c7d622317f881de44ae94a1", "reference": "693e77b2fb0b266c3c7d622317f881de44ae94a1", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Serialization\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Serialization tools for IPC and data storage in PHP.", "homepage": "https://github.com/amphp/serialization", "keywords": ["async", "asynchronous", "serialization", "serialize"], "time": "2020-03-25T21:39:07+00:00"}, {"name": "amphp/socket", "version": "v2.3.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/socket/zipball/58e0422221825b79681b72c50c47a930be7bf1e1", "reference": "58e0422221825b79681b72c50c47a930be7bf1e1", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/dns": "^2", "ext-openssl": "*", "kelunik/certificate": "^1.1", "league/uri": "^6.5 | ^7", "league/uri-interfaces": "^2.3 | ^7", "php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "type": "library", "autoload": {"files": ["src/functions.php", "src/Internal/functions.php", "src/SocketAddress/functions.php"], "psr-4": {"Amp\\Socket\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Non-blocking socket connection / server implementations based on Amp and Revolt.", "homepage": "https://github.com/amphp/socket", "keywords": ["amp", "async", "encryption", "non-blocking", "sockets", "tcp", "tls"], "time": "2024-04-21T14:33:03+00:00"}, {"name": "amphp/sync", "version": "v2.3.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/sync/zipball/217097b785130d77cfcc58ff583cf26cd1770bf1", "reference": "217097b785130d77cfcc58ff583cf26cd1770bf1", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/pipeline": "^1", "amphp/serialization": "^1", "php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Sync\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Non-blocking synchronization primitives for PHP based on Amp and Revolt.", "homepage": "https://github.com/amphp/sync", "keywords": ["async", "asynchronous", "mutex", "semaphore", "synchronization"], "time": "2024-08-03T19:31:26+00:00"}, {"name": "daverandom/libdns", "version": "v2.1.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/DaveRandom/LibDNS/zipball/b84c94e8fe6b7ee4aecfe121bfe3b6177d303c8a", "reference": "b84c94e8fe6b7ee4aecfe121bfe3b6177d303c8a", "shasum": ""}, "require": {"ext-ctype": "*", "php": ">=7.1"}, "suggest": {"ext-intl": "Required for IDN support"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"LibDNS\\": "src/"}}, "license": ["MIT"], "description": "DNS protocol implementation written in pure PHP", "keywords": ["dns"], "time": "2024-04-12T12:12:48+00:00"}, {"name": "friendsoftwig/twigcs", "version": "6.5.0", "source": {"type": "git", "url": "https://github.com/friendsoftwig/twigcs.git", "reference": "aaa3ba112bf4fcee7b51a00d9b45b13bc2cc23bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/friendsoftwig/twigcs/zipball/aaa3ba112bf4fcee7b51a00d9b45b13bc2cc23bc", "reference": "aaa3ba112bf4fcee7b51a00d9b45b13bc2cc23bc", "shasum": ""}, "require": {"ext-ctype": "*", "ext-hash": "*", "ext-json": "*", "ext-mbstring": "*", "ext-simplexml": "*", "php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "symfony/console": "^4.4 || ^5.3 || ^6.0 || ^7.0", "symfony/filesystem": "^4.4 || ^5.3 || ^6.0 || ^7.0", "symfony/finder": "^4.4 || ^5.3 || ^6.0 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^9.6.19", "symfony/phpunit-bridge": "^7.1.4"}, "bin": ["bin/twigcs"], "type": "library", "autoload": {"psr-4": {"FriendsOfTwig\\Twigcs\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Checkstyle automation for Twig", "support": {"issues": "https://github.com/friendsoftwig/twigcs/issues", "source": "https://github.com/friendsoftwig/twigcs/tree/6.5.0"}, "time": "2024-11-27T21:59:24+00:00"}, {"name": "gitonomy/gitlib", "version": "v1.5.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/gitonomy/gitlib/zipball/ac17834888bf399a4ecae5e108be52c8c5f93958", "reference": "ac17834888bf399a4ecae5e108be52c8c5f93958", "shasum": ""}, "require": {"ext-pcre": "*", "php": "^8.0", "symfony/polyfill-mbstring": "^1.7", "symfony/process": "^5.4 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Gitonomy\\Git\\": "src/Gitonomy/Git/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/juliendidier"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/lyrixx"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/alexandresalome"}], "description": "Library for accessing git", "time": "2024-11-03T15:59:21+00:00"}, {"name": "kelunik/certificate", "version": "v1.1.3", "dist": {"type": "zip", "url": "https://api.github.com/repos/kelunik/certificate/zipball/7e00d498c264d5eb4f78c69f41c8bd6719c0199e", "reference": "7e00d498c264d5eb4f78c69f41c8bd6719c0199e", "shasum": ""}, "require": {"ext-openssl": "*", "php": ">=7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Kelunik\\Certificate\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Access certificate details and transform between different formats.", "keywords": ["DER", "certificate", "certificates", "openssl", "pem", "x509"], "time": "2023-02-03T21:26:53+00:00"}, {"name": "laravel/serializable-closure", "version": "v2.0.4", "source": {"type": "git", "url": "https://github.com/laravel/serializable-closure.git", "reference": "b352cf0534aa1ae6b4d825d1e762e35d43f8a841"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/serializable-closure/zipball/b352cf0534aa1ae6b4d825d1e762e35d43f8a841", "reference": "b352cf0534aa1ae6b4d825d1e762e35d43f8a841", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"illuminate/support": "^10.0|^11.0|^12.0", "nesbot/carbon": "^2.67|^3.0", "pestphp/pest": "^2.36|^3.0", "phpstan/phpstan": "^2.0", "symfony/var-dumper": "^6.2.0|^7.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Laravel\\SerializableClosure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel Serializable Closure provides an easy and secure way to serialize closures in PHP.", "keywords": ["closure", "laravel", "serializable"], "support": {"issues": "https://github.com/laravel/serializable-closure/issues", "source": "https://github.com/laravel/serializable-closure"}, "time": "2025-03-19T13:51:03+00:00"}, {"name": "league/uri", "version": "7.5.1", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri/zipball/81fb5145d2644324614cc532b28efd0215bda430", "reference": "81fb5145d2644324614cc532b28efd0215bda430", "shasum": ""}, "require": {"league/uri-interfaces": "^7.5", "php": "^8.1"}, "conflict": {"league/uri-schemes": "^1.0"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-fileinfo": "to create Data URI from file contennts", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "jeremykendall/php-domain-parser": "to resolve Public Suffix and Top Level Domain", "league/uri-components": "Needed to easily manipulate URI objects components", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "middleware", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "uri-template", "url", "ws"], "time": "2024-12-08T08:40:02+00:00"}, {"name": "league/uri-interfaces", "version": "7.5.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/08cfc6c4f3d811584fb09c37e2849e6a7f9b0742", "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742", "shasum": ""}, "require": {"ext-filter": "*", "php": "^8.1", "psr/http-factory": "^1", "psr/http-message": "^1.1 || ^2.0"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "Common interfaces and classes for URI representation and interaction", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "url", "ws"], "time": "2024-12-08T08:18:47+00:00"}, {"name": "nikic/php-parser", "version": "v5.4.0", "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/447a020a1f875a434d62f2a401f53b82a396e494", "reference": "447a020a1f875a434d62f2a401f53b82a396e494", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2024-12-30T11:07:19+00:00"}, {"name": "nystudio107/craft-autocomplete", "version": "1.12.2", "source": {"type": "git", "url": "https://github.com/nystudio107/craft-autocomplete.git", "reference": "903d74ed3315a0c61297700063ba6071d4e44f6d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-autocomplete/zipball/903d74ed3315a0c61297700063ba6071d4e44f6d", "reference": "903d74ed3315a0c61297700063ba6071d4e44f6d", "shasum": ""}, "require": {"craftcms/cms": "^3.0.0 || ^4.0.0 || ^5.0.0"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main", "craftcms/rector": "dev-main"}, "type": "yii2-extension", "extra": {"bootstrap": "nystudio107\\autocomplete\\Autocomplete"}, "autoload": {"psr-4": {"nystudio107\\autocomplete\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com"}, {"name": "PutYourLightsOn", "homepage": "https://putyourlightson.com"}], "description": "Provides Twig template IDE autocomplete of Craft CMS & plugin variables", "keywords": ["Craft", "auto", "autocomplete", "cms", "complete", "craftcms", "symfony", "twig"], "support": {"docs": "https://github.com/nystudio107/craft-autocomplete/blob/v1/README.md", "issues": "https://github.com/nystudio107/craft-autocomplete/issues", "source": "https://github.com/nystudio107/craft-autocomplete"}, "funding": [{"url": "https://github.com/khalwat", "type": "github"}], "time": "2024-09-23T17:51:10+00:00"}, {"name": "ondram/ci-detector", "version": "4.2.0", "source": {"type": "git", "url": "https://github.com/OndraM/ci-detector.git", "reference": "8b0223b5ed235fd377c75fdd1bfcad05c0f168b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/OndraM/ci-detector/zipball/8b0223b5ed235fd377c75fdd1bfcad05c0f168b8", "reference": "8b0223b5ed235fd377c75fdd1bfcad05c0f168b8", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.13.2", "lmc/coding-standard": "^3.0.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.1.0", "phpstan/phpstan": "^1.2.0", "phpstan/phpstan-phpunit": "^1.0.0", "phpunit/phpunit": "^9.6.13"}, "type": "library", "autoload": {"psr-4": {"OndraM\\CiDetector\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Detect continuous integration environment and provide unified access to properties of current build", "keywords": ["CircleCI", "Codeship", "<PERSON><PERSON><PERSON>", "adapter", "appveyor", "aws", "aws codebuild", "azure", "azure devops", "azure pipelines", "bamboo", "bitbucket", "buddy", "ci-info", "codebuild", "continuous integration", "continuousphp", "devops", "drone", "github", "gitlab", "interface", "jenkins", "pipelines", "<PERSON><PERSON><PERSON>", "teamcity", "travis"], "support": {"issues": "https://github.com/OndraM/ci-detector/issues", "source": "https://github.com/OndraM/ci-detector/tree/4.2.0"}, "time": "2024-03-12T13:22:30+00:00"}, {"name": "php-parallel-lint/php-parallel-lint", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/php-parallel-lint/PHP-Parallel-Lint.git", "reference": "6db563514f27e19595a19f45a4bf757b6401194e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-parallel-lint/PHP-Parallel-Lint/zipball/6db563514f27e19595a19f45a4bf757b6401194e", "reference": "6db563514f27e19595a19f45a4bf757b6401194e", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.3.0"}, "replace": {"grogy/php-parallel-lint": "*", "jakub-onderka/php-parallel-lint": "*"}, "require-dev": {"nette/tester": "^1.3 || ^2.0", "php-parallel-lint/php-console-highlighter": "0.* || ^1.0", "squizlabs/php_codesniffer": "^3.6"}, "suggest": {"php-parallel-lint/php-console-highlighter": "Highlight syntax in code snippet"}, "bin": ["parallel-lint"], "type": "library", "autoload": {"classmap": ["./src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This tool checks the syntax of PHP files about 20x faster than serial check.", "homepage": "https://github.com/php-parallel-lint/PHP-Parallel-Lint", "keywords": ["lint", "static analysis"], "support": {"issues": "https://github.com/php-parallel-lint/PHP-Parallel-Lint/issues", "source": "https://github.com/php-parallel-lint/PHP-Parallel-Lint/tree/v1.4.0"}, "time": "2024-03-27T12:14:49+00:00"}, {"name": "phpro/grumphp", "version": "v2.12.0", "source": {"type": "git", "url": "https://github.com/phpro/grumphp.git", "reference": "7ea467d0d20353791dda798a0679aae00224daae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpro/grumphp/zipball/7ea467d0d20353791dda798a0679aae00224daae", "reference": "7ea467d0d20353791dda798a0679aae00224daae", "shasum": ""}, "require": {"amphp/amp": "^3.0", "amphp/parallel": "^2.1", "composer-plugin-api": "^2.0", "doctrine/collections": "^1.6.8 || ^2.0", "ext-json": "*", "gitonomy/gitlib": "^1.3", "laravel/serializable-closure": "^2.0", "monolog/monolog": "^2.0 || ^3.0", "ondram/ci-detector": "^4.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/container": "^1.1 || ^2.0", "seld/jsonlint": "^1.8", "symfony/config": "^5.4 || ^6.0 || ^7.0", "symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/dependency-injection": "^5.4 || ^6.0 || ^7.0", "symfony/dotenv": "^5.4 || ^6.0 || ^7.0", "symfony/event-dispatcher": "^5.4 || ^6.0 || ^7.0", "symfony/expression-language": "^5.4 || ^6.0 || ^7.0", "symfony/filesystem": "^5.4 || ^6.0 || ^7.0", "symfony/finder": "^5.4 || ^6.0 || ^7.0", "symfony/options-resolver": "^5.4 || ^6.0 || ^7.0", "symfony/process": "^5.4 || ^6.0 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"brianium/paratest": "^6.4", "composer/composer": "^2.2.6", "nikic/php-parser": "^5.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpspec/phpspec": "^7.2", "phpspec/prophecy-phpunit": "^2.2", "phpunit/phpunit": "^9.5.13"}, "suggest": {"atoum/atoum": "Lets GrumPHP run your unit tests.", "behat/behat": "Lets GrumPHP validate your project features.", "brianium/paratest": "Lets GrumPHP run PHPUnit in parallel.", "codeception/codeception": "Lets GrumPHP run your project's full stack tests", "consolidation/robo": "Lets GrumPHP run your automated PHP tasks.", "designsecurity/progpilot": "Lets GrumPHP be sure that there are no vulnerabilities in your code.", "doctrine/orm": "Lets GrumPHP validate your Doctrine mapping files.", "enlightn/security-checker": "Lets GrumPHP be sure that there are no known security issues.", "ergebnis/composer-normalize": "Lets GrumPHP tidy and normalize your composer.json file.", "friendsofphp/php-cs-fixer": "Lets GrumPHP automatically fix your codestyle.", "friendsoftwig/twigcs": "Lets GrumPHP check Twig coding standard.", "infection/infection": "Lets GrumPHP evaluate the quality your unit tests", "maglnet/composer-require-checker": "Lets GrumPHP analyze composer dependencies.", "malukenho/kawaii-gherkin": "Lets GrumPHP lint your Gherkin files.", "nette/tester": "Lets GrumPHP run your unit tests with nette tester.", "nikic/php-parser": "Lets GrumPHP run static analyses through your PHP files.", "pestphp/pest": "Lets GrumPHP run your unit test with Pest PHP", "phan/phan": "Lets GrumPHP unleash a static analyzer on your code", "phing/phing": "Lets GrumPHP run your automated PHP tasks.", "php-parallel-lint/php-parallel-lint": "Lets GrumPHP quickly lint your entire code base.", "phparkitect/phparkitect": "Let GrumPHP keep your codebase coherent and solid, by permitting to add some architectural constraint check to your workflow.", "phpmd/phpmd": "Lets GrumPHP sort out the mess in your code", "phpspec/phpspec": "Lets GrumPHP spec your code.", "phpstan/phpstan": "Lets GrumPHP discover bugs in your code without running it.", "phpunit/phpunit": "Lets GrumPHP run your unit tests.", "povils/phpmnd": "Lets GrumPHP help you detect magic numbers in PHP code.", "rector/rector ": "Lets GrumPHP instantly upgrade and automatically refactor your PHP code.", "roave/security-advisories": "Lets GrumPHP be sure that there are no known security issues.", "sebastian/phpcpd": "Lets GrumPHP find duplicated code.", "squizlabs/php_codesniffer": "Lets GrumPHP sniff on your code.", "sstalle/php7cc": "Lets GrumPHP check PHP 5.3 - 5.6 code compatibility with PHP 7.", "symfony/phpunit-bridge": "Lets GrumPHP run your unit tests with the phpunit-bridge of Symfony.", "symplify/easy-coding-standard": "Lets GrumPHP check coding standard.", "vimeo/psalm": "Lets GrumPHP discover errors in your code without running it.", "vincentlanglet/twig-cs-fixer": "Lets GrumPHP check and fix twig coding standard."}, "bin": ["bin/grumphp"], "type": "composer-plugin", "extra": {"class": "GrumPHP\\Composer\\GrumPHPPlugin"}, "autoload": {"psr-4": {"GrumPHP\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Toon Verwerft", "email": "<EMAIL>"}, {"name": "Community", "homepage": "https://github.com/phpro/grumphp/graphs/contributors"}], "description": "A composer plugin that enables source code quality checks.", "support": {"issues": "https://github.com/phpro/grumphp/issues", "source": "https://github.com/phpro/grumphp/tree/v2.12.0"}, "time": "2025-03-21T14:40:46+00:00"}, {"name": "phpstan/phpstan", "version": "1.12.26", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "84cbf8f018e01834b9b1ac3dacf3b9780e209e53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/84cbf8f018e01834b9b1ac3dacf3b9780e209e53", "reference": "84cbf8f018e01834b9b1ac3dacf3b9780e209e53", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "time": "2025-05-14T11:08:32+00:00"}, {"name": "psy/psysh", "version": "v0.12.8", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "85057ceedee50c49d4f6ecaff73ee96adb3b3625"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/85057ceedee50c49d4f6ecaff73ee96adb3b3625", "reference": "85057ceedee50c49d4f6ecaff73ee96adb3b3625", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "php": "^8.0 || ^7.4", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."}, "bin": ["bin/psysh"], "type": "library", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}, "branch-alias": {"dev-main": "0.12.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.8"}, "time": "2025-03-16T03:05:19+00:00"}, {"name": "revolt/event-loop", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/revoltphp/event-loop.git", "reference": "09bf1bf7f7f574453efe43044b06fafe12216eb3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/revoltphp/event-loop/zipball/09bf1bf7f7f574453efe43044b06fafe12216eb3", "reference": "09bf1bf7f7f574453efe43044b06fafe12216eb3", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-json": "*", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^9", "psalm/phar": "^5.15"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Revolt\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Rock-solid event loop for concurrent PHP applications.", "keywords": ["async", "asynchronous", "concurrency", "event", "event-loop", "non-blocking", "scheduler"], "support": {"issues": "https://github.com/revoltphp/event-loop/issues", "source": "https://github.com/revoltphp/event-loop/tree/v1.0.7"}, "time": "2025-01-25T19:27:39+00:00"}, {"name": "studio-stomp/phpstan-craftcms", "version": "dev-main", "source": {"type": "git", "url": "https://github.com/studio-stomp/phpstan-craftcms.git", "reference": "5bca82d3f0e2723c109cf3cd4b73d22043cf959c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/studio-stomp/phpstan-craftcms/zipball/5bca82d3f0e2723c109cf3cd4b73d22043cf959c", "reference": "5bca82d3f0e2723c109cf3cd4b73d22043cf959c", "shasum": ""}, "require": {"php": "^8.0", "phpstan/phpstan": "^1.9"}, "require-dev": {"roave/security-advisories": "dev-latest"}, "default-branch": true, "type": "phpstan-extension", "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Studio Stomp", "email": "<EMAIL>", "homepage": "https://studiostomp.nl/", "role": "Developer"}], "description": "Craft CMS extension for PHPStan", "homepage": "https://github.com/studio-stomp/phpstan-craftcms", "keywords": ["phpstan-craftcms", "studio-stomp"], "support": {"issues": "https://github.com/studio-stomp/phpstan-craftcms/issues", "source": "https://github.com/studio-stomp/phpstan-craftcms/tree/main"}, "time": "2022-11-06T13:21:55+00:00"}, {"name": "symfony/config", "version": "v6.4.14", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "4e55e7e4ffddd343671ea972216d4509f46c22ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/4e55e7e4ffddd343671ea972216d4509f46c22ef", "reference": "4e55e7e4ffddd343671ea972216d4509f46c22ef", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<5.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v6.4.14"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-04T11:33:53+00:00"}, {"name": "symfony/dependency-injection", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "c49796a9184a532843e78e50df9e55708b92543a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/c49796a9184a532843e78e50df9e55708b92543a", "reference": "c49796a9184a532843e78e50df9e55708b92543a", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4.20|^7.2.5"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<6.1", "symfony/finder": "<5.4", "symfony/proxy-manager-bridge": "<6.3", "symfony/yaml": "<5.4"}, "provide": {"psr/container-implementation": "1.1|2.0", "symfony/service-implementation": "1.1|2.0|3.0"}, "require-dev": {"symfony/config": "^6.1|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T09:55:08+00:00"}, {"name": "symfony/dotenv", "version": "v6.4.16", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "1ac5e7e7e862d4d574258daf08bd569ba926e4a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/1ac5e7e7e862d4d574258daf08bd569ba926e4a5", "reference": "1ac5e7e7e862d4d574258daf08bd569ba926e4a5", "shasum": ""}, "require": {"php": ">=8.1"}, "conflict": {"symfony/console": "<5.4", "symfony/process": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v6.4.16"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-27T11:08:19+00:00"}, {"name": "symfony/options-resolver", "version": "v6.4.16", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/368128ad168f20e22c32159b9f761e456cec0c78", "reference": "368128ad168f20e22c32159b9f761e456cec0c78", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "time": "2024-11-20T10:57:02+00:00"}, {"name": "yiisoft/yii2-shell", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-shell.git", "reference": "c0aef8874eb6e9e6a56cf2972e422457008f9e18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-shell/zipball/c0aef8874eb6e9e6a56cf2972e422457008f9e18", "reference": "c0aef8874eb6e9e6a56cf2972e422457008f9e18", "shasum": ""}, "require": {"psy/psysh": "~0.9.3|~0.10.3|^0.11.0|^0.12.0", "symfony/var-dumper": "~2.7|~3.0|~4.0|~5.0", "yiisoft/yii2": "~2.0.0"}, "type": "yii2-extension", "extra": {"bootstrap": "yii\\shell\\Bootstrap", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\shell\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "svku<PERSON><PERSON>@gmail.com"}], "description": "The interactive shell extension for Yii framework", "keywords": ["shell", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-shell/issues", "source": "https://github.com/yiisoft/yii2-shell", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://opencollective.com/yiisoft", "type": "open_collective"}], "time": "2025-02-13T21:05:12+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {"redkiwi/craft-endpoints": 20, "studio-stomp/phpstan-craftcms": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "platform-overrides": {"php": "8.1"}, "plugin-api-version": "2.6.0"}