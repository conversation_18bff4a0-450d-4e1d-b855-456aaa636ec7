# General Settings
CP_TRIGGER=beheer
PRIMARY_SITE_URL=https://msn.nl.internal/
CRAFT_APP_ID=CraftCMS--c156782a-9c95-40e4-8cfa-f3219a3275d4
POSTALCODE_API_KEY="op://Operations/Postcode API MSN/credential"# find me in 1password
GOOGLE_API_KEY="op://Operations/MSN Google API/credential"# find me in 1password (production) or google cloud console (local/test/accept)

CRAFT_SECURITY_KEY="op://Operations/MSN Craft security key/CRAFT_SECURITY_KEY"# find me in 1password (under "MSN Craft security key)

CRAFT_ENVIRONMENT=dev
ENVIRONMENT=dev

# Database Configuration
CRAFT_DB_DRIVER=mysql
CRAFT_DB_SERVER=ddev-msn-db
CRAFT_DB_PORT=3306
CRAFT_DB_DATABASE=db
CRAFT_DB_USER=db
CRAFT_DB_PASSWORD=db
CRAFT_DB_SCHEMA=
CRAFT_DB_TABLE_PREFIX=

# SMTP
SMTP_HOST=127.0.0.1
SMTP_PORT=1025
SMTP_USERNAME=
SMTP_PASSWORD=

# Plugins / Other
VITE_USE_DEV_SERVER=1

COOKIEBOT_ID=7afaea56-7b2d-4aa1-94a8-e2a55ba8e0fa
KIYOH_URI=https://www.kiyoh.com
KIYOH_HASH="op://Operations/MSN Kiyoh hash/KIYOH HASH"

WOOCOMMERCE_REST_API_URL="op://Operations/zzacsm34r44rdeabs7leevme3i/rest_api_url"
WOOCOMMERCE_REST_API_CONSUMER_KEY="op://Operations/zzacsm34r44rdeabs7leevme3i/consumer_key"
WOOCOMMERCE_REST_API_CONSUMER_SECRET="op://Operations/zzacsm34r44rdeabs7leevme3i/consumer_secret"

FORMIE_SECURITY_KEY=
RECAPTCHA_SECRET_KEY=

CUSTOMER_IO_API_KEY=
CUSTOMER_IO_API_VERSION=
CUSTOMER_IO_SITE_ID=