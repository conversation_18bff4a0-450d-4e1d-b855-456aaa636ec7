{% set breadcrumbColor = 'text-tertiary-900' %}
{% set blocks = entry.contentBlocks.all %}
{% set isGenericPage = entry.type == 'genericPage' %}
{% set articleCTA = entry.articleCTA.one ?? null %}
{% set articleAuthor = articleCTA ? articleCTA.ctaExpert.one ?? null : null %}

{% if not articleAuthor %}
    {% set articleAuthor = entry.articleAuthor.one ?? null %}
{% endif %}

{% if articleAuthor %}
    {% set mainEntity = seomatic.jsonLd.get('mainEntityOfPage') %}
    {% do mainEntity.setAttributes({
        'author': {
            '@type': 'Person',
            'name': articleAuthor.title,
            'url': articleAuthor.url,
            'image': articleAuthor.authorImage.one.url,
        }
    }) %}
{% endif %}

{% extends '_layout/base.twig' %}

{% block content %}
    {% if isGenericPage %}
        {% include '_layout/content.twig' %}
    {% else %}
        {% include '_parts/article/header.twig' %}
        {% include '_parts/content.twig' with {
            blocks: blocks | filter(block => block.type != 'articleDoorway'),
        } %}
        {% include '_parts/article/cta.twig' %}
        {% include '_parts/article/footer.twig' %}
        {% include '_parts/content.twig' with {
            blocks: blocks | filter(block => block.type == 'articleDoorway'),
        } %}
    {% endif %}
{% endblock %}
