{% extends "_layout/base.twig" %}

{% set errorContent = {
    keyVisualType: 'media',
    keyVisualAlignment: 'simple',
    keyVisualTitle: error500.keyVisualTitle,
    keyVisualHighlightedWords: error500.keyVisualHighlightedWords,
    keyVisualImage: error500.keyVisualImage,
    keyVisualHideScrollText: true,
    keyVisualCloseContentGap: false,
    keyVisualIntro: null,
    keyVisualVideo: null,
    keyVisualScrollText: null
} %}
{# twigcs use-var entry #}

{% block content %}
    {% include '_parts/keyvisual' with {
        entry: errorContent
    } %}
{% endblock %}
