{% extends "_layout/base.twig" %}

{% block content %}

    {% set fields = entry.behaviors.customFields %}

    <section class="p-8 keyvisual">

        <div class="keyvisual-background"></div>

        {% set words = fields.headingHighlightedWords | split(' ') %}
        {% set headingText = fields.heading | split(' ') %}
        {% set heading = [] %}
        {% for word in headingText %}
            {% if word in words %}
                {% set heading = heading | push('<span class="text-primary-500 font-bold">' ~ word ~ '</span>') %}
            {% else %}
                {% set heading = heading | push(word) %}
            {% endif %}
        {% endfor %}
        {% set heading = heading | join(' ') %}

        <{{ fields.headingType }}>{{ heading | raw }}</{{ fields.headingType }}>

        <div class="wysiwyg py-8">
            {{ fields.richText | raw }}
        </div>

        <section class="flex flex-row flex-wrap gap-8 pb-8">

            <button class="btn btn-{{ fields.buttonType }} has-icon-left btn-animation"{{ fields.buttonState == 'disabled' ? ' disabled' : '' }}>
                <span class="icon icon-arrow-left"></span>
                <span>{{ fields.buttonTitle }}</span>
            </button>

            <button class="btn btn-{{ fields.buttonType }} has-icon-right btn-animation"{{ fields.buttonState == 'disabled' ? ' disabled' : '' }}>
                <span class="icon icon-arrow-right"></span>
                <span>{{ fields.buttonTitle }}</span>
            </button>

            <button class="btn btn-{{ fields.buttonType }} btn-animation"{{ fields.buttonState == 'disabled' ? ' disabled' : '' }}>
                <span>{{ fields.buttonTitle }} ~ Button</span>
            </button>

            <a href="#" class="btn btn-{{ fields.buttonType }} btn-animation"{{ fields.buttonState == 'disabled' ? ' disabled' : '' }}>
                <span>{{ fields.buttonTitle }} ~ Link</span>
            </a>

            <input type="button" value="{{ fields.buttonTitle }} ~ Input" class="btn btn-{{ fields.buttonType }}"{{ fields.buttonState == 'disabled' ? ' disabled' : '' }}>

        </section>

        <section class="flex flex-row gap-8 pb-8">

            <button class="btn-text btn-text-{{ fields.buttonType }}"{{ fields.buttonState == 'disabled' ? ' disabled' : '' }}>
                {{ fields.buttonTitle }} ~ Text Button
            </button>

        </section>

        <section class="flex flex-row gap-8 pb-8">

            <span class="tag tag--{{ fields.buttonType }}"{{ fields.buttonState == 'disabled' ? ' disabled' : '' }}>
                {{ fields.buttonTitle }} ~ Tag
            </span>

        </section>

        <footer>

            <div class="ellipse"></div>
            Footer

        </section>

    </section>

{% endblock %}
