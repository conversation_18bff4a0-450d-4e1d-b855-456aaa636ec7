{% if entry is defined and (isErrorPage is not defined or not isErrorPage) %}
    {% do eagerLoadElements(entry, [
        'keyVisualImage',
        'image',
        'contentBlocks.articleDoorway:doorwayItemsWithNoFollow.doorwayItem',
        'contentBlocks.articleDoorway:doorwayItemsWithNoFollow.button',
        'contentBlocks.articleDoorway:button',
        'contentBlocks.textImageElement:image',
        'contentBlocks.media:mediaImage',
        'contentBlocks.reviews:button',
        'contentBlocks.ctaUspBig:button',
        'contentBlocks.ctaUspBig:usp',
        'contentBlocks.ctaUspBig:uspItemThumbnail',
        'contentBlocks.form:form',
        'contentBlocks.brandSlider:brands',
        'contentBlocks.segments:segmentImage'
    ]) %}
{% endif %}

{% do eagerLoadElements(footer, [
    'footerLogos.image',
    'footerLogos.imageLink'
]) %}

{% include '_layout/layout-head.twig' %}
  {# main nav #}
  {% include '_parts/top-navigation.twig' %}
  {% include '_parts/navigation.twig' %}
  {# breadcrumbs #}
  {% block breadcrumbs %}
    {% include '_parts/breadcrumbs.twig' with {
        breadcrumbColor: breadcrumbColor ?? false
    } %}
  {% endblock %}

  <main>
    {# The content #}
    {% block content %}
      Sorry, no content
    {% endblock %}
  </main>

  {# Always show footer unless option is false #}
  {% if not entry is defined or (entry.footerActive or entry.footerActive is null) %}

    {# footer #}
    {% block footer %}
        {% include '_layout/footer.twig' %}
    {% endblock %}

  {% endif %}

{% include '_layout/layout-end.twig' %}
