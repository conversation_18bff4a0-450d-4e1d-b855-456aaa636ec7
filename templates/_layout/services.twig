{% include '_layout/layout-head.twig' %}
{% set servicesBlocks = entry.services.all() %}

<div class="services">
    <div class="services__column services__column--left container--padding">
        {% include '_parts/quotation/navigation.twig' with {
            class: 'services__nav'
        } %}
        <h1 class="services__title">{{ entry.title }}</h1>
        {% if entry.servicesText is not empty %}
            <div class="services__text wysiwyg">
                {{ entry.servicesText }}
            </div>
        {% endif %}
        {% if not entry.servicesButton.isEmpty %}
            {% include '_parts/button.twig' with {
                link: entry.servicesButton,
                type: 'primary',
                class: 'services__button',
            } %}
        {% endif %}
    </div>
    <div class="services__column services__column--right services__column--green container--padding">
    {% for block in servicesBlocks %}
        {% include '_parts/services/' ~ (block.type | kebab) ~ '.twig' with {
            block: block
        } %}
        {# twigcs use-var block #}
    {% endfor %}
    </div>
</div>

{% include '_layout/layout-end.twig' %}
