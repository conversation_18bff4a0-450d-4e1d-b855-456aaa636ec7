<!DOCTYPE html>
<html class="no-js"
      lang="{{ currentSite.language }}"
      dir="{{ craft.app.i18n.getLocaleById(currentSite.id).getOrientation() }}">
<head>
    <meta charset="{{ _charset }}"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta name="application-name" content="{{ siteName }}"/>

    <link rel="apple-touch-icon" sizes="180x180" href="{{ siteUrl }}dist/images/favicons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ siteUrl }}dist/images/favicons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ siteUrl }}dist/images/favicons/favicon-16x16.png">
    <link rel="manifest" href="{{ siteUrl }}dist/images/favicons/site.webmanifest">
    <link rel="mask-icon" href="{{ siteUrl }}dist/images/favicons/safari-pinned-tab.svg" color="#009d3d">
    <meta name="msapplication-TileColor" content="#e5f5ec">
    <meta name="theme-color" content="#e5f5ec">
    <meta property="og:image" content="{{ siteUrl }}dist/images/logos/logo.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">


    {% if statusCode is defined and statusCode == 404 and devMode == false %}
        {% set siteName = "Pagina niet gevonden" %}
    {% endif %}

    {{ head() }}

    {{ craft.vite.script('src/js/app.ts', false) }}
</head>
<body class="flex flex-col min-h-screen no-js text-20">

{{ beginBody() }}
