{% set containerClasses = block.containContent is defined and block.containContent != 'wide' ? block.containContent == 'small' ? 'container container--padding container--small' : 'container container--padding' : '' %}

<div class="
    custom-code c-block
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
    {{ containerClasses }}
    {{ block.centerContent ? 'flex flex-col items-center text-center' }}
">
    {% if block.heading %}
        {% include '_parts/block-title' with {
            title: block.heading,
            titleClass: '
                mb-6
                lg:mb-8
            ',
        } %}
    {% endif %}
    {{ block.code | raw }}
</div>
