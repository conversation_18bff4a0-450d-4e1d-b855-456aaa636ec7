{% set minRating = block.minRating | default(7) %}
{% set maxItems = block.maxItems | default(10) %}
{% set data = get_current_reviews(minRating, maxItems) %}
{% set kiyohScore = get_kiyoh_score() %}

<div class="
    wrapper c-block
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="container px-1 container--small">
        <div class="flex items-center justify-between gap-8 px-8 py-6 bg-primary-100 sm-max:flex-col lg:gap-24">
            <div class="flex flex-col gap-2">
                {# title #}
                {% if block.reviewTitle is defined %}
                    {% include '_parts/block-title' with {
                        title: kiyohScore is not null ? block.reviewTitle | replace({'{rating}': kiyohScore}) : block.reviewTitle,
                        headingType: 'h3',
                        fakeHeading: true,
                    } %}
                {% endif %}

                {# text #}
                {% if block.reviewSubtitle is defined %}
                    <div class="wysiwyg text-16">
                        {{ block.reviewSubtitle | replace({'{total}': data.numberReviews}) }}
                    </div>
                {% endif %}
            </div>

            <div class="relative shrink-0 score-circle">
                {{ svg('./dist/images/icons-full-color/kiyoh-circle.svg') | attr({class: 'icon size-[7.5rem]'}) }}
                {% if kiyohScore is not null %}
                    <div class="absolute text-center transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2">
                        <span class="text-[3ch] leading-none text-tertiary-900 font-medium">{{ kiyohScore | number_format(1, '.', '.') }}</span>
                    </div>
                {% endif %}
            </div>

            <div class="shrink-0">
                {# button #}
                {% if not block.buttonLink.isEmpty %}
                    {% include '_parts/button.twig' with {
                        link: block.buttonLink,
                        type: block.buttonType,
                        linkNoFollow: block.linkNoFollow,
                        class: 'text-element__button !px-4 !py-3',
                    } %}
                {% endif %}
            </div>
        </div>
    </div>
</div>
