{% set button = block.button.one() %}
{% set usps = block.usp.all() %}

<div class="
    cta-big container c-block
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="cta-big__thumbnails">
        <div class="cta-big__thumbnails-sticky-wrapper">
            {% for usp in usps %}
                {% include '_parts/picture.twig' with {
                    image: usp.uspItemThumbnail.one(),
                    class: "cta-big__thumbnail" ~ (loop.first ? ' cta-big__thumbnail--current' : ''),
                    attributes: 'data-image-id=' ~ loop.index,
                    loading: 'lazy',
                    width: 830,
                    height: 580,
                } %}
            {% endfor %}
        </div>
    </div>
    <div class="cta-big__content container--padding">
        {% if block.uspTitle %}
            {% include '_parts/block-title' with {
                title: block.uspTitle,
                titleClass: 'cta-big__title',
            } %}
        {% endif %}
        <ul class="cta-big__usps">
            {% for usp in usps %}
                <li class="cta-big__usp {% if loop.first %}cta-big__usp--current{% endif %}" data-image-id="{{ loop.index }}">
                    {% include '_parts/icon.twig' with {
                        icon: 'check',
                        class: 'cta-big__usp-icon'
                    } %}
                    {{ usp.uspItemTitle }}
                </li>
            {% endfor %}
        </ul>
        {# button #}
        {% if button is not null %}
            {% include '_parts/button.twig' with {
                link: button.ctaLink,
                type: button.ctaType,
                linkNoFollow: button.linkNoFollow,
                class: 'cta-big__button',
            } %}
        {% endif %}

    </div>

</div>
