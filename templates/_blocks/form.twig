{% set form = block.form.one() %}
{% set image = block.blockImage.one() %}
{% set imageLandscape = (block.imageAspectRatio.value ?? '45') == '32' %}
{% set aspectRatio = imageLandscape ? 'md-max:aspect-[3/2]' : 'md-max:aspect-[4/5]' %}
{% set submitted = craft.formie.plugin.service.getFlash(form.id, 'submitted') %}
{% set submissionId = craft.app.request.getParam('submission') %}

{% if submitted %}
    {# Fetch the submission #}
    {% set submission = submissionId ? craft.formie.submissions.uid(submissionId).one() : null %}

    {% if submission %}
        <div>
            {# Get the value for the field, from the submission #}
            {% set email = submission.getFieldValue('email') %}
            {% set phonenumber = submission.getFieldValue('phonenumber') %}

            <script defer>
                window.onload = () => {
                    if (typeof pushToFormDatalayer === 'function') {
                        pushToFormDatalayer('{{ submission.id }}', '{{ email }}' ?? '');
                    }
                }
            </script>
        </div>
    {% endif %}
{% endif %}

<div class="c-block container container--padding
    {{ image ? 'grid lg:grid-cols-2 gap-6 md:gap-14' : 'container--small' }}
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}">
    {% if image %}
        {% include '_parts/picture.twig' with {
            class: "
                block rounded object-cover size-full md-max:order-2
                #{aspectRatio}
            ",
            image: image,
            loading: 'eager',
            sizes: ['0.25x', '0.5x', '1x', '1.5x'],
            alt: ''
        } %}
    {% endif %}
    {% if not submissionId %}
        <div class="form__wrapper">
            <div>
                {% if block.heading %}
                    {% include '_parts/block-title' with {
                        title: block.heading,
                        titleClass: "!mb-2 #{block.headingColor}",
                    } %}
                {% endif %}
                {% if block.subtitle %}
                    <div class="text-16">{{ block.subtitle }}</div>
                {% endif %}
            </div>
            {% set config = form.configJson | json_decode %}
            {% if config.settings.submitMethod != 'ajax' %}
                {% do form.setSettings({
                    redirectUrl: craft.app.request.url ~ '?submission={uid}'
                }) %}
            {% endif %}

            {{ craft.formie.renderForm(form) }}

            <script defer>
                const $form = document.querySelector('#{{ form.getFormId() }}');
                if ($form) {
                    const $uuidInput = $form.querySelector('[name="fields[uuidInput]"]')
                    const uuid = localStorage.getItem('formUuid')

                    if (uuid) {
                        $uuidInput.value = uuid
                    } else {
                        window.addEventListener('formUuid', (e) => {
                            if ($uuidInput && e.detail) {
                                $uuidInput.value = e.detail
                            }
                        })
                    }

                    $form.addEventListener('onAfterFormieSubmit', (e) => {
                        e.preventDefault();

                        if (e.detail.nextPageId) {
                            return;
                        }

                        let $subjects = $form.querySelectorAll('[name="fields[waaroverWilJeContactOpnemen]"], [name="fields[subject]"]')
                        let $subject = null;
                        $subjects.forEach(subject => {
                            if (subject.checked) {
                                $subject = subject;
                            }
                        })
                        const $companyName = $form.querySelector('[name="fields[naam1]"], [name="fields[companyName]"]')
                        const $city = $form.querySelector('[name="fields[plaats1]"], [name="fields[city]"]')
                        const $email = $form.querySelector('[name="fields[eMailadres]"], [name="fields[email]"], [name="fields[email1]"], [name="fields[eMailadres1]"]')
                        const $phonenumber = $form.querySelector('[name="fields[telefoonnummer]"], [name="fields[telefoonnummer1]"], [name="fields[plaats]"]')
                        const submissionId = e.detail.submissionId

                        pushToFormDatalayer(
                            submissionId,
                            $email ? $email.value : '',
                            $phonenumber ? $phonenumber.value : '',
                            $subject ? $subject.value : '',
                            $companyName ? $companyName.value : '',
                            $city ? $city.value : '',
                        );
                    });
                }
            </script>
        </div>
    {% else %}
        {% set config = form.configJson | json_decode %}
        <div class="form__wrapper">
            <div>
                {% if block.heading %}
                    {% include '_parts/block-title' with {
                        title: block.heading,
                        titleClass: "!mb-2 #{block.headingColor}",
                    } %}
                {% endif %}
                {% if block.subtitle %}
                    <div class="text-16">{{ block.subtitle }}</div>
                {% endif %}
            </div>
            <div class="fui-i">
                <div class="fui-alert fui-alert-success fui-alert-top-form"
                    role="alert">{{ config.settings.submitActionMessage }}</div>
            </div>
            <a href="/{{ craft.app.request.getPathInfo() }}" class="doorways__button btn btn-primary btn-animation">
                <span>{{ 'Fill in the form again' | t }}</span>
            </a>
        </div>
    {% endif %}
</div>

<script defer>
    const pushToFormDatalayer = (submissionId, email, phonenumber, subject = '', companyName = '', city = '') => {
        window.dataLayer = window.dataLayer || [];

        const formUuid = localStorage.getItem('formUuid') || ''
        dataLayer.push({
            event: 'formSubmission',
            formType: '{{ form.title }}',
            formId: '{{ form.id }}',
            submissionId: submissionId,
            subject: subject,
            companyName: companyName,
            city: city,
            phonenumber: phonenumber,
            email: email,
            formUuid: formUuid
        });

        setTimeout(() => {
            localStorage.removeItem('formUuid');
        }, 250);
    }
</script>
