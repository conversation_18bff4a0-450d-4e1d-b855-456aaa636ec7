{% set segments = block.segments.all() %}

<div class="
    wrapper c-block
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="
        flex gap-x-8 gap-y-6 container container--padding
        md-max:flex-col
    ">
        {% for segment in segments %}
            {% set isStaticUrl = false %}
            {% set linkedPage = segment.segmentLink %}
            {% set target = linkedPage.getTarget() %}

            {% if linkedPage.getElement() %}
                {% set linkedPage = linkedPage.getElement() %}
            {% else %}
                {% set isStaticUrl = true %}
            {% endif %}

            {% set title = segment.segmentTitle ?? (isStaticUrl ? null : linkedPage.keyVisualTitle ?? linkedPage.title) %}
            {% set description = segment.segmentText ?? (isStaticUrl ? null : linkedPage.description ?? '') %}
            {% set image = segment.segmentImage and segment.segmentImage.one() ? segment.segmentImage.one() : (isStaticUrl ? null : linkedPage.keyVisualImage and linkedPage.keyVisualImage.one() ? linkedPage.keyVisualImage.one() : linkedPage.thumbnail and linkedPage.thumbnail.one() ? linkedPage.thumbnail.one() : null) %}

            <a
                href="{{ linkedPage.url }}"
                target="{{ target }}"
                class="grow basis-full group relative flex-col rounded overflow-hidden"
                {% if segment.noFollow %} rel="nofollow" {% endif %}
            >
                {% include '_parts/picture.twig' with {
                    image: image,
                    class: '
                        w-full rounded-t aspect-[327/231] object-cover
                        lg:aspect-[421/386] lg:rounded
                    ',
                    attributes: isStaticUrl ? '' : "style=\"object-position: #{image.focalPoint.x * 100}% #{image.focalPoint.y * 100}%;\"",
                    loading: 'lazy',
                } %}
                <div class="
                    flex flex-col p-3 pr-16 bg-primary-100 w-full
                    lg:p-6 lg:pr-20 lg:pt-9 lg:bg-tertiary-300 lg:absolute lg:bottom-0 lg:translate-y-[6rem] lg:transition-transform
                    lg:group-hover:bg-primary-100 lg:group-hover:translate-y-0
                    lg:group-focus-visible:bg-primary-100 lg:group-focus-visible:translate-y-0
                ">
                    <span class="
                        text-18 leading-24 font-medium
                        lg:text-22 lg:leading-28
                    ">{{ title }}</span>
                    <div class="
                        mt-4 wysiwyg wysiwyg--no-outer-margins line-clamp-4 text-16 leading-24
                        lg:mt-6 lg:min-h-[4.5rem] lg:line-clamp-3 lg:text-18
                        [&>p]:text-16 [&>p]:leading-24
                        lg:[&>p]:text-18
                    ">{{ description }}</div>
                </div>
                <div class="
                    rounded border transition-colors flex items-center justify-center absolute right-3 bottom-3 w-10 h-10 bg-primary-500 text-white border-primary-500
                    lg:right-6 lg:bottom-6
                    lg:[.group:not(:hover):not(:focus-visible)_&]:text-tertiary-900 lg:[.group:not(:hover):not(:focus-visible)_&]:bg-tertiary-300 lg:[.group:not(:hover):not(:focus-visible)_&]:border-tertiary-900
                ">
                    {% include '_parts/icon.twig' with {
                        icon: 'arrow-right',
                        class: 'text-24'
                    } %}
                </div>
            </a>
        {% endfor %}
    </div>
</div>
