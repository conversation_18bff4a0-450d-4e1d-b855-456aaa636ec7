{% set brands = block.brands.all() %}

<div class="
    brand-slider c-block container swiper
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    {% if block.brandsTitle is not empty %}
        {% include '_parts/block-title' with {
            title: block.brandsTitle,
            titleClass: 'brand-slider__title container--padding',
        } %}
    {% endif %}
    <div class="brand-slider__inner swiper-wrapper">
        {% for brand in brands %}
            <div class="brand-slider__slide swiper-slide">
                {% include '_parts/picture.twig' with {
                    image: brand,
                    loading: 'lazy',
                    height: 64,
                } %}
            </div>
        {% endfor %}
    </div>
</div>
