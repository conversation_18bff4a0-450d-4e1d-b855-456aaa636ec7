{% set downloadWallFormie = block.downloadWallFormie %}
{% set image = downloadWallFormie.wallImage.one() %}
{% set file = downloadWallFormie.wallDownload.one() ?? null %}
{% set form = downloadWallFormie.wallForm.one() %}
{% set submitted = craft.formie.plugin.service.getFlash(form.id, 'submitted') %}
{% set submissionId = craft.app.request.getParam('submission') %}

<div class="
    wrapper c-block container px-6 container--medium
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="
        flex flex-col rounded bg-white w-full shadow-[0_2px_9px_0_#0000000D]
        md:flex-row-reverse
    ">
        <div class="
            grow p-6 pb-4
            lg:p-8 lg:pb-6
        ">
            <h2 class="h3">{{ downloadWallFormie.wallTitle }}</h2>
            {% if downloadWallFormie.wallSubtitle %}
                <p class="
                    text-16 leading-24 mt-2 block
                    lg:text-18 lg:leading-28
                ">{{ downloadWallFormie.wallSubtitle }}</p>
            {% endif %}

            <div class="form__wrapper download_wall__formie">
                {% if submitted %}

                    {# Fetch the submission #}
                    {% set submission = submissionId ? craft.formie.submissions.uid(submissionId).one() : null %}

                    {% if submission %}
                        {# Get the value for the field, from the submission #}
                        {% set email = submission.getFieldValue('email') %}
                        {% set phonenumber = submission.getFieldValue('phonenumber') %}

                        <script defer>
                            window.onload = () => {
                                if (typeof pushToDownloadDatalayer === 'function') {
                                    pushToDownloadDatalayer('{{ submission.id }}', '{{ email }}' ?? '');
                                }
                            }
                        </script>
                    {% endif %}
                {% endif %}

                {% if not submissionId %}
                    {% set config = form.configJson | json_decode %}
                    {% if config.settings.submitMethod != 'ajax' %}
                        {% do form.setSettings({
                            redirectUrl: craft.app.request.url ~ '?submission={uid}'
                        }) %}
                    {% endif %}
                    {% do form.setSettings({
                        submitActionMessage: config.settings.submitActionMessage ~ "<p>" ~ "Your download will start automatically. Does nothing happen? Please click " | t ~ "<a class=\"no-underline color-primary font-bold\" href=\"" ~ file.url ~ "\" download=\"" ~ file.title ~ "." ~ file.extension ~ "\">" ~ "here" | t ~ "</a>.</p>"
                    }) %}

                    {{ craft.formie.renderForm(form) }}

                    <script defer>
                        const $form = document.querySelector('#{{ form.getFormId() }}');

                        if ($form) {
                            const $uuidInput = $form.querySelector('[name="fields[uuidInput]"]')
                            const uuid = localStorage.getItem('formUuid')

                            if (uuid) {
                                $uuidInput.value = uuid
                            } else {
                                window.addEventListener('formUuid', (e) => {
                                    if ($uuidInput && e.detail) {
                                        $uuidInput.value = e.detail
                                    }
                                })
                            }

                            $form.addEventListener('onAfterFormieSubmit', (e) => {
                                e.preventDefault();

                                if (e.detail.nextPageId) {
                                    return;
                                }
                                const $firstName = $form.querySelector('[name="fields[voornaam]"], [name="fields[firstName]"]')
                                const $lastName = $form.querySelector('[name="fields[achternaam]"], [name="fields[lastName]"]')
                                let $subjects = $form.querySelectorAll('[name="fields[waaroverWilJeContactOpnemen]"], [name="fields[subject]"]')
                                let $subject = null;
                                $subjects.forEach(subject => {
                                    if (subject.checked) {
                                        $subject = subject;
                                    }
                                })
                                const $companyName = $form.querySelector('[name="fields[naam1]"], [name="fields[companyName]"]')
                                const $city = $form.querySelector('[name="fields[plaats1]"], [name="fields[city]"]')
                                const $email = $form.querySelector('[name="fields[eMailadres]"], [name="fields[emailadres]"], [name="fields[email]"], [name="fields[email1]"], [name="fields[eMailadres1]"]')
                                const $phonenumber = $form.querySelector('[name="fields[telefoonnummer]"], [name="fields[telefoonnummer1]"], [name="fields[plaats]"]')
                                const submissionId = e.detail.submissionId
                                {% if file %}
                                    const $downloadFile = '{{ file.url }}'
                                {% endif %}
                                const $subscribedCheckbox = $form.querySelector('[name="fields[subscribe]"]:checked');
                                const $subscribed = $subscribedCheckbox ? $subscribedCheckbox.checked : false;

                                pushToDownloadDatalayer(
                                    submissionId,
                                    $firstName ? $firstName.value : '',
                                    $lastName ? $lastName.value : '',
                                    $email ? $email.value : '',
                                    $phonenumber ? $phonenumber.value : '',
                                    $subject ? $subject.value : '',
                                    $companyName ? $companyName.value : '',
                                    $city ? $city.value : '',
                                    $downloadFile ? $downloadFile : '',
                                    $subscribed ? $subscribed : false,
                                );
                            });
                        }
                    </script>
                {% else %}
                    {% set config = form.configJson | json_decode %}
                    <div class="fui-i">
                        <div class="fui-alert fui-alert-success fui-alert-top-form"
                            role="alert">{{ config.settings.submitActionMessage }} <p>{{ "Your download will start automatically. Does nothing happen? Please click " | t }}<a class="no-underline color-primary font-bold" href="{{ file.url }}" download="{{ file.title }}.{{ file.extension }}">{{ "here" | t }}</a>.</p></div>
                    </div>
                    <a href="/{{ craft.app.request.getPathInfo() }}" class="doorways__button btn btn-primary btn-animation">
                        <span>{{ 'Fill in the form again' | t }}</span>
                    </a>
                {% endif %}
            </div>
        </div>
        {% if image %}
            {% include '_parts/picture.twig' with {
                image: image,
                width: 500,
                height: 500,
                attributes: "style=\"object-position: #{image.focalPoint.x * 100}% #{image.focalPoint.y * 100}%;\"",
                class: '
                    w-full shrink-0
                    sm-max:rounded-b
                    md:w-1/3 md:object-cover md:rounded-l
                ',
                loading: 'lazy',
            } %}
        {% endif %}
    </div>
</div>

<script>

    const downloadFile = (url, filename) => {
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    };

    const pushToDownloadDatalayer = (submissionId, firstName, lastName, email, phonenumber, subject = '', companyName = '', city = '', downloadFile, subscribed) => {
        window.dataLayer = window.dataLayer || [];

        const formUuid = localStorage.getItem('formUuid') || ''
        dataLayer.push({
            event: 'formSubmission',
            formType: '{{ form.title }}',
            formId: '{{ form.id }}',
            submissionId: submissionId,
            firstName: firstName,
            lastName: lastName,
            subject: subject,
            companyName: companyName,
            city: city,
            phonenumber: phonenumber,
            email: email,
            downloadFile: downloadFile,
            subscribed: subscribed,
            formUuid: formUuid
        });

        setTimeout(() => {
            localStorage.removeItem('formUuid');
        }, 250);
    }

    {% if file %}
        document.addEventListener('DOMContentLoaded', () => {
            const $form = document.querySelector('#{{ form.getFormId() }}');

            // For AJAX submissions
            if ($form) {
                $form.addEventListener('onAfterFormieSubmit', (e) => {
                    if (e.detail.nextPageId) return;

                    setTimeout(() => {
                        downloadFile('{{ file.url }}', '{{ file.title }}.{{ file.extension }}');
                    }, 200);
                });
            }

            // For page reload submissions
            {% if submitted %}
                setTimeout(() => {
                    const formieForm = document.querySelector('.download_wall__formie')
                    const successMessage = formieForm.querySelector('.fui-alert-success')

                    if (successMessage) {
                        formieForm.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                        })
                    }

                    downloadFile('{{ file.url }}', '{{ file.title }}.{{ file.extension }}');
                }, 200);
            {% endif %}
        });
    {% endif %}
</script>
