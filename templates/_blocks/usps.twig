<div class="
    wrapper c-block bg-primary-50
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="container container--padding py-6 lg:py-10 xl:py-14">
        {% if block.uspItem | length %}
            <ul class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-16">
                {% for usp in block.uspItem %}
                    <li>
                        <div class="text-primary-500 text-28 leading-none mb-5"><span
                                    class="icon icon-{{ usp.uspItemIcon.label }}"></span></div>
                        <div class="h4">{{ usp.uspItemTitle }}</div>
                        {% if usp.uspItemText %}
                            <div class="text-base mt-2">{{ usp.uspItemText }}</div>
                        {% endif %}
                    </li>
                {% endfor %}
            </ul>
        {% endif %}
    </div>
</div>
