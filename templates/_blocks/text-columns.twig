{% set textColumns = block.textColumns %}
{% set showTitles = textColumns | filter(column => column.columnTitle ?? false) %}

<div class="
    wrapper c-block
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="
        container container--padding container--{{ block.columnSize.value }}
        flex flex-col gap-y-6
        lg:flex-row lg:gap-x-8 lg:flex-wrap
    ">
        {% for column in textColumns %}
            {% if column.columnTitle %}
                {{ tag(column.columnTitleSize, {
                    class: "
                        text-primary-500
                        lg:order-1 lg:w-[calc(50%-1rem)] lg:self-end
                        #{not loop.first ? 'md-max:mt-4'}
                    ",
                    text: column.columnTitle
                }) }}
            {% elseif showTitles %}
                <span class="
                    hidden
                    lg:block lg:order-1 lg:w-[calc(50%-1rem)]
                "></span>
            {% endif %}
            <div class="
                wysiwyg wysiwyg--no-outer-margins
                lg:order-2 lg:w-[calc(50%-1rem)] lg:self-start
            ">
                {{ column.columnText }}
            </div>
        {% endfor %}
    </div>
</div>
