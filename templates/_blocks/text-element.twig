{# heading tag #}
<div class="
    wrapper c-block
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="text-element container container--padding container--small">
        {# title #}
        {% if block.heading is defined %}
            {% include '_parts/block-title' with {
                title: block.heading,
                titleClass: "
                    text-primary-500
                    #{block.headingColor}
                ",
            } %}
        {% endif %}

        {# text #}
        {% if block.text is defined %}
            <div class="wysiwyg">
                {{ block.text }}
            </div>
        {% endif %}

        {# button #}
        {% if not block.buttonLink.isEmpty %}
            {% include '_parts/button.twig' with {
                link: block.buttonLink,
                type: block.buttonType,
                linkNoFollow: block.linkNoFollow,
                class: 'text-element__button',
            } %}
        {% endif %}
    </div>
</div>
