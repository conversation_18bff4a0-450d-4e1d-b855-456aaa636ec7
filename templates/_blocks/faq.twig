{% set isFollowedByFAQ = block.getNext() ? block.getNext().type == 'faq' : false %}
{% set faqs = block.faq.all() %}
{% set stucturedDataFaqs = faqs | filter(faq => faq.isShownInStructuredData) %}

<div class="
    faq c-block container container--padding container--small
    {{ isFollowedByFAQ ? 'faq--next-faq' }}
    {{ not block.faqTitle ? 'faq--no-title' }}
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    {% if block.faqTitle %}
        {% include '_parts/block-title' with {
            title: block.faqTitle,
            titleClass: 'faq__title',
        } %}
    {% endif %}
    {% if block.faqSubtitle %}
        <h3 class="faq__subtitle">{{ block.faqSubtitle }}</h3>
    {% endif %}
    {% for faq in faqs %}
        <details class="faq__item"
                    data-shown-in-structured-data="{{ faq.isShownInStructuredData ? 'true' : 'false' }}">
            <summary class="faq__question details__header">{{ faq.question }}</summary>
            <div class="faq__answer details__content wysiwyg wysiwyg--no-outer-margins">{{ faq.answer }}</div>
        </details>
    {% endfor %}
</div>
