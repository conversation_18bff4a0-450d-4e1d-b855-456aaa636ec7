{% set minRating = block.minRating | default(7) %}
{% set maxItems = block.maxItems | default(10) %}
{% set data = get_current_reviews(minRating, maxItems) %}
{% set kiyohRating = get_kiyoh_score() %}
{% set button = block.button.one() %}
{% set isSlider = block.reviewBlockType.value | default('slider') == 'slider' %}

{% if data.reviews is defined %}
    <div class="
        {{ isSlider ? 'reviews-slider swiper' : 'reviews-list' }}
        c-block
        {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
    ">
        <div class="container reviews-slider__header container--padding">
            <div class="reviews-slider__header-text">
                {% if block.reviewTitle is defined %}
                    {% include '_parts/block-title' with {
                        title: block.reviewTitle,
                        titleClass: "
                            review-slider__title
                            #{block.reviewSubtitle is not defined ? 'review-slider__title--no-subtitle'}
                        ",
                    } %}
                {% endif %}
                {% if block.reviewSubtitle is defined %}
                    <p class="review-slider__subtitle">{{ block.reviewSubtitle | replace({'{rating}': kiyohRating}) }}</p>
                {% endif %}
            </div>
            {% if button is not null %}
                {% include '_parts/button.twig' with {
                    link: button.ctaLink,
                    type: button.ctaType,
                    linkNoFollow: button.linkNoFollow,
                    label: button.ctaLink.customText | replace({'{total}': data.numberReviews}),
                    class: 'reviews-slider__header-button',
                } %}
            {% endif %}
        </div>
        <div class="{{ isSlider ? 'reviews-slider__slides swiper-wrapper' : 'container container--padding grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8' }}">
            {% for review in data.reviews %}
                <div class="reviews-slider__slide {{ isSlider ? 'swiper-slide' }}">
                    <h3 class="reviews-slider__slide-title">
                        {{ review.oneliner }}
                    </h3>
                    <div class="reviews-slider__slide-stars">
                        {% set filledStars = ((review.rating / 2) | round(0, 'ceil')) %}
                        {% for i in 1..filledStars %}
                            {% include '_parts/email/email-image' with {
                                path: 'dist/images/reviews/filled-star.svg',
                                alt: 'Filled star',
                                width: 24,
                                height: 24,
                            } %}
                        {% endfor %}
                    </div>
                    <p class="reviews-slider__slide-review">
                        {{ review.comment }}
                    </p>
                    <p class="reviews-slider__slide-author end">
                        {{ review.reviewAuthor }} uit {{ review.city }}
                    </p>
                </div>
            {% endfor %}
        </div>

        {% if isSlider %}
            <div class="container reviews-slider__controls container--padding">
                <div class="reviews-slider__scrollbar swiper-scrollbar">
                    {# Swiper generates a child with the `article-slider__scrollbar-thumb` class #}
                </div>
                <div class="reviews-slider__buttons">
                    <button class="reviews-slider__button reviews-slider__button--prev swiper-button-prev" aria-label="Schuif naar de vorige beoordeling">
                        {% include '_parts/icon.twig' with {
                            icon: 'arrow-left'
                        } %}
                    </button>
                    <button class="reviews-slider__button reviews-slider__button--next swiper-button-next" aria-label="Schuif naar de volgende beoordeling">
                        {% include '_parts/icon.twig' with {
                            icon: 'arrow-right'
                        } %}
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
{% endif %}
