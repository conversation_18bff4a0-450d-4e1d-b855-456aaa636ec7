{% set step1 = block.visualisationStep1 %}
{% set step2 = block.visualisationStep2 %}
{% set step3 = block.visualisationStep3 %}
{% set step4 = block.visualisationStep4 %}
{% set step5 = block.visualisationStep5 %}
{% set step6 = block.visualisationStep6 %}

{% set steps = [step1, step2, step3, step4, step5, step6] %}
{% set stepPositions = [
    '
        left-1/2 top-0 -translate-x-1/2 -translate-y-1/2
        [&[aria-selected="true"]]:translate-x-[calc(-50%+2rem)] [&[aria-selected="true"]]:translate-y-[calc(-50%-2rem)]
        md:[&[aria-selected="true"]]:translate-x-[calc(-50%+3rem)] md:[&[aria-selected="true"]]:translate-y-[calc(-50%-3rem)]
        xl:[&[aria-selected="true"]]:translate-x-[calc(-50%+3.75rem)] xl:[&[aria-selected="true"]]:translate-y-[calc(-50%-3.75rem)]
    ',
    '
        right-[11.5%] top-[11.5%] translate-x-[20%] -translate-y-[20%]
        md:translate-x-[15%] md:-translate-y-[15%]
        [&[aria-selected="true"]]:translate-x-[2rem] [&[aria-selected="true"]]:translate-y-[-2rem]
        md:[&[aria-selected="true"]]:translate-x-[3rem] md:[&[aria-selected="true"]]:translate-y-[-3rem]
        xl:[&[aria-selected="true"]]:translate-x-[3.75rem] xl:[&[aria-selected="true"]]:translate-y-[-3.75rem]
    ',
    '
        right-[11.5%] bottom-[11.5%] translate-x-[20%] translate-y-[20%]
        md:translate-x-[15%] md:translate-y-[15%]
        [&[aria-selected="true"]]:translate-x-[2rem] [&[aria-selected="true"]]:translate-y-[-2rem]
        md:[&[aria-selected="true"]]:translate-x-[3rem] md:[&[aria-selected="true"]]:translate-y-[-3rem]
        xl:[&[aria-selected="true"]]:translate-x-[3.75rem] xl:[&[aria-selected="true"]]:translate-y-[-3.75rem]
    ',
    '
        left-[11.5%] bottom-[11.5%] -translate-x-[20%] translate-y-[20%]
        md:-translate-x-[15%] md:translate-y-[15%]
        [&[aria-selected="true"]]:translate-x-[2rem] [&[aria-selected="true"]]:translate-y-[-2rem]
        md:[&[aria-selected="true"]]:translate-x-[3rem] md:[&[aria-selected="true"]]:translate-y-[-3rem]
        xl:[&[aria-selected="true"]]:translate-x-[4.5rem] xl:[&[aria-selected="true"]]:translate-y-[-3.75rem]
    ',
    '
        left-[11.5%] top-[11.5%] -translate-x-[20%] -translate-y-[20%]
        md:-translate-x-[15%] md:-translate-y-[15%]
        [&[aria-selected="true"]]:translate-x-[2rem] [&[aria-selected="true"]]:translate-y-[-2rem]
        md:[&[aria-selected="true"]]:translate-x-[3rem] md:[&[aria-selected="true"]]:translate-y-[-3rem]
        xl:[&[aria-selected="true"]]:translate-x-[3.75rem] xl:[&[aria-selected="true"]]:translate-y-[-3.75rem]
    ',
    '
        left-[calc(50%-(min(30vw,11.25rem)/2))] top-1/2 -translate-x-1/2 -translate-y-1/2
        md:-translate-y-full
    ',
] %}
{% set stepImagePositions = [
    'left-1/2 top-0 -translate-x-1/2 -translate-y-1/2',
    'right-[11.5%] top-[11.5%] translate-x-1/3 -translate-y-1/3',
    'right-[11.5%] bottom-[11.5%] translate-x-1/3 translate-y-1/3',
    'left-[11.5%] bottom-[11.5%] -translate-x-1/3 translate-y-1/3',
    'left-[11.5%] top-[11.5%] -translate-x-1/3 -translate-y-1/3',
    'left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2'
] %}

<div class="
    platform-visualisation
    bg-gradient-to-r from-primary-100 to-[#7FBE97] relative wrapper c-block overflow-hidden
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="
        container flex justify-end container--padding relative py-12
        lg:py-[12.5rem]
    ">
        <div class="
            flex flex-col
            lg:w-[33.5rem]
        ">
            {% if block.visualisationTitle %}
                {% include '_parts/block-title' with {
                    title: block.visualisationTitle,
                    titleClass: '
                        text-primary-900 mb-20
                        lg:mb-12
                    ',
                } %}
            {% endif %}
            <div class="
                aspect-square max-w-[570px] w-full self-center relative mb-10
                lg:absolute lg:left-8 lg:top-1/2 lg:-translate-y-1/2 lg:w-[min(calc(100vw-41rem),40rem)] lg:mb-0
                xl:left-12
            ">
                <img class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[87.6%] h-[87.6%] z-10" src="/dist/images/platform-visual/inner-radar.svg" alt="" />
                <img
                    class="
                        absolute left-0 top-0 w-full h-full z-20
                        motion-safe:animate-spin-slowest
                    "
                    src="/dist/images/platform-visual/arrow-circle.svg" alt=""
                />
                <div class="absolute left-0 top-0 w-full h-full z-30">
                    {% for step in steps %}
                        {% if loop.index != (steps | length) %}
                            <img
                                class="
                                    {{ loop.first ? 'active' }}
                                    absolute rounded-full scale-0 transition-all w-[5.5rem] h-[5.5rem] shadow-[0.25rem_0.25rem_2rem_0_#00000040]
                                    md:w-[7.75rem] md:h-[7.75rem]
                                    xl:w-[11.25rem] xl:h-[11.25rem]
                                    [&.active]:scale-100
                                    {{ stepImagePositions[loop.index0] }}
                                "
                                src="/dist/images/platform-visual/step-{{ loop.index }}.svg"
                                data-step-image-id="{{ loop.index }}"
                                alt=""
                            />
                        {% else %}
                            <img
                                class="
                                    {{ loop.first ? 'active' }}
                                    absolute rounded-full bg-transparent transition-colors w-[min(30vw,11.25rem)] h-[min(30vw,11.25rem)]
                                    lg:w-[11.25rem] lg:h-[11.25rem]
                                    [&.active]:bg-white [&.active]:shadow-[0.25rem_0.25rem_2rem_0_#00000040]
                                    {{ stepImagePositions[loop.index0] }}
                                "
                                src="/dist/images/platform-visual/step-{{ loop.index }}.svg"
                                data-step-image-id="{{ loop.index }}"
                                alt=""
                            />
                        {% endif %}
                    {% endfor %}
                </div>
                <div class="absolute left-0 top-0 w-full h-full z-40">
                    {% for step in steps %}
                        <button
                            class="
                                {{ loop.first ? 'active' }}
                                absolute w-10 h-10 flex items-center justify-center bg-white rounded-full text-18 font-medium text-primary-500
                                md:w-12 md:h-12 md:text-20
                                hover:bg-primary-900 hover:text-white
                                focus-visible:bg-primary-900 focus-visible:text-white
                                hover:before:border-primary-900
                                focus-visible:before:border-primary-900
                                [&[aria-selected='true']]:bg-primary-900 [&[aria-selected='true']]:text-white [&[aria-selected='true']]:before:!opacity-0
                                before:absolute before:w-full before:h-full before:border-2 before:border-white before:rounded-full before:pointer-events-none before:transition-colors
                                motion-safe:before:animate-ping-slow
                                {{ loop.index != (steps | length) ? 'transition-all sm-max:[&[aria-selected=true]]:w-8 sm-max:[&[aria-selected=true]]:h-8 sm-max:[&[aria-selected=true]]:text-14' : 'transition-colors' }}
                                {{ stepPositions[loop.index0] }}
                            "
                            data-target-step-id="{{ loop.index }}"
                            aria-controls="step-{{ loop.index }}"
                            aria-selected="{{ loop.first ? 'true' : 'false' }}"
                            aria-label="Open {{ loop.index }}. {{ step.stepTitle }}"
                        >
                            {{ loop.index }}
                        </button>
                    {% endfor %}
                </div>
            </div>
            <div class="
                w-full relative
                lg:h-[20rem]
            ">
                {% for step in steps %}
                    <div
                        id="step-{{ loop.index }}"
                        class="
                            {{ loop.first ? 'active' }}
                            hidden w-full bg-white p-6 rounded shadow-[0_0.25rem_1rem_0_#56565629]
                            lg:absolute lg:left-0 lg:top-0 lg:p-8
                            [&[aria-hidden='false']]:block
                        "
                        data-step-id="{{ loop.index }}"
                        aria-hidden="{{ loop.first ? 'false' : 'true' }}"
                    >
                        <h3 class="flex items-baseline h5 mb-4">
                            <span class="w-8 h-8 shrink-0 inline-flex items-center justify-center bg-primary-900 text-white mr-2 font-medium rounded-full text-18">{{ loop.index }}</span>
                            {{ step.stepTitle }}
                        </h3>
                        <p>{{ step.stepText }}</p>
                        {% if step.stepButton and not step.stepButton.isEmpty() %}
                            {% include '_parts/button.twig' with {
                                link: step.stepButton,
                                linkNoFollow: step.stepButtonNoFollow,
                            } %}
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
