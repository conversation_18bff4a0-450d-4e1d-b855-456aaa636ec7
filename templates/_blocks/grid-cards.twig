{% set cards = block.gridCards.all() %}

{% if cards | length %}
    <div class="
        wrapper c-block
        {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
    ">
        <div class="container container--padding">
            {% if block.heading is defined %}
                {% include '_parts/block-title' with {
                    title: block.heading,
                    titleClass: "mb-6 #{block.headingColor}",
                } %}
            {% endif %}
            <div class="grid gap-4 grid-cols-[repeat(auto-fill,minmax(22.5rem,1fr))]">
                {% for card in cards %}
                    <div class="bg-white p-4">
                        <h4 class="h4 text-black">{{ card.heading }}</h4>
                        <div class="*:p-0 *:m-0 [*&_a]:text-gray-900">
                            {{ card.text }}
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
{% endif %}
