{% set quickLinks = blocks | filter(block => block.quickLinkTitle is not empty) | map(block => block.quickLinkTitle) %}

<div class="
    quick-links container container--padding container--small c-block
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    {% if block.quickLinkHeader %}
        {% include '_parts/block-title' with {
            title: block.quickLinkHeader,
            titleClass: 'quick-links__title',
        } %}
    {% endif %}
    <div class="quick-links__list">
        {% for quickLink in quickLinks %}
            <a class="quick-links__item tag tag--primary tag--enable-hover" data-scroll-to="{{ quickLink | kebab }}">{{ quickLink }}</a>
        {% endfor %}
    </div>
</div>
