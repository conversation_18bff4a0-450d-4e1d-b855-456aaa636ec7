{% set doorways = block.doorways.all() %}

<div class="
    doorways__wrapper c-block c-block--bm-sm c-block--padding-small
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="doorways container container--padding">
        <div class="doorways__content">
            {% if block.heading %}
                {% include '_parts/block-title' with {
                    title: block.heading,
                    titleClass: 'doorways__title',
                } %}
            {% endif %}

            {# text #}
            {% if block.richText is not null %}
                <div class="doorways__description wysiwyg">
                    {{ block.richText }}
                </div>
            {% endif %}

            {# button #}
            {% set buttons = block.doorwayButtons.all() %}

            {% if buttons | length %}
                <div class="doorways__buttons">
                    {% for button in buttons %}
                        {% include '_parts/button.twig' with {
                            link: button.buttonLink,
                            type: button.buttonType,
                            linkNoFollow: button.linkNoFollow,
                            class: 'doorways__button',
                        } %}
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        {% if doorways | length %}
            <div class="doorways__list">
                {% for doorway in doorways %}
                    {% include '_parts/doorway-with-image.twig' with {
                        doorway: doorway,
                        class: 'doorways__item'
                    } %}
                {% endfor %}
            </div>
        {% endif %}
    </div>
</div>
