{% set newsletter = block.newsletter %}
{% set usps = newsletter.usps.all() %}

<div class="
    wrapper c-block container px-6 container--medium
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="
        flex flex-col rounded bg-white w-full shadow-[0_2px_9px_0_#0000000D]

        md:flex-row
    ">
        <div class="
            p-6 pb-4 bg-primary-100 w-full
            lg:p-8 lg:pb-6 lg:w-1/2
        ">
            <h2>{{ newsletter.newsletterTitle }}</h2>
            {% if newsletter.newsletterText %}
                <p class="
                    mt-2 text-16 leading-24 pb-0
                    lg:text-18 lg:leading-28
                ">{{ newsletter.newsletterText }}</p>
            {% endif %}
            {% if usps | length %}
                <ul class="mt-10 flex flex-col items-start gap-1">
                    {% for usp in usps %}
                        <li class="relative pl-8 text-16 leading-28">
                            {% include '_parts/icon.twig' with {
                                icon: 'check',
                                class: 'absolute left-0 top-[0.3125rem] text-primary-500 mr-2 text-24 border border-primary-500 rounded-full flex items-center justify-center !w-5 !h-5'
                            } %}
                            {{ usp.uspLabel }}
                        </li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
        <div class="
            p-6 pb-4 w-full
            lg:p-8 lg:pb-6 lg:w-1/2
        ">
            {% include '_parts/customerio/extended-form.twig' with {
                buttonLabel: 'Subscribe' | t,
                urlParam: 'newsletter-subscribed',
                formSource: 'aanmeldbox',
            } %}
        </div>
    </div>
</div>
