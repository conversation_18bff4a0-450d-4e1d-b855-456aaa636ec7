{% set image = block.mediaImage.one() %}

<div class="
    wrapper c-block
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="container">
        {% if block.mediaTitle %}
            {% include '_parts/block-title' with {
                title: block.mediaTitle,
                titleClass: '
                    text-center
                    md-max:mb-6
                    lg:mb-10
                ',
            } %}
        {% endif %}

        <div class="w-full max-w-6xl mx-auto md-max:px-6 md:px-10">
            {# if there is a video, show video #}
            {% if block.mediaVideo %}
                {% include '_parts/video.twig' with {
                    class: 'media__video',
                    src: block.mediaVideo,
                    fallbackImage: image
                } %}
            {% else %}
                {% include '_parts/picture.twig' with {
                    class: 'w-full rounded object-cover max-h-[32rem]',
                    image: image,
                    loading: 'lazy',
                    sizes: ['0.25x', '0.5x', '1x', '1.5x']
                } %}
            {% endif %}
        </div>
    </div>
</div>
