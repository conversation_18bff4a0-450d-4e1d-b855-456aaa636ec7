{% set smallImage = block.smallImage %}
{% set blockLayout = block.layout ?? 'twoColMediaLeft' %}

<div class="
    wrapper c-block
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="text-image-element">
        {# 2 Columns layout #}
            <div class="
                wrapper flex items-center
                {{ smallImage ? 'justify-center' }}
                {{ blockLayout == 'twoColMediaLeft' ? 'md-max:flex-col-reverse lg:flex-row-reverse' : 'md-max:flex-col lg:flex-row' }}
            ">
                {% include "_parts/text-image-element/content" %}
                {% include "_parts/text-image-element/media" %}
            </div>
    </div>
</div>
