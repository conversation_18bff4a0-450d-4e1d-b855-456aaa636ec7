{% set isCustomSelection = block.articleDoorwaySelector is defined ? block.articleDoorwaySelector.value == 'custom' : true %}
{% set articleNofollow = block.articleDoorwaysNoFollow %}
{% set sliderLimit = block.sliderLimit | default(5) %}

{% if isCustomSelection %}
    {% set sliderItems = block.sliderWithNoFollow.with([
            ['thumbnail', {
                withTransforms: ['thumbnail1000']
            }]
    ]).all() %}
{% else %}
    {% set sliderItemsQuery = craft.entries.section('article').with([
            ['thumbnail', {
                withTransforms: ['thumbnail1000']
            }]
    ]) %}

    {% if block.articleDoorwayCategoryArticle | length %}
        {% set sliderItemsQuery = sliderItemsQuery.andRelatedTo(block.articleDoorwayCategoryArticle) %}
    {% endif %}
    {% if block.articleDoorwayCategoryCompany | length %}
        {% set sliderItemsQuery = sliderItemsQuery.andRelatedTo(block.articleDoorwayCategoryCompany) %}
    {% endif %}
    {% if block.articleDoorwayCategoryDisposal | length %}
        {% set sliderItemsQuery = sliderItemsQuery.andRelatedTo(block.articleDoorwayCategoryDisposal) %}
    {% endif %}

    {% set sliderItems = sliderItemsQuery.limit(sliderLimit).all() %}
{% endif %}

<div class="
    article-slider c-block swiper
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    {% if block.sliderTitle is defined %}
        {% include '_parts/block-title' with {
            title: block.sliderTitle,
            titleClass: 'article-slider__title container container--padding',
        } %}
    {% endif %}

    <div class="article-slider__slides swiper-wrapper">
        {% for slide in sliderItems %}
            {% if isCustomSelection %}
                {% set article = slide.articleSlide %}
                {% for entry in article %}
                    {% set thumbnail = entry.thumbnail.one().url | default() %}

                    <a href="{{ entry.url }}" class="article-slider__slide swiper-slide"
                        {% if slide.linkNoFollow ? 'true' %} rel="nofollow" {% endif %}
                        style="--background-image: url('{{ thumbnail }}');">
                        {% set tags = entry.disposalType.all() %}
                        {% if tags | length %}
                            <div class="article-slider__slide-tags">
                                {% for tag in tags %}
                                    <span class="article-slider__slide-tag tag tag--primary">{{ tag }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <h3 class="article-slider__slide-title">
                            {{ entry.title }}
                            {% include '_parts/icon.twig' with {
                                icon: 'arrow-right',
                                class: 'article-slider__slide-icon'
                            } %}
                        </h3>
                    </a>
                {% endfor %}
            {% else %}
                {% set thumbnail = slide.thumbnail.one().url | default() %}

                <a href="{{ slide.url }}" class="article-slider__slide swiper-slide"
                    {% if articleNofollow ? 'true' %} rel="nofollow" {% endif %}
                    style="--background-image: url('{{ thumbnail }}');">
                    {% set tags = slide.disposalType %}
                    {% if tags | length %}
                        <div class="article-slider__slide-tags">
                            {% for tag in tags %}
                                <span class="article-slider__slide-tag tag tag--primary">{{ tag }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <h3 class="article-slider__slide-title">
                        {{ slide.title }}
                        {% include '_parts/icon.twig' with {
                            icon: 'arrow-right',
                            class: 'article-slider__slide-icon'
                        } %}
                    </h3>
                </a>
            {% endif %}
        {% endfor %}
    </div>

    <div class="article-slider__controls container container--padding">
        <div class="article-slider__scrollbar swiper-scrollbar">
            {# Swiper generates a child with the `article-slider__scrollbar-thumb` class #}
        </div>
        <div class="article-slider__buttons">
            <button class="article-slider__button article-slider__button--prev swiper-button-prev" aria-label="Schuif naar het vorige artikel">
                {% include '_parts/icon.twig' with {
                    icon: 'arrow-left'
                } %}
            </button>
            <button class="article-slider__button article-slider__button--next swiper-button-next" aria-label="Schuif naar het volgende artikel">
                {% include '_parts/icon.twig' with {
                    icon: 'arrow-right'
                } %}
            </button>
        </div>
    </div>
</div>
