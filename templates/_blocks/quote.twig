{% set text = block.richText %}
{% set author = block.articleAuthor.one() %}

<div class="
    wrapper c-block
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
    quote-gradient py-8
">
    <div class="container--small mx-auto items-center flex flex-col gap-6">
        <svg width="53" height="38" viewBox="0 0 53 38" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path opacity="0.3" d="M0.436438 37.584C0.244438 36.336 0.148438 35.088 0.148438 33.84C0.148438 32.592 0.148438 31.584 0.148438 30.816C0.340438 25.056 1.44444 19.536 3.46044 14.256C5.57244 8.976 8.11644 4.224 11.0924 0L23.9084 3.456C21.9884 7.68 20.5004 12.144 19.4444 16.848C18.3884 21.552 17.8604 26.016 17.8604 30.24C17.8604 31.008 17.8604 32.112 17.8604 33.552C17.8604 34.992 17.9564 36.336 18.1484 37.584H0.436438ZM29.3804 37.584C29.1884 36.336 29.0924 35.088 29.0924 33.84C29.0924 32.592 29.0924 31.584 29.0924 30.816C29.2844 25.056 30.3884 19.536 32.4044 14.256C34.5164 8.976 37.0604 4.224 40.0364 0L52.8524 3.456C50.9324 7.68 49.4444 12.144 48.3884 16.848C47.3324 21.552 46.8044 26.016 46.8044 30.24C46.8044 31.008 46.8044 32.112 46.8044 33.552C46.8044 34.992 46.9004 36.336 47.0924 37.584H29.3804Z" fill="#007E31"/>
        </svg>

        <figure class="flex flex-col gap-6 mt-2">
            <blockquote class="w-full text-center mt-2 *:h3 md-max:px-6 md:px-10">
                {{ text }}
            </blockquote>
            <figcaption class="flex flex-col text-center gap-2">
                <cite class="h4 not-italic">{{ author.title }}</cite>
                <span class="font-medium text-16 leading-16">{{ author.jobTitle }}</span>
            </figcaption>
        </figure>
    </div>
</div>
