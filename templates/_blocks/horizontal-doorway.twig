{% set items = block.horizontalDoorwayItems.with(['image', 'itemLink']).all() %}


<div class="
    container container--padding c-block
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <div class="horizontal-doorways__wrapper">
        {% for doorway in items %}
            {% include '_parts/doorway-with-image.twig' with {
                doorway: doorway,
                class: 'horizontal-doorways__item horizontal-doorways__item--' ~ doorway.itemColor
            } %}
        {% endfor %}
    </div>
</div>
