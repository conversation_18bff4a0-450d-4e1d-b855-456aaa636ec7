{% set button = block.button.one() %}
{% set isCustomSelection = block.articleDoorwaySelector is defined ? block.articleDoorwaySelector.value == 'custom' : true %}
{% set articleNofollow = block.articleDoorwaysNoFollow %}

{% if isCustomSelection %}
    {% set doorwayItems = block.doorwayItemsWithNoFollow.all() %}
{% else %}
    {% set doorwayItemsQuery = craft.entries.section('article') %}

    {% if block.articleDoorwayCategoryArticle | length %}
        {% set doorwayItemsQuery = doorwayItemsQuery.andRelatedTo(block.articleDoorwayCategoryArticle) %}
    {% endif %}
    {% if block.articleDoorwayCategoryCompany | length %}
        {% set doorwayItemsQuery = doorwayItemsQuery.andRelatedTo(block.articleDoorwayCategoryCompany) %}
    {% endif %}
    {% if block.articleDoorwayCategoryDisposal | length %}
        {% set doorwayItemsQuery = doorwayItemsQuery.andRelatedTo(block.articleDoorwayCategoryDisposal) %}
    {% endif %}

    {% set doorwayItems = doorwayItemsQuery.limit(3) %}
{% endif %}

<div class="
    wrapper c-block
    {{ block.bottomMargin.value is defined and block.bottomMargin.value != 0 ? block.bottomMargin.value : 'c-block--bm-md' }}
">
    <section class="container container--padding">
        {% if block.doorwayTitle %}
            <div class="article-doorway__header">
                {% include '_parts/block-title' with {
                    title: block.doorwayTitle,
                    titleClass: 'article-doorway__title',
                } %}
                {% if button is not null %}
                    {% include '_parts/button.twig' with {
                        link: button.ctaLink,
                        type: button.ctaType,
                        linkNoFollow: button.linkNoFollow,
                        class: 'article-doorway__button',
                    } %}
                {% endif %}
            </div>
        {% endif %}
        <ul class="grid lg:grid-cols-3 gap-x-8 gap-y-12">
            {% for doorwayItem in doorwayItems %}
                <li class="[&>a]:h-full">
                    {% include '_parts/article-doorway.twig' with {
                        entry: isCustomSelection ? doorwayItem.doorwayItem.one() : doorwayItem,
                        linkNofollow: isCustomSelection ? doorwayItem.linkNoFollow : articleNofollow,
                    } %}
                </li>
            {% endfor %}
        </ul>
    </section>
</div>
