{# content blocks #}
{% set blocks = blocks ?? entry.contentBlocks.all() %}

{% if blocks | length %}
    {% for block in blocks %}
        {# twigcs use-var block #}
        {% if block.quickLinkTitle is not empty %}
            <a class="quick-links__anchor" id="{{ block.quickLinkTitle | kebab }}"></a>
        {% endif %}
        {% include '_blocks/' ~ block.type | kebab ~ '.twig' with {
            'block': block
        } %}
    {% endfor %}
{% endif %}
