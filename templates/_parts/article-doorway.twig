{% set excludeAuthor = excludeAuthor | default(false) %}

{% if not excludeAuthor %}
    {% set author = entry.articleAuthor is defined and entry.articleAuthor is not null ? entry.articleAuthor.one() : null %}
{% endif %}

<a
    href="{{ entry.url }}"
    class="p-4 rounded border border-tertiary-300 flex flex-col shadow-[0px_2px_9px_0px_#0000000D]"
    {% if linkNofollow is defined and linkNofollow == 'true' %} rel="nofollow" {% endif %}
    aria-label="{{ 'read more about' | t | capitalize }} {{ entry.title }}"
    onclick="selectNewsItem(event)"
    data-entry="{{ {
        name: entry.title
    } | json_encode | e('html_attr') }}"
>
    {% if entry.thumbnail | length %}
        {% set image = entry.thumbnail[0] %}
        {% include '_parts/picture.twig' with {
            image: image,
            loading: 'lazy',
            width: 390,
            attributes: "style=\"object-position: #{image.focalPoint.x * 100}% #{image.focalPoint.y * 100}%;\"",
            class: 'rounded mb-6 shrink-0 h-[140px] w-full object-cover lg:h-[200px]'
        } %}
    {% endif %}
    <div class="flex flex-col gap-4 grow">
        <div class="flex flex-col gap-2">
            <span class="font-medium h5">
                {{ entry.keyVisualTitle ?? entry.title }}
            </span>
            {% if entry.section.handle == 'article' %}
                <span class="text-14 text-tertiary-500">
                    {% if not excludeAuthor %}
                        {% if author %}
                            {{ 'By' | t }} {{ author.title }} &nbsp;&nbsp;•&nbsp;&nbsp;
                        {% endif %}
                    {% endif %}
                    {{ entry.postDate | date('d-m-Y') }}
                </span>
            {% endif %}
        </div>
        {% set excerpt = entry.description %}
        {% if not excerpt and entry.keyVisualIntro %}
            {% set excerpt = entry.keyVisualIntro %}
        {% endif %}
        {% if not excerpt and entry.contentBlocks | length %}
            {# Generate excerpt from first text block #}
            {% for block in entry.contentBlocks.all() %}
                {% if block.type == 'text' and block.text %}
                    {% set excerpt = block.text | striptags | slice(0, 150) ~ '...' %}
                    {% break %}
                {% endif %}
            {% endfor %}
        {% endif %}

        {% if excerpt %}
            <div class="line-clamp-2">
                {{ excerpt }}
            </div>
        {% endif %}
        {% include '_parts/article/tags.twig' with {
            class: 'mt-auto'
        } %}
    </div>
</a>
