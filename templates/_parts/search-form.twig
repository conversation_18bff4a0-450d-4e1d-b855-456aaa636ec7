<form
    {{ id is not defined ? '' : 'id=' ~ id }}
    action="{{ url(craft.entries().section('search').one() ? craft.entries().section('search').one().getUrl() : 'zoeken') }}"
    class="search-form {{ class | default('') }}"
    {{ attributes | default('') | raw }}
>
    <button type="submit" class="flex items-center gap-2 rounded">
        {% include '_parts/icon.twig' with {icon: 'search', class: 'search-form__icon text-primary-500 p-3 hover:text-primary-900 duration-300 text-26'} %}
        <span class="sr-only">{{ 'Search' | t }}</span>
    </button>
    <label class="block relative flex-1 items-center">
        <input
            type="search"
            name="q"
            class="
                search-form__input w-full bg-transparent block text-tertiary-900
                {{ inputClass ?? '' }}
            "
            placeholder="{{ placeholder is defined ? placeholder : ' ' }}"
            {% if value is defined %}
                value="{{ value }}"
            {% endif %}
            {{ inputAttributes | default('') | raw }}
        >
        <span class="search-form__label absolute top-1/2 left-0 -translate-y-1/2 transition-transform text-14 lg:text-18 font-normal text-tertiary-700 {{ hideLabel is not defined ? '' : 'sr-only' }}">
            {{ 'What are you looking for?' | t }}
        </span>
    </label>
</form>
