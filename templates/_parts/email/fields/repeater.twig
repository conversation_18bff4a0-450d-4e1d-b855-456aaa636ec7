<div style="margin: 1em 0;">
    {% if not (renderOptions.hideName ?? false) %}
    <strong>{{ field.name | t('formie') }}</strong>
    {% endif %}

    {% set fields = field.getFieldLayout().getCustomFields() %}

    {% if value.exists() %}
    <table class="email-table">
        {% for row in value.all() %}

        <tbody>
        {% for field in fields %}
        {% set fieldValue = (row ? row.getFieldValue(field.handle) : field.normalizeValue(null)) %}

        {% set html = field.getEmailHtml(submission, notification, fieldValue, {
        hideName: true,
        }) %}

        {% if html %}
        <tr>
            <th class="field-label"
                style="text-align:left;width:30%;padding:4px;font-family:arial,'helvetica neue',helvetica,sans-serif;line-height:21px;color:rgb(255,255,255);font-size:14px">
                {% if field.hasLabel %}
                {{ field.name | t('formie') }}:
                {% endif %}
            </th>

            <td class="field-value"
                style="padding:4px;font-family:arial,'helvetica neue',helvetica,sans-serif;line-height:21px;color:rgb(255,255,255);font-size:14px">
                {{ html | raw }}
            </td>
        </tr>
        {% endif %}
        {% endfor %}
        </tbody>
        {% endfor %}
    </table>
    {% else %}
    <p>{{ notification.getPlaceholder() }}</p>
    {% endif %}
</div>