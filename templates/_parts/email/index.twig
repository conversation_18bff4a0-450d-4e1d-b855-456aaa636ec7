<!doctype html>
<html>
<head>
    <title>Thanks for your email</title>
    <style>
        .redkiwi-email-body blockquote {
            margin-left: 0;
            margin-right: 0;
        }

        .redkiwi-email-body h1 {
            margin-bottom:30px;
            line-height:30px;
            font-family:arial,"helvetica neue",helvetica,sans-serif;
            font-size:20px;
            font-style:normal;
            font-weight:bold;
            color:rgb(255,255,255);
            text-align:left
        }

        .redkiwi-email-body th.field-label {
            vertical-align: top;
        }

        .redkiwi-email-body td.field-value p {
            margin: 0;
        }

        .redkiwi-email-body .email-table {
            width: 100%;
            margin: 1em 0;
            border-collapse: collapse;
            border:none;
        }

        .redkiwi-email-body .email-table td.field-label {
            text-align: left;
            vertical-align: top;
            width: 30%;
            padding: 4px;
            font-family: arial,'helvetica neue',helvetica,sans-serif;
            line-height: 21px;
            color: rgb(255,255,255);
            font-size: 14px
        }

        .redkiwi-email-body .email-table td.field-value {
            padding: 4px;
            font-family: arial,'helvetica neue',helvetica,sans-serif;
            line-height: 21px;
            color: rgb(255,255,255);
            font-size: 14px
        }
    </style>
</head>

<body class="redkiwi-email-body">

{% include '_parts/email/email-header' %}

<table style="margin: auto; border-collapse:collapse;border-spacing:0px;background-color:rgb(255,255,255);width:640px">
    <tbody style="width: 100%">
    <tr>
        <td style="padding-bottom: 30px">
            <table style="background-color: {{ backgroundColor }};width:100%">
                <tbody>
                <tr>
                    <td style="padding:30px;margin:0px;font-family:arial,'helvetica neue',helvetica,sans-serif;line-height:21px;color:rgb(255,255,255);font-size:14px">
                        {{ contentHtml }}
                    </td>
                </tr>
                </tbody>
            </table>
        </td>
    </tr>
    </tbody>
</table>

{% include '_parts/email/email-footer' %}

</body>
</html>