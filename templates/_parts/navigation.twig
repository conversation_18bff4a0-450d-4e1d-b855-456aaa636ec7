{% set links = craft.navigation.nodes().handle('mainNavigation').level(1).with(['children.children.children.children']).cache(60).all %}
{% set buttons = navigation.navigationButton.all() %}
{% set pageIdsWithoutStickyCTA = navigation.pagesWithoutStickyCTA.all() | map(entry => entry.id) %}

<nav class="navigation">
    <div class="navigation__inner container container--padding">
        <a class="navigation__logo" href="{{ siteUrl }}" aria-label="Home pagina">
            <img src="/dist/images/logos/logo-horizontal.svg" height="64" alt="Milieu Service Nederland" />
        </a>

        {% include '_parts/navigation/menus-mobile.twig' with {
            links
        } %}
        {% include '_parts/navigation/menus-desktop.twig' with {
            links
        } %}

        <div class="navigation__search navigation__search--desktop">
            <button
                id="open-search"
                class="navigation__search-toggle"
                aria-expanded="false"
                aria-controls="nav-search"
                aria-label="Zoekbalk"
            >
                {% include '_parts/icon.twig' with {
                    icon: 'search'
                } %}
                <span class="sr-only">Open zoekveld</span>
            </button>
            {% include '_parts/search-form.twig' with {
                id: 'nav-search',
                class: 'search-form--nav-dekstop',
                hideLabel: true,
                placeholder: 'Waar ben je naar op zoek?'
            } %}
        </div>
        {% include '_parts/navigation/buttons.twig' with {
            buttons
        } %}
    </div>
</nav>
{% if entry is defined and entry.id not in pageIdsWithoutStickyCTA and entry.showOrderButton %}
    <div class="navigation__sticky-wrapper hide-sticky">
        {% for button in buttons %}
            {% set link = button.linkItem.page %}
            {% set icon = button.linkItem.icon.label %}

            {% include '_parts/button.twig' with {
                link: link,
                type: button.buttonType,
                icon: icon,
                iconPosition: button.buttonIconPosition,
                class: 'navigation__button' ~ (isMobile is defined and isMobile == true ? ' navigation__button--center'),
            } %}
        {% endfor %}
    </div>
{% endif %}
