{% set author = entry.articleAuthor.one() %}

{% set words = entry.keyVisualHighlightedWords | split(' ') %}
{% set headingText = entry.keyVisualTitle | split(' ') %}
{% set heading = [] %}
{% for word in headingText %}
    {% if word in words %}
        {% set heading = heading | push('<span class="text-primary-500">' ~ word ~ '</span>') %}
    {% else %}
        {% set heading = heading | push(word) %}
    {% endif %}
{% endfor %}
{% set heading = heading | join(' ') %}

<header class="mb-20">
    <div class="
        bg-[#E8E8E866] pt-[3.5rem] pb-[7.5rem]
        lg:pt-20 lg:pb-[9rem]
    ">
        <div class="container container--medium px-6">
            <div class="
                flex gap-6 flex-col
                lg:gap-8 lg:flex-row
            ">
                <div class="flex flex-col items-start grow">
                    {% include '_parts/article/subtitle.twig' with {
                        text: "#{'Published at:' | t} #{entry.postDate | date('d F Y')}"
                    } %}
                    <h1 class="h2">{{ heading | raw }}</h1>
                    {% include '_parts/article/tags.twig' with {
                        withLink: true,
                        class: 'mt-8 lg:mt-10'
                    } %}
                </div>
                <div class="
                    pt-6 border-t border-tertiary-500/40 w-full shrink-0
                    lg:border-t-0 lg:border-l lg:pl-8 lg:max-w-[20rem] lg:pt-2
                ">
                    {% if author %}
                        {% include '_parts/article/subtitle.twig' with {
                            text: 'Written by:' | t
                        } %}
                        {% include '_parts/article/author.twig' with {
                            author: author,
                            class: 'mb-8'
                        } %}
                    {% endif %}
                    {% include '_parts/article/subtitle.twig' with {
                        text: 'Share:' | t
                    } %}
                    {% include '_parts/article/share-buttons.twig' %}
                </div>
            </div>
        </div>
    </div>
    <div class="max-w-[72rem] mx-auto mt-[-5.75rem] px-6">
        {% set image = entry.keyVisualImage and entry.keyVisualImage.one() ? entry.keyVisualImage.one() : entry.thumbnail.one() %}

        {% include '_parts/picture.twig' with {
            image: image,
            width: 1300,
            class: 'rounded w-full aspect-[327/230] object-cover md:aspect-[1104/420]',
            attributes: "style=\"object-position: #{image.focalPoint.x * 100}% #{image.focalPoint.y * 100}%;\"",
            loading: 'lazy'
        } %}
        {% if entry.keyVisualIntro %}
            <div class="
                mx-auto mt-12 max-w-[51.5rem]
                lg:max-w-[49.5rem]
                xl:max-w-[47.5rem]
            ">
                {{ entry.keyVisualIntro }}
            </div>
        {% endif %}
    </div>
</header>
