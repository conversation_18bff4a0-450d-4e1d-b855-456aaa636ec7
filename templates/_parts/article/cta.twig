{% set articleCTA = entry.articleCTA.one() %}
{% set ctaTitle = articleCTA ? articleCTA.ctaTitle : null %}
{% set ctaText = articleCTA ? articleCTA.ctaText : null %}
{% set ctaButton = articleCTA and articleCTA.ctaButton and not articleCTA.ctaButton.isEmpty() ? articleCTA.ctaButton : null %}
{% set ctaExpert = articleCTA and articleCTA.ctaExpert.one() ? articleCTA.ctaExpert.one() : null %}

{% if ctaTitle or ctaText or ctaButton or ctaExpert %}
    <div class="
        mb-[3.5rem] max-w-[min(54.5rem,calc(100vw-3rem))] mx-auto flex flex-col gap-6 bg-primary-100 p-6
        md:p-8 md:gap-8
        lg:p-12 lg:gap-12
    ">
        {% if ctaTitle or ctaText or ctaButton %}
            <div class="flex flex-col gap-4">
                {% if ctaTitle %}
                    <h2>{{ ctaTitle }}</h2>
                {% endif %}
                {% if ctaText %}
                    <div class="wysiwyg wysiwyg--no-outer-margins">{{ ctaText }}</div>
                {% endif %}
            </div>
        {% endif %}
        {% if ctaExpert or ctaButton %}
            <div class="
                flex flex-col gap-4 w-full
                lg:flex-row lg:justify-between
            ">
                {% include '_parts/article/author.twig' with {
                    author: ctaExpert,
                    showAbout: true,
                    class: 'grow'
                } %}
                <div class="
                    flex items-start gap-4 flex-col w-full
                    md:flex-row md:items-center
                    md-max:justify-end
                    lg:w-auto lg:shrink-0
                ">
                    {% if ctaExpert.email or ctaExpert.phone %}
                        {% set linkClass = '
                            inline-flex items-center text-16 leading-24 text-primary-500 border-b border-transparent
                            focus-visible:border-primary-500
                            hover:border-primary-500
                        ' %}
                        <div class="flex flex-col items-start shrink-0">
                            {% if ctaExpert.email %}
                                <a class="{{ linkClass }}" href="mailto:{{ ctaExpert.email }}">
                                    {% include '_parts/icon.twig' with {
                                        icon: 'mail',
                                        class: 'mr-1 text-18'
                                    } %}
                                    {{ ctaExpert.email }}
                                </a>
                            {% endif %}
                            {% if ctaExpert.phone %}
                                <a class="{{ linkClass }} mt-2" href="{{ ctaExpert.phone | replace({'-': ''}) }}">
                                    {% include '_parts/icon.twig' with {
                                        icon: 'phone',
                                        class: 'mr-1 text-18'
                                    } %}
                                    {{ ctaExpert.phone | replace({
                                        'tel:': '',
                                        '-': ' '
                                    }) }}
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                    {% if ctaButton %}
                        {% include '_parts/button.twig' with {
                            link: ctaButton,
                            type: 'primary',
                            class: 'shrink-0'
                        } %}
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
{% endif %}
