{% set withLink = withLink is defined ? withLink : false %}
{% set articleTypes = entry.articleType is not null ? entry.articleType.all() %}
{% set companyTypes = entry.companyType is not null ? entry.companyType.all() %}
{% set disposalTypes = entry.disposalType is not null ? entry.disposalType.all() %}
{% set themeTypes = entry.themeType is not null ? entry.themeType.all() %}

{% macro tag(tagEntry, primary=false, newsOverviewUrl=false, name, noParams=false) %}
    <{{ newsOverviewUrl ? noParams ? "a href=#{newsOverviewUrl}" : "a href=#{newsOverviewUrl}?#{name}%5B%5D=#{tagEntry.slug}" : 'span' }} class="rounded p-1 border border-primary-500 text-14 leading-16 {{ primary ? 'bg-primary-500 text-white' : 'text-primary-500' }} {{ newsOverviewUrl ? 'focus-visible:underline hover:underline' }}">
        {{ tagEntry.title }}
    </{{ newsOverviewUrl ? 'a' : 'span' }}>
{% endmacro %}

<div class="flex flex-wrap gap-2 {{ class ?? '' }}">
    {% for articleType in articleTypes %}
        {{ _self.tag(articleType, true, withLink ? articleType.url : false, 'articleType', true) }}
    {% endfor %}
    {% for tag in companyTypes %}
        {{ _self.tag(tag, false, withLink ? newsOverview.url : false, 'companyType') }}
    {% endfor %}
    {% for tag in disposalTypes %}
        {{ _self.tag(tag, false, withLink ? newsOverview.url : false, 'disposalType') }}
    {% endfor %}
    {% for tag in themeTypes %}
        {{ _self.tag(tag, false, withLink ? newsOverview.url : false, 'themeType') }}
    {% endfor %}
</div>
