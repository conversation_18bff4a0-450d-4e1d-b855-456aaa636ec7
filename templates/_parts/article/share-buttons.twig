{% set shareButtonClass = '
    transition-colors
    hover:text-primary-500
' %}

<div class="flex gap-2">
    <a
        href="https://www.linkedin.com/shareArticle?mini=true&url={{ craft.app.request.absoluteUrl }}{{ '?utm_source=linkedin&utm_medium=organic&utm_campaign=shared' | url_encode }}"
        target="_blank"
        class="{{ shareButtonClass }}"
        aria-label="{{ 'Share on' | t }} LinkedIn"
    >
        <span class="icon icon-linkedin text-24"></span>
    </a>
    <a
        href="http://www.facebook.com/sharer/sharer.php?u={{ craft.app.request.absoluteUrl }}{{ '?utm_source=facebook&utm_medium=organic&utm_campaign=shared' | url_encode }}&t={{ entry.keyVisualTitle ?? entry.title }}"
        target="_blank"
        class="{{ shareButtonClass }}"
        aria-label="{{ 'Share on' | t }} Facebook"
    >
        <span class="icon icon-facebook text-24"></span>
    </a>
    <a
        href="whatsapp://send?text={{ craft.app.request.absoluteUrl }}{{ '?utm_source=whatsapp&utm_medium=organicl&utm_campaign=shared' | url_encode }}"
        target="_blank"
        data-action="share/whatsapp/share"
        class="{{ shareButtonClass }}"
        aria-label="{{ 'Share on' | t }} WhatsApp"
    >
        <span class="icon icon-whatsapp text-24"></span>
    </a>
    <button
        class="
            share-button
            relative cursor-pointer
            {{ shareButtonClass }}
        "
        aria-label="{{ 'Share link' | t }}"
        data-page-title="{{ entry.keyVisualTitle ?? entry.title }}"
        data-page-url="{{ craft.app.request.absoluteUrl }}?utm_source=link&utm_medium=referral&utm_campaign=shared"
    >
        <span class="
            absolute top-0 left-full pl-2 pointer-events-none text-14 leading-24 opacity-0 text-tertiary-900 transition-opacity
            [.copied_>_&]:opacity-100 [.copied_>_&]:delay-[0]
        ">
            {{ 'Copied!' | t }}
        </span>
        <span class="icon icon-link text-24"></span>
    </button>
</div>
