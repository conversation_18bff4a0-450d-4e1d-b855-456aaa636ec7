{% if author %}
    <div class="flex items-center gap-4 text-tertiary-900/90 {{ class | default('') }}">
        {% include '_parts/picture.twig' with {
            image: author.authorImage.one(),
            width: 72,
            height: 72,
            class: 'rounded-full shrink-0',
        } %}
        <div class="flex flex-col items-start">
            <span class="text-16 leading-18 font-medium">{{ author.title }}</span>
            {% if author.jobTitle %}
                <span class="text-14 leading-24">{{ author.jobTitle }}{{ (showAbout | default(false)) and author.aboutTheAuthor ? (' | ' ~ author.aboutTheAuthor) }}</span>
            {% endif %}
        </div>
    </div>
{% endif %}
