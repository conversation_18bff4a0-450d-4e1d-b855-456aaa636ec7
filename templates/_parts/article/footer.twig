<div class="
    flex justify-between flex-col gap-4 pt-8 border-t border-tertiary-500/40 max-w-[min(54.5rem,calc(100vw-3rem))] mx-auto mb-[9rem]
    lg:flex-row lg:gap-8
">
    {% set footerColumnClass = '
        flex flex-col items-start text-tertiary-900/90
        lg:max-w-[calc(33%-(4rem/3))]
    ' %}
    <div class="{{ footerColumnClass }}">
        {% include '_parts/article/subtitle.twig' with {
            text: 'Tags:' | t
        } %}
        {% include '_parts/article/tags.twig' with {
            withLink: true,
        } %}
    </div>
    <div class="{{ footerColumnClass }}">
        {% include '_parts/article/subtitle.twig' with {
            text: 'Published at:' | t
        } %}
        <span class="text-16 leading-18">{{ entry.postDate | date('d F Y') }}</span>
    </div>
    <div class="{{ footerColumnClass }}">
        {% include '_parts/article/subtitle.twig' with {
            text: 'Share:' | t
        } %}
        {% include '_parts/article/share-buttons.twig' %}
    </div>
</div>
