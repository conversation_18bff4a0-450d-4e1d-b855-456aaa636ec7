{% set keyVisualButton = entry.keyVisualButton ?? null %}
{% set buttonType = entry.ctaButtonType ?? 'tag' %}
{% set hiddenClass = '' %}

{% switch entry.ctaHiddenFrom %}
    {% case 'mobile-tablet' %}
        {% set hiddenClass = 'lg:hidden' %}
    {% case 'mobile' %}
        {% set hiddenClass = 'md:hidden' %}
    {% case 'tablet-desktop' %}
        {% set hiddenClass = 'sm-max:hidden' %}
    {% case 'desktop' %}
        {% set hiddenClass = 'md-max:hidden' %}
{% endswitch %}

{% if keyVisualButton.type and keyVisualButton.url %}
    <a
        class="
            {{ buttonType == 'tag' ? 'tag tag--primary tag--enable-hover' : 'btn btn-primary' }}
            {{ hiddenClass }}
        "
        {{ keyVisualButton.getLinkAttributes() }}
        {{ entry.keyVisualCtaNoFollow ? 'rel="nofollow"' }}
    >
        {{ keyVisualButton.getText() | t }}
    </a>
{% endif %}
