{% set linkClasses = 'cursor-pointer scrollhint relative flex flex-row flex-nowrap items-center md-max:mb-8' %}
{% set textClasses = 'pl-4 text-primary-500 text-18 font-bold' %}
{% set button = entry.keyVisualButton is defined ? entry.keyVisualButton %}
{% set noFollow = entry.keyVisualCtaNoFollow is defined ? entry.keyVisualCtaNoFollow %}

{% if button and not button.isEmpty %}
    {% include '_parts/button.twig' with {
        component: 'a',
        link: button,
        class: 'mb-8 md:mb-0',
        linkNoFollow: noFollow,
    } %}
{% else %}
    {% if entry.keyVisualType == 'doorways' %}
        <a data-scroll-to="main-content" class="{{ linkClasses }}{% if not entry.keyVisualIntro %} mt-8 lg:mt-12{% endif %}">

            <span class="scrollhint-animation"></span>

            <span class="{{ textClasses }}">
                Sc<PERSON> verder
            </span>

        </a>
    {% elseif entry.keyVisualType == 'media' %}

        <a data-scroll-to="main-content" class="{{ linkClasses }}{% if not entry.keyVisualIntro %} mt-8 lg:mt-12{% endif %}{% if entry.keyVisualVideo %} lg:mb-12{% endif %}">

            <span class="icon icon-arrow-circle-down text-primary-500 text-32"></span>

            <span class="{{ textClasses }}">
                {% if entry.keyVisualScrollText %}
                    {{ entry.keyVisualScrollText }}
                {% else %}
                    Scrol verder
                {% endif %}
            </span>

        </a>
    {% endif %}
{% endif %}
