{% set image = entry.keyVisualImage.one %}

<header class="
    relative pt-[9.5rem] pb-16 wrapper
    after:absolute after:size-full after:top-0 after:left-0 after:bg-black/30 after:z-20
    {{ entry.keyVisualCloseContentGap ? 'wrapper--no-bm' : 'wrapper--small-bm' }}
">
    {% if image %}
        {% include '_parts/picture' with {
            image: image,
            width: 1920,
            height: 1080,
            class: 'absolute size-full object-cover top-0 left-0',
            loading: 'eager',
        } %}
    {% endif %}
    <div class="relative container container--padding z-30 !text-white">
        <h1 class="
            text-white
            lg:max-w-[75%]
            xl:max-w-[50%]
        ">{{ entry.keyVisualTitle ?? entry.title }}</h1>
        {% if entry.keyVisualIntro %}
            <div class="
                wysiwyg text-white mt-6
                [&>*]:text-white
                [&>*]:last:mb-0
            ">{{ entry.keyVisualIntro }}</div>
        {% endif %}
    </div>
</header>
