{% set homeDoorways = homeDoorways ?? false %}

<ul class="relative z-0 w-full {{ listClass ?? 'lg:w-[40%]' }}">

    {% for item in doorways %}
        {% if item.doorwayTitle or item.doorwayIntro %}
            {% if item.doorwayLink %}

                <li class="keyvisual-doorway has-hover-animation {{ homeDoorways ? '!mb-0 keyvisual-doorway--simple keyvisual-doorway--hover-' ~ item.hoverColor }}">

                    <a class="keyvisual-doorway-link has-icon-right" href="{{ item.doorwayLink.url }}" aria-label="{{ item.doorwayTitle }}" {% if item.doorwayLink.target %} target="{{ item.doorwayLink.target }}" {% endif %} {% if item.doorwayLink.target == '_blank' or item.doorwayNoFollow %} rel="noopener noreferrer {{ item.doorwayNoFollow ? 'nofollow' }}" {% endif %}>
                        {% include '_parts/icon.twig' with {
                            icon: 'arrow-right'
                        } %}
                        <p class="h3 text-primary-500 pb-2 flex gap-2 items-center">
                            {% if item.icon is defined and item.icon is iterable and item.icon.label %}
                                {% include '_parts/icon.twig' with {
                                    icon: item.icon.label,
                                    class: 'text-40 text-tertiary-900 block'
                                } %}
                            {% endif %}
                            {{ item.doorwayTitle }}
                        </p>
                        <p>{{ item.doorwayIntro }}</p>
                    </a>

                </li>

            {% else %}

                <li class="keyvisual-doorway {{ homeDoorways ? '!mb-0 keyvisual-doorway--simple keyvisual-doorway--hover-' ~ item.hoverColor }}">
                    <span class="keyvisual-doorway-nolink">
                        {% if item.icon is defined and item.icon is iterable and item.icon.label %}
                            {% include '_parts/icon.twig' with {
                                icon: item.icon.label,
                                class: 'text-40 mb-4 text-tertiary-900 block'
                            } %}
                        {% endif %}
                        <p class="h3 text-primary-500 pb-2">{{ item.doorwayTitle }}</p>
                        <p>{{ item.doorwayIntro }}</p>
                    </span>

                </li>

            {% endif %}
        {% endif %}
    {% endfor %}

</ul>
