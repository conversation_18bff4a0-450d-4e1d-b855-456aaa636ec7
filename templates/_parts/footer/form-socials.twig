{% set request = craft.app.request %}
{% set redirectQuery = '?' in url(request.url) ? "&subscribed=true" : "?subscribed=true" %}
{% set subscribed = request.getParam('subscribed') %}

<nav class="flex flex-col gap-4 basis-1/3">

    {# Newsletter #}
    <span class="h4">{{ arr.formHeading }}</span>
    <span>{{ arr.formIntro }}</span>

    <div data-newsletter-form class="mb-10">
        <div class="fui-page">
            <form id="subscription-form"
                  name="subscribe"
                  class="customerio-form form-table js-form"
                  method="post"
                  action="{{ actionUrl('validators/recaptcha/submit-newsletter') }}">
                {{ csrfInput() }}
                <input type="hidden" name="redirect" value="{{ request.absoluteUrl }}{{ redirectQuery }}">

                <div class="footer-form fui-form-container flex">
                    <div class="fui-row">
                        <label>
                            <input class="fui-input py-4 px-6" type="email" placeholder="E-mailadres"
                                   name="email"
                                   pattern="\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+" required/>
                        </label>
                    </div>
                    <div class="fui-btn-wrapper fui-btn-right btn btn-primary btn-animation">
                        <button type="submit"
                                class="g-recaptcha fui-submit fui-btn"
                                data-sitekey="6LdjygArAAAAANQeSbUJeINHaC3OjHWSlGDQzZ6t"
                                data-callback="onSubmit"
                                data-size="invisible">
                            Aanmelden
                        </button>
                    </div>
                </div>
                {% if subscribed == "true" %}
                    <span id="customerio-subscribed" class="tag tag--primary mt-2 w-full">
                        {{ 'Thank you for your subscription' | t }}
                    </span>
                {% endif %}
            </form>
        </div>
    </div>

    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <script>
        function onSubmit() {
            document.getElementById("subscription-form").submit();
        }

        window.onload = () => {
            const customerioForm = document.querySelector('.customerio-form')
            const successMessage = customerioForm.querySelector('span#customerio-subscribed')

            setTimeout(() => {
                if (successMessage && "{{ subscribed }}" === "true") {
                    customerioForm.scrollIntoView()
                }
            }, 25)
        }
    </script>

    {# Socials #}
    <span class="h4">{{ arr.socialMediaHeading }}</span>

    <div class="flex flex-row flex-wrap gap-4 mb-10">

        {% for social in arr.footerSocials.all() %}

            <a class="hover:text-primary-500 transition-colors"
               href="{{ social.socialItemLink.getUrl() }}" {{ social.socialItemLink.getLinkAttributes() }}
               aria-label="{{ social.socialItemTitle }}">
                <span class="icon icon-{{ social.socialItemIcon }} text-32"></span>
            </a>

        {% endfor %}

    </div>

</nav>
