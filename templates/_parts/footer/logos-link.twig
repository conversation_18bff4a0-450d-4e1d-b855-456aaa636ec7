<div class="border-t border-tertiary-500/50 flex flex-col md:flex-row gap-4 justify-between">

    {# Logos #}
    <div class="flex flex-row flex-wrap gap-4 items-center mt-10">

        {% for images in arr.footerLogos %}
            {% set image = images.image.one() %}
            {% if (images.imageLink.getUrl()) %}
                <a href="{{ images.imageLink.getUrl() }}" {{ images.imageLink.getLinkAttributes() }} aria-label="{{ image.title }}">
            {% endif %}
            {% include '_parts/picture.twig' with {
                image: image,
                width: 80,
                loading: 'lazy',
                sizes: ['1x', '2x'],
                alt: image.alt
            } %}
            {% if (images.imageLink.getUrl()) %}
                </a>
            {% endif %}
        {% endfor %}

    </div>

    {# Link #}
    <div class="flex flex-row gap-4 items-center mt-9 md:mt-10">

        <a class="growing-underline growing-underline-primary"
           href="{{ arr.footerBarLink.getUrl() }}" {{ arr.footerBarLink.getLinkAttributes() }}>{{ arr.footerBarLink.getText() }}</a>

    </div>

</div>
