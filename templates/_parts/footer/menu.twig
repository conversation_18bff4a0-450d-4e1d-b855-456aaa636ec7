<nav class="flex flex-col gap-4 basis-1/5">

    {% if columnHeading %}
        <span class="h4">{{ columnHeading }}</span>
    {% endif %}

    <ul class="flex flex-col gap-y-4">
        {% for item in menuItems %}

            <li>
                <a class="growing-underline growing-underline-primary {% if not columnHeading and loop.first %} font-bold{% endif %}" href="{{ item.footerMenuLink.getUrl() }}" {{ item.footerMenuLink.getLinkAttributes() }}>{{ item.footerMenuLink.getText() }}</a>
            </li>

        {% endfor %}
    </ul>

</nav>
