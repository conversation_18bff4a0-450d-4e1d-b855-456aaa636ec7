{% block searchresult %}
    <li class="flex justify-between relative my-6 p-6 gap-x-6 bg-white rounded">
        {# content #}
        <div>
            <div class="text-22 md:h3 !text-black">{{ result.title }}</div>

            {% if result.seo.descriptionRaw is not empty %}
                <div class="mt-2 p-0 line-clamp-1 text-basic-black">{{ result.seo.descriptionRaw }}</div>
            {% endif %}

            {# link #}
            {% set link = result.url %}
            <a class="block mt-4 break-all text-secondary-500 underline cursor-pointer after:absolute after:inset-0 after:w-full after:h-full"
               href="{{ link }}">
                {{ link | replace("https://", "") }}
            </a>
        </div>

        {% include '_parts/icon.twig' with {
            icon: 'arrow-right',
            class: 'flex items-center'
        } %}
    </li>
{% endblock %}