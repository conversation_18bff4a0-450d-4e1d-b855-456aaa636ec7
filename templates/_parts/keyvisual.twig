{% set isDoorways = entry.keyVisualType == 'doorways' %}
{% set isMedia = entry.keyVisualType == 'media' %}
{% set isBasic = entry.keyVisualType == 'basic' %}

{% set keyVisualHideScrollText = false %}

{% if entry['keyVisualHideScrollText'] is defined %}
    {% set keyVisualHideScrollText = entry.keyVisualHideScrollText %}
{% endif %}

{% set words = entry.keyVisualHighlightedWords | split(' ') %}
{% set headingText = entry.keyVisualTitle | split(' ') %}
{% set heading = [] %}
{% for word in headingText %}
    {% if word in words %}
        {% set heading = heading | push('<span class="text-primary-500 font-bold">' ~ word ~ '</span>') %}
    {% else %}
        {% set heading = heading | push(word) %}
    {% endif %}
{% endfor %}
{% set heading = heading | join(' ') %}

{% if entry.keyVisualType == 'fullWidth' %}
    {% include '_parts/keyvisual/full-width' %}
{% else %}
    <div class="
        wrapper
        {{ entry.keyVisualCloseContentGap ? 'wrapper--no-bm' : 'wrapper--small-bm' }}
    ">
        <section class="keyvisual py-10 md-max:relative
            {% if entry.keyVisualType != 'media' %} lg:py-20 container container--padding{% else %} keyvisual__media text-image-element{% endif %}
            {% if entry.keyVisualAlignment == 'center' and isBasic %} container--small{% endif %}
            {% if entry.keyVisualType != 'doorways' %} keyvisual__basic{% endif %}"
        >

            {% if isDoorways %}
                <span class="keyvisual__graphic">
                    <img alt="" width="0" height="0" src="/dist/images/keyvisual/background.svg" />
                </span>
            {% endif %}

            <div class="flex flex-col lg:flex-row w-full justify-between lg:gap-y-16{% if isMedia %} items-center gap-x-16{% endif %}">

                <div class="w-full{% if isDoorways %} lg:w-[45%]{% endif %}{% if isMedia %} md:pt-16 md:pb-10{% endif %}">

                    {% if isMedia %}
                    <div class="2xl:ml-auto 2xl:max-w-[41rem]">
                    {% endif %}

                    <h1 class="relative -ml-[2px] md:-ml-1{% if isDoorways %} lg:text-64 lg:leading-74{% endif %}">{{ heading | raw }}</h1>

                    {% if entry.keyVisualIntro %}
                        <div class="keyvisual__text wysiwyg">{{ entry.keyVisualIntro }}</div>
                    {% endif %}

                    {% if not keyVisualHideScrollText %}
                        {% include '_parts/keyvisual/scroll-down.twig' with {
                            entry: entry
                        } %}
                    {% endif %}

                    {% if isMedia %}
                    </div>
                    {% endif %}

                </div>

                {% if isDoorways %}
                    {% if entry.keyVisualDoorways %}
                        {% include '_parts/keyvisual/doorways.twig' with {
                            doorways: entry.keyVisualDoorways.all()
                        } %}
                    {% endif %}
                {% endif %}

                {% if isMedia %}
                    {% if entry.keyVisualImage or entry.keyVisualVideo %}
                        {% include '_parts/keyvisual/media.twig' with {
                            block: entry
                        } %}
                    {% endif %}
                {% endif %}

            </div>

        </section>
    </div>

    {% if entry.keyVisualType != 'basic' and not keyVisualHideScrollText %}
        <a class="quick-links__anchor" id="main-content"></a>
    {% endif %}
{% endif %}
