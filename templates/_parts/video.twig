{#
    Usage:

    {% include "_parts/video.twig" with {
        class: "classname",
        src: "https://www.youtube.com/embed/xOwyciJgldo",
        fallbackImage: block.image.one()
    } %}
#}

{# set empty class variable when it's not defined #}
{% if class is not defined %}
    {% set class = '' %}
{% endif %}

{% set videoData = get_youtube_data_from_api(src) %}

{% if videoData.error is not defined and videoData.items is defined and videoData.items is not empty %}
    {% set videoData = videoData.items[0] %}

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "VideoObject",
            "name": "{{ videoData.snippet.title }}",
            "description": "{{ videoData.snippet.description }}",
            "thumbnailUrl": [
                "{{ videoData.snippet.thumbnails.default.url }}",
                "{{ videoData.snippet.thumbnails.standard.url ?? videoData.snippet.thumbnails.medium.url }}",
                "{{ videoData.snippet.thumbnails.maxres.url ?? videoData.snippet.thumbnails.high.url }}"
            ],
            "uploadDate": "{{ videoData.snippet.publishedAt }}",
            "duration": "{{ videoData.contentDetails.duration }}",
            "embedUrl": "{{ src }}",
            "interactionStatistic": {
                "@type": "InteractionCounter",
                "interactionType": { "@type": "WatchAction" },
                "userInteractionCount": {{ videoData.statistics.viewCount }}
            }
        }
    </script>
{% endif %}

<div class="video__wrapper {{ class }}" data-video-id="{{ src | replace("https://www.youtube.com/embed/", "") }}" data-block-id="{{ block.uid }}">
    <div id="{{ block.uid }}"></div>
    <div class="video__placeholder">
        <div class="video__placeholder-image-wrapper">
            {% include '_parts/picture.twig' with {
                class: 'video__placeholder-image',
                image: fallbackImage,
                width: 920,
                height: 518,
                loading: 'lazy',
                quality: 10
            } %}
        </div>
        <div class="video__placeholder-details">
            <div class="video__placeholder-play">
                {% include '_parts/icon.twig' with {
                    icon: 'play'
                } %}
            </div>
            <p class="video__placeholder-message">
                Accepteer de marketing cookies om deze video te kunnen bekijken.<br />
                Klik hier om je instellingen aan te passen
            </p>
        </div>
        <button class="video__placeholder-button" aria-label="Open cookie consent"></button>
    </div>
</div>
