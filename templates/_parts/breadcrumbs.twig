{% set isFullWidthKeyVisual = entry is defined and entry.keyVisualType is defined and entry.keyVisualType == 'fullWidth' %}{% set padding = padding is defined and padding is not null ? padding : true %}
{% set nonElementLinks = nonElementLinks ?? false %}
{% set breadcrumbLinks = [] %}
{# home #}
{% set home = craft.app.getElements().getElementByUri('__home__', currentSite.id) %}
{% set breadcrumbLinks = breadcrumbLinks | merge([{
    url: home.url ?? alias(currentSite.baseUrl),
    title: home.title ?? 'homepage' | t,
}]) %}

{# get elements #}
{% set segments = craft.app.request.segments %}
{% for segment in segments %}
    {% set uriPart = segments[0:loop.index] | join('/') | literal %}
    {% set element = craft.app.elements.getElementByUri(uriPart, currentSite.id) %}
    {% if element %}
        {% set breadcrumbLinks = breadcrumbLinks | merge([{
            url: element.url,
            title: element.title,
        }]) %}
    {% elseif nonElementLinks %}
        {% set breadcrumbLinks = breadcrumbLinks | merge([{
            url: url(uriPart),
            title: segment | t,
        }]) %}
    {% endif %}
{% endfor %}

{% if breadcrumbLinks | length > 1 %}
    <nav class="
        wrapper breadcrumbs text-16 leading-24 font-regular z-40
        {{ breadcrumbColor ? breadcrumbColor : isFullWidthKeyVisual ? 'text-white' : 'text-tertiary-900' }}
    ">

        <div class="container container--padding">

            {% if entry is defined and entry.keyVisualType == 'media' %}<div class="lg:w-1/2">{% endif %}

            <ol class="
                flex flex-row flex-wrap gap-y-2 my-4 relative isolate
                {{ isFullWidthKeyVisual ? '-mb-[100%]' : 'lg:-mb-[100%]' }}
            " itemscope itemtype="https://schema.org/BreadcrumbList">

                {% for breadcrumb in breadcrumbLinks %}
                    <li class="after:content-['/'] after:mx-4{{ loop.last ? ' after:hidden' : '' }}" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">

                        {% if loop.last == false %}
                            <a class="underline transition-colors underline-offset-4 decoration-from-font hover:text-primary-500" itemprop="item" href="{{ breadcrumb.url }}">
                        {% else %}
                            <span aria-current="page">
                        {% endif %}
                        <span itemprop="name">
                            {{ breadcrumb.title == 'Home' ? 'Milieu Service Nederland' : breadcrumb.title | raw }}
                        </span>
                        {% if loop.last == false %}
                            </a>
                        {% else %}
                            </span>
                        {% endif %}
                        <meta itemprop="position" content="{{ loop.index }}">
                    </li>
                {% endfor %}

            </ol>

            {% if entry is defined and entry.keyVisualType == 'media' %}</div>{% endif %}

        </div>

    </nav>
{% endif %}
