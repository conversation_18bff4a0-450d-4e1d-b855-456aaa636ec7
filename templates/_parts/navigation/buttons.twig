<div class="navigation__buttons {{ isMobile is defined and isMobile == true ? 'navigation__buttons--mobile' : '' }}">
        {% for button in buttons %}
            {% set link = button.linkItem.page %}
            {% set icon = button.linkItem.icon.label %}

            {% include '_parts/button.twig' with {
                link: link,
                type: button.buttonType,
                icon: icon,
                iconPosition: button.buttonIconPosition,
                class: 'navigation__button' ~ (isMobile is defined and isMobile == true ? ' navigation__button--center'),
            } %}
        {% endfor %}
</div>
