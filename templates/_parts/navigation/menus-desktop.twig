<div class="
    flex items-center
    nav-mobile:hidden
">
    <ul
        x-data="{
            openMenu: null
        }"
        class="flex gap-6 items-center text-base"
    >
        {% for link in links %}
            {% set cta = {
                page: link.entry.one ?? null,
                url: link.ctaUrl ?? '',
                title: link.ctaTitle ?? '',
                image: link.blockImage.one ?? null,
                newWindow: link.ctaNewWindow,
                isExternal: link.ctaExternal,
                enableOverlay: link.ctaEnableOverlay,
            } %}

            <li
                @mouseover="openMenu = {{ link.id }}"
                @mouseleave="openMenu = null"
                @focusin="openMenu = {{ link.id }}"
            >
                <a
                    href="{{ link.url }}"
                    class="
                        relative flex gap-1 items-center
                    "
                    :class="{
                        'text-primary-500 after:absolute after:w-full after:h-20 after:top-0 after:scale-x-120 after:z-20': openMenu == {{ link.id }}
                    }"
                >
                    {{ link.title }}
                    {% if link.children | length %}
                        {% include '_parts/icon.twig' with {
                            icon: 'chevron-down',
                            class: 'size-6 text-24 transition-transform duration-100',
                            iconAttributes: "
                                :class=\"{
                                    'rotate-180': openMenu == #{link.id}
                                }\"
                            "
                        } %}
                    {% endif %}
                    {% if link.newWindow %}
                        {% include '_parts/icon.twig' with {
                            icon: 'extern-down',
                            class: 'size-6 text-24 transition-transform duration-100',
                            iconAttributes: "
                                :class=\"{
                                    'rotate-180': openMenu == #{link.id}
                                }\"
                            "
                        } %}
                        <span class="sr-only">{{ 'opens in a new tab' | t }}</span>
                    {% endif %}
                </a>
                {% if link.children | length %}
                    <div
                        x-show="openMenu == {{ link.id }}"
                        x-transition:enter="transition ease-out duration-100"
                        x-transition:enter-start="opacity-0"
                        x-transition:enter-end="opacity-100"
                        x-transition:leave="transition ease-in duration-100"
                        x-transition:leave-start="opacity-100"
                        x-transition:leave-end="opacity-0"
                        class="
                            absolute w-full py-6 bg-white left-0 top-full z-10 border-t border-gray-100
                        "
                    >
                        <ul class="container container--padding flex gap-10 justify-start">
                            {% for child in link.children %}
                                <li class="w-[calc((100%-18rem)/4)]">
                                    <a
                                        href="{{ child.url }}"
                                        class="
                                            font-medium
                                            hover:text-primary-500
                                            focus-visible:text-primary-500
                                        "
                                    >
                                        {{ child.title }}
                                    </a>
                                    {% if child.children | length %}
                                        <ul class="flex flex-col items-start gap-2 mt-4">
                                            {% for child in child.children %}
                                                <li>
                                                    <a
                                                        href="{{ child.url }}"
                                                        class="
                                                            hover:text-primary-500
                                                            focus-visible:text-primary-500
                                                        "
                                                    >
                                                        {{ child.title }}
                                                    </a>
                                                </li>
                                            {% endfor %}
                                        </ul>
                                    {% endif %}
                                </li>
                            {% endfor %}
                            {% if cta.page or cta.url %}
                                <li class="
                                    group relative ml-auto shrink-0 rounded overflow-hidden size-[18rem] flex flex-col justify-end
                                    {{ cta.enableOverlay ? 'after:absolute after:w-full after:h-full after:top-0 after:left-0 after:bg-black/30 after:z-10' }}
                                ">
                                    {% include '_parts/picture' with {
                                        image: cta.image ?? cta.page.thumbnail.one,
                                        width: 432,
                                        height: 432,
                                        class: '
                                            absolute top-0 left-0 w-full h-full object-cover z-0 transition-transform duration-100
                                            group-hover:scale-110
                                            group-focus-within:scale-110
                                        ',
                                    } %}
                                    {% if cta.enableOverlay %}
                                        <div class="flex items-end justify-between gap-2 w-full z-20 text-white p-4 text-20 font-bold">
                                            {{ cta.title is defined and cta.title ? cta.title : cta.isExternal ? cta.url ?? '' : cta.page.title ?? '' }}
                                            {% include '_parts/icon' with {
                                                icon: 'arrow-right',
                                                class: 'size-6 text-24 text-24 mb-1.5 shrink-0',
                                            } %}
                                        </div>
                                    {% endif %}
                                    <a
                                        href="{{ cta.isExternal ? cta.url : cta.page.url }}"
                                        class="absolute top-0 left-0 w-full h-full z-30"
                                        aria-label="{{ cta.title is defined and cta.title ? cta.title : cta.isExternal ? cta.url ?? '' : cta.page.title ?? '' }}"
                                        {% if cta.newWindow %}
                                            target="_blank"
                                        {% endif %}
                                    ></a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                {% endif %}
            </li>
        {% endfor %}
    </ul>
</div>
