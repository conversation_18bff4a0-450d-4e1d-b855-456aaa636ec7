<ul class="links links--{{ orientation | default('vertical') }} {{ class | default('') }}">
{% for link in links %}
    {% set class = linkClass | default('') %}

    {% if link['class'] is defined and link['class'] %}
        {% set class = class ~ ' ' ~ link['class'] %}
    {% endif %}

    <li class="links__item relative">
        {% include '_parts/navigation/link.twig' with {
            link: link,
            class
        } %}
    </li>
{% endfor %}
</ul>
