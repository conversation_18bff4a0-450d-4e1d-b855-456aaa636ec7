{% set topLinks = craft.navigation.nodes().handle('topNavigation').level(1).with(['children.children']).asUsp(false).cache(60).all %}

{% set linkClasses = '
    py-6 w-full flex items-center justify-between gap-2
    hover:text-primary-500
    focus-visible:text-primary-500
' %}

<div
    x-data="{
        menuOpen: false,
        level2Open: null,
        level3Open: null,
        searchOpen: false,

        init() {
            this.$watch('menuOpen', menuOpen => {
                document.body.classList.toggle('scroll-lock', menuOpen)

                if (menuOpen) {
                    this.searchOpen = false
                }
            });
            this.$watch('searchOpen', searchOpen => {
                if (searchOpen) {
                    this.menuOpen = false
                    this.level2Open = null
                    this.level3Open = null
                    this.$nextTick(() => {
                        this.$refs.searchInput.focus()
                    })
                }
            });
        }
    }"
>
    <div class="flex flex-row gap-3 nav-desktop:hidden">
        {% include '_parts/navigation/mobile-search' %}
        <div class="sm-max:hidden">
            {% for button in buttons %}
                {% set link = button.linkItem.page %}
                {% set icon = button.linkItem.icon.label %}

                {% include '_parts/button' with {
                    link: link,
                    type: button.buttonType,
                    icon: icon,
                    iconPosition: button.buttonIconPosition,
                    class: 'navigation__button h-12',
                } %}
            {% endfor %}
        </div>
        <button
            class="navigation__toggle"
            :class="{
                'navigation__toggle--open': menuOpen
            }"
            aria-label="{{ 'Open and close mobile navigation' | t }}"
            @click="menuOpen = !menuOpen"
        >
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
    <div
        x-show="menuOpen"
        x-transition:enter="transition ease-out duration-100"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-100"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="
            absolute w-full top-full left-0 h-[calc(100dvh-4.5rem)] bg-white border-t border-gray-100 pt-4 flex flex-col overflow-auto text-base
            nav-desktop:hidden
        "
        :class="{
            'overflow-hidden': level2Open || level3Open
        }"
    >
        <div class="container--padding">
            {% include '_parts/search-form' with {
                id: 'nav-search',
                hideLabel: true,
                placeholder: 'Waar ben je naar op zoek?'
            } %}
        </div>
        <ul class="container--padding mt-4">
            {% for link in links %}
                {% set cta = {
                    page: link.entry.one ?? null,
                    url: link.ctaUrl ?? '',
                    title: link.ctaTitle ?? '',
                    image: link.blockImage.one ?? null,
                    newWindow: link.ctaNewWindow,
                    isExternal: link.ctaExternal,
                    enableOverlay: link.ctaEnableOverlay,
                } %}

                <li class="
                    w-full border-b border-gray-100
                    last:border-b-0
                ">
                    {% if not link.children | length %}
                        <a
                            href="{{ link.url }}"
                            class="{{ linkClasses }}"
                        >
                            {{ link.title }}
                        </a>
                    {% else %}
                        <button
                            class="{{ linkClasses }}"
                            @click="level2Open = {{ link.id }}"
                        >
                            {{ link.title }}
                            {% include '_parts/icon' with {
                                icon: 'chevron-right',
                                class: 'size-6 text-24 shrink-0',
                            } %}
                        </button>
                        {% include '_parts/navigation/submenu-mobile' with {
                            link,
                            cta,
                            level: 2
                        } %}
                    {% endif %}
                </li>
            {% endfor %}
        </ul>
        {% if buttons | length %}
            <div class="
                mt-auto w-full pt-4 container--padding pb-8
                md:hidden
            ">
                {% for button in buttons %}
                    {% set link = button.linkItem.page %}
                    {% set icon = button.linkItem.icon.label %}

                    {% include '_parts/button' with {
                        link: link,
                        type: button.buttonType,
                        icon: icon,
                        iconPosition: button.buttonIconPosition,
                        class: 'navigation__button w-full text-center justify-center h-12',
                    } %}
                {% endfor %}
            </div>
        {% endif %}
        {% if topLinks | length %}
            <ul class="
                container--padding py-2 bg-primary-100
                {{ not buttons | length ? 'mt-auto' : 'md:mt-auto' }}
            ">
                {% for link in topLinks %}
                    <li class="
                        w-full border-b border-primary-500
                        last:border-b-0
                    ">
                        {% if not link.children | length %}
                            <a
                                href="{{ link.url }}"
                                class="{{ linkClasses }} !justify-start"
                            >
                                {% if link.icon is defined and link.icon.label %}
                                    {% include '_parts/icon' with {
                                        icon: link.icon.label,
                                        class: 'size-6 text-24 shrink-0',
                                    } %}
                                {% endif %}
                                {{ link.title }}
                            </a>
                        {% else %}
                            <button
                                class="{{ linkClasses }} !justify-start"
                                @click="level2Open = {{ link.id }}"
                            >
                                {% if link.icon is defined and link.icon.label %}
                                    {% include '_parts/icon' with {
                                        icon: link.icon.label,
                                        class: 'size-6 text-24 shrink-0',
                                    } %}
                                {% endif %}
                                {{ link.title }}
                                {% include '_parts/icon' with {
                                    icon: 'chevron-right',
                                    class: 'size-6 text-24 shrink-0 ml-auto',
                                } %}
                            </button>
                            {% include '_parts/navigation/submenu-mobile' with {
                                link,
                                cta: null,
                                level: 2
                            } %}
                        {% endif %}
                    </li>
                {% endfor %}
            </ul>
        {% endif %}
    </div>
</div>
