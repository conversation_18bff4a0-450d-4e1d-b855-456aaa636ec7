{% set label = link.title %}
<div class="
    group/link-wrapper relative
    {{ link.children | length ? 'after:absolute after:w-full after:h-4 after:top-full after:left-0 after:z-10' }}
">
    <a
        class="
            link group
            {{ class | default('') }}
        "
        {% if link.newWindow %}target="_blank"{% endif %}
        href="{{ link.url }}"
    >
    {% if link.icon is defined and link.icon is iterable and link.icon.label is defined and link.icon.label != null %}
        {% include '_parts/icon.twig' with {
            icon: link.icon.label,
            class: 'link__icon'
        } %}
    {% endif %}
        <span class="
            group-hover:underline
            group-focus-visible:underline
        ">{{ link.title }}</span>
    </a>
    {% if link.children | length %}
        {% set childLinks = link.children | filter(link => not link.buttonVariant or link.buttonVariant == 'none') %}
        {% set childButtons = link.children | filter(link => link.buttonVariant != 'none') %}
        <div class="
            fixed left-0 top-12 w-full h-[calc(100dvh-3rem)] z-10 bg-black/30 opacity-0 transition-opacity pointer-events-none duration-100
            group-hover/link-wrapper:opacity-100
            group-focus-within/link-wrapper:opacity-100
        "></div>
        <ul class="
            absolute left-1/2 top-[calc(100%+1rem)] w-[17rem] -translate-x-1/2 rounded p-4 bg-white z-20 flex flex-col opacity-0 transition-opacity duration-100 pointer-events-none
            group-hover/link-wrapper:opacity-100 group-hover/link-wrapper:pointer-events-auto
            group-focus-within/link-wrapper:opacity-100 group-focus-within/link-wrapper:pointer-events-auto
        ">
            {% for link in childLinks %}
                <li class="mb-4">
                    <a
                        class="
                            text-20 w-full flex items-center gap-2
                            hover:text-primary-500
                            focus-visible:text-primary-500
                        "
                        href="{{ link.url }}"
                    >
                        {% if link.icon is defined and link.icon.label %}
                            {% include '_parts/icon' with {
                                icon: link.icon.label,
                                class: 'size-6 text-24 shrink-0',
                            } %}
                        {% endif %}
                        {{ link.title }}
                        {% include '_parts/icon' with {
                            icon: 'chevron-right',
                            class: 'size-6 text-24 shrink-0 text-primary-500 ml-auto',
                        } %}
                    </a>
                </li>
            {% endfor %}
            {% if childButtons | length and childLinks | length %}
                <li class="mb-4">
                    <hr class="border-gray-100" />
                </li>
            {% endif %}
            {% for link in childButtons %}
                <li>
                    <a
                        class="
                        group w-full text-center btn items-center !flex justify-center gap-2
                        btn-{{ link.buttonVariant }}
                        "
                        href="{{ link.url }}"
                    >
                        <span class="
                            group-hover:underline
                            group-focus-visible:underline
                        ">{{ link.title }}</span>
                        {% if link.icon is defined and link.icon.label %}
                            {% include '_parts/icon' with {
                                icon: link.icon.label,
                                class: '!inline size-5 text-20 shrink-0',
                            } %}
                        {% endif %}
                    </a>
                </li>
            {% endfor %}
        </ul>
    {% endif %}
</div>
