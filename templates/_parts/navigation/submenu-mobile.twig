{% set linkClasses = '
    py-6 w-full flex items-center justify-between gap-2
    hover:text-primary-500
    focus-visible:text-primary-500
' %}
{% set childLinks = link.children | filter(link => not link.buttonVariant or link.buttonVariant == 'none') %}
{% set childButtons = link.children | filter(link => link.buttonVariant is defined and link.buttonVariant and link.buttonVariant != 'none') %}

<ul
    class="flex flex-col bg-white fixed left-0 top-[4.5rem] w-full h-[calc(100dvh-4.5rem)] z-40 py-6 container--padding pb-8 overflow-auto border-t border-gray-100"
    x-show="level{{ level }}Open == {{ link.id }}"
    x-transition:enter="transition ease-out duration-100"
    x-transition:enter-start="translate-x-full"
    x-transition:enter-end="translate-x-0"
    x-transition:leave="transition ease-in duration-100"
    x-transition:leave-start="translate-x-0"
    x-transition:leave-end="translate-x-full"
>
    <li>
        <button
            class="w-full py-2 flex gap-4 items-center"
            @click="level{{ level }}Open = null"
        >
            {% include '_parts/icon.twig' with {
                icon: 'chevron-left',
                class: 'size-6 text-24 shrink-0 -rotate-90',
            } %}
            {{ 'Back' | t }}
        </button>
    </li>
    <li class="
        w-full border-b border-gray-100
        last:border-b-0
    ">
        <a
            href="{{ link.url }}"
            class="{{ linkClasses }} !font-medium"
        >
            {{ link.title }}
        </a>
    </li>
    {% for link in childLinks %}
        <li class="
            w-full
            {{ not loop.last ? 'border-b border-gray-100' }}
        ">
            {% if not link.children | length %}
                <a
                    class="
                        !justify-start
                        {{ linkClasses }}
                    "
                    href="{{ link.url }}"
                >
                    {% if link.icon is defined and link.icon and link.icon.label %}
                        {% include '_parts/icon' with {
                            icon: link.icon.label,
                            class: 'size-6 text-24 shrink-0',
                        } %}
                    {% endif %}
                    {{ link.title }}
                </a>
            {% else %}
                <button
                    class="{{ linkClasses }}"
                    @click="level3Open = {{ link.id }}"
                >
                    {{ link.title }}
                    {% include '_parts/icon' with {
                        icon: 'chevron-right',
                        class: 'size-6 text-24 shrink-0',
                    } %}
                </button>
                {% include '_parts/navigation/submenu-mobile' with {
                    link,
                    cta: null,
                    level: level + 1
                } %}
            {% endif %}
        </li>
    {% endfor %}
    {% for link in childButtons %}
        <li class="
            w-full
            {{ loop.first ? 'mt-auto' : 'mt-2' }}
            {{ not loop.last ? 'border-b border-gray-100' }}
        ">
            <a
                class="
                group w-full text-center btn items-center !flex justify-center gap-2
                btn-{{ link.buttonVariant }}
                "
                href="{{ link.url }}"
            >
                <span class="
                    group-hover:underline
                    group-focus-visible:underline
                ">{{ link.title }}</span>
                {% if link.icon is defined and link.icon and link.icon.label %}
                    {% include '_parts/icon' with {
                        icon: link.icon.label,
                        class: '!inline size-5 text-20 shrink-0',
                    } %}
                {% endif %}
            </a>
        </li>
    {% endfor %}
    {% if cta and (cta.page or cta.url) %}
        <li class="mt-auto pt-8">
            <div class="
                group relative rounded overflow-hidden size-[18rem] flex flex-col justify-end mx-auto
                {{ cta.enableOverlay ? 'after:absolute after:w-full after:h-full after:top-0 after:left-0 after:bg-black/30 after:z-10' }}
            ">
                {% include '_parts/picture' with {
                    image: cta.image ?? cta.page.thumbnail.one,
                    width: 432,
                    height: 432,
                    class: '
                        absolute top-0 left-0 w-full h-full object-cover z-0 transition-transform duration-100
                        group-hover:scale-110
                        group-focus-within:scale-110
                    ',
                } %}
                {% if cta.enableOverlay %}
                    <div class="flex items-end justify-between gap-2 w-full z-20 text-white p-4 text-20 font-bold">
                        {{ cta.title is defined and cta.title ? cta.title : cta.isExternal ? cta.url ?? '' : cta.page.title ?? '' }}
                        {% include '_parts/icon' with {
                            icon: 'arrow-right',
                            class: 'size-6 text-24 text-24 mb-1.5 shrink-0',
                        } %}
                    </div>
                {% endif %}
                <a
                    href="{{ cta.isExternal ? cta.url : cta.page.url }}"
                    class="absolute top-0 left-0 w-full h-full z-30"
                    aria-label="{{ cta.title is defined and cta.title ? cta.title : cta.isExternal ? cta.url ?? '' : cta.page.title ?? '' }}"
                    {% if cta.newWindow %}
                        target="_blank"
                    {% endif %}
                ></a>
            </div>
        </li>
    {% endif %}
</ul>
