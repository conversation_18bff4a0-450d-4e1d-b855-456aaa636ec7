<button
    class="
        navigation__search-toggle navigation__search-toggle--mobile
    "
    :aria-expanded="searchOpen ? 'true' : 'false'"
    aria-controls="nav-search-mobile"
    aria-label="Zoekbalk"
    @click="searchOpen = !searchOpen"
>
    {% include '_parts/icon' with {
        icon: 'search',
        iconAttributes: 'x-show="!searchOpen"'
    } %}
    {% include '_parts/icon' with {
        icon: 'close',
        iconAttributes: 'x-show="searchOpen"'
    } %}
    <span class="sr-only">Open zoekveld</span>
</button>
{% include '_parts/search-form' with {
    id: 'nav-search-mobile',
    class: 'absolute top-full left-0 w-screen bg-white !rounded-t-none !border-x-0 !border-b-0 !border-gray-100',
    hideLabel: true,
    placeholder: 'Waar ben je naar op zoek?',
    attributes: '
        x-show="searchOpen"
        x-transition:enter="transition ease-out duration-100"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-100"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
    ',
    inputClass: 'text-base',
    inputAttributes: '
        x-ref="searchInput"
    '
} %}
