{% set urlParam = urlParam | default('subscribed') %}
{% set implicitAccept = implicitAccept ?? true %}
{% set request = craft.app.request %}
{% set redirectQuery = '?' in url(request.url) ? "&#{urlParam}=true" : "?#{urlParam}=true" %}
{% set subscribed = craft.app.request.getParam(urlParam) %}

{% macro formField(label, name, isEmail=false) %}
    <div class="flex flex-col items-start grow text-teriary-900">
        <label for="{{ name }}" class="text-14 leading-16 mb-2 opacity-90">{{ label }}</label>
        <input
            class="
                text-16 leading-24 w-full p-2 rounded border border-tertiary-500
                focus:border-primary-500 focus:outline-none
            "
            type="{{ isEmail ? 'email' : 'text' }}"
            id="{{ name }}"
            name="{{ name }}"
            required
            {% if isEmail %}pattern="\w+([\.-]?\w+)@\w+([\.-]?\w+)(\.\w{2,3})+"{% endif %}
        />
    </div>
{% endmacro %}

<form id="subscription-form"
      class="form__wrapper-simple customerio-form {{ class | default('') }}"
      name="subscribe"
      method="post"
      action="{{ actionUrl('validators/recaptcha/submit-newsletter') }}">
    {{ csrfInput() }}
    <input type="hidden" name="redirect" value="{{ request.absoluteUrl ~ redirectQuery }}">
    {% if implicitAccept %}
        <input type="hidden" name="accept_terms" value="Ja, niet bevestigd">
    {% endif %}
    <input type="hidden" name="form_source" value="{{ formSource | default('Aanmeldformulier') }}">

    {% if subscribed == "true" %}
        <span id="customerio-subscribed" class="tag tag--primary mb-4 w-full">
            {{ successMessage | default('Thank you for your subscription' | t) }}
        </span>
    {% endif %}
    <div class="flex flex-col">
        <div class="
            flex flex-col gap-4 mb-4
            lg:mb-6 lg:flex-row lg:gap-8
        ">
            {{ _self.formField('Voornaam*', 'firstname') }}
            {{ _self.formField('Achternaam*', 'lastname') }}
        </div>
        {{ _self.formField('Email*', 'email', true) }}
        <div>
            {% if not implicitAccept %}
                <div class="
                    flex items-baseline gap-2 mt-4
                    lg:mt-6
                ">
                    <input
                        class="
                            peer accent-primary-500 border-tertiary-500 border appearance-none w-4 h-4 rounded translate-y-[0.125rem] shrink-0
                            focus:border-primary-500 focus:outline-none
                        "
                        type="checkbox"
                        id="accept_terms"
                        name="accept_terms"
                        value="Ja, bevestigd"
                        required
                    >
                    <label
                        for="accept_terms"
                        class="
                            relative text-14 leading-16 text-tertiary-900/90
                            before:absolute before:left-[-1.325rem] before:top-[0.125rem] before:w-[0.625rem] before:h-[0.625rem] before:rounded-sm before:bg-primary-500 before:opacity-0
                            peer-checked:before:opacity-100
                        "
                    >
                        {{ acceptTerms | default('I subscribe to the newsletter' | t) }}*
                    </label>
                </div>
            {% endif %}
            <div class="fui-btn-wrapper fui-btn-right btn btn-primary btn-animation">
                <button type="submit"
                        class="g-recaptcha fui-submit fui-btn"
                        data-sitekey="6LdjygArAAAAANQeSbUJeINHaC3OjHWSlGDQzZ6t"
                        data-callback="onSubmit"
                        data-size="invisible">
                    {{ buttonLabel | default('Submit' | t) }}
                </button>
            </div>
        </div>
    </div>
</form>

<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<script>
    function onSubmit() {
        document.getElementById("subscription-form").submit();
    }

    setTimeout(() => {
        const customerioForm = document.querySelector('.customerio-form')
        const successMessage = customerioForm.querySelector('span#customerio-subscribed')

        if (successMessage && "{{ subscribed }}" === "true") {
            customerioForm.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
            })
        }
    }, 25)
</script>
