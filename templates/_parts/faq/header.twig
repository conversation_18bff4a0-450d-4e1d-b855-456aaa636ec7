{% set faqQuickLinks = entry.faqQuickLinks.all() %}

<header class="
    relative flex justify-center pt-[4.75rem] pb-6 overflow-hidden mb-5
    md:pb-10
    lg:pb-[3.675rem]
    [&_+_.c-block]:!mt-12
    [&_+_.quick-links\_\_anchor_+_.c-block]:!mt-12
    [&_+_script_+_.c-block]:!mt-12
">
    <div class="flex items-center text-center flex-col px-6 max-w-[54.5rem]">
        <h1 class="mb-2">{{ entry.title }}</h1>
        {{ entry.description }}
        {% if faqQuickLinks and faqQuickLinks | length %}
            <div class="mt-6 gap-4 w-full grid grid-cols-2 md:flex flex-wrap justify-center">

                {% for link in faqQuickLinks %}
                    {% include "_parts/keyvisual/quick-link" with {
                        link: '#' ~ link.target | kebab,
                        icon: (link.icon and link.icon is iterable and link.icon.label) ? link.icon.label : null,
                        label: link.label
                    } %}
                {% endfor %}
            </div>
        {% endif %}
    </div>
    <span class="
        w-full absolute left-1/2 top-0 -translate-x-1/2 -z-1 scale-150
        lg:w-2/3 lg:-translate-y-1/3
    ">
        <img class="block aspect-square w-full blur-[105px] motion-safe:animate-spin-slow" alt="" width="0" height="0" src="/dist/images/keyvisual/background.svg" />
    </span>
</header>
