{% set image = block.image.one() %}
{% set smallImage = block.smallImage %}
{% set imageLeft = block.layout == 'twoColMediaLeft' %}

<div class="
    {{ smallImage ? 'px-6 relative' : '
        w-full
        lg:w-1/2
    ' }}
    {{ smallImage and imageLeft ? 'flex justify-end lg:pl-14 lg:pr-0' }}
    {{ smallImage and not imageLeft ? 'lg:pr-14 lg:pl-0' }}
">
    {% if block.video %}
        {% include '_parts/video.twig' with {
            src: block.video,
            fallbackImage: image
        } %}
    {% else %}
        {% include '_parts/picture.twig' with {
            class: "
                block rounded object-cover w-full
                #{smallImage ? 'aspect-[432/267] lg:max-w-[27rem]' : 'aspect-square'}
            ",
            image: image,
            loading: 'lazy',
            width: smallImage ? 432 : 900,
            height: smallImage ? 267 : 900,
            sizes: ['0.25x', '0.5x', '1x', '1.5x']
        } %}
    {% endif %}
</div>
