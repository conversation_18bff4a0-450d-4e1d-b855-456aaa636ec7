{# heading tag #}
{% if block.headingTag is not defined %}
    {% set headingTag = '2' %}
{% else %}
    {% set headingTag = block.headingTag %}
{% endif %}

<div class="
    w-full px-6
    lg:w-1/2
    {{ block.layout == 'twoColMediaLeft' ? '
        pt-12
        md:pr-14
        lg:pl-24 lg:pt-0
    ' : '
        pb-12
        md:pr-24
        lg:md:pl-14 lg:pb-0
    ' }}">
    <div>
        {# tag #}
        {% if block.tag is not empty %}
            <div class="tag tag--primary inline-block mb-6">
                {{ block.tag }}
            </div>
        {% endif %}

        {# title #}
        {% if block.heading is defined %}
            {% include '_parts/block-title' with {
                title: block.heading,
            } %}
        {% endif %}

        {# text #}
        {% if block.text is defined %}
            <div class="wysiwyg">
                {{ block.text }}
            </div>
        {% endif %}

        {# button #}
        {% if not block.buttonLink.isEmpty %}
            {% include '_parts/button.twig' with {
                component: 'a',
                link: block.buttonLink,
                type: block.buttonType,
                linkNoFollow: block.linkNoFollow,
                class: 'text-image-element__button',
            } %}
        {% endif %}
    </div>
</div>
