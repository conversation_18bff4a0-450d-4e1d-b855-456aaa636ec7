<header class="
    bg-basic-lightgray pt-4 pb-6
    [&_+_.c-block]:!mt-12
    [&_+_.quick-links\_\_anchor_+_.c-block]:!mt-12
    [&_+_script_+_.c-block]:!mt-12
">
    <div class="container px-6 grid grid-cols-1 md:grid-cols-12 gap-6">
        <div class="flex gap-6 flex-col lg:gap-8 lg:flex-row col-span-full md:col-span-6 lg:col-span-5 lg:col-start-2">
            <div class="relative flex flex-col items-start justify-center grow">
                <div class="hidden md:block absolute top-0 h-6">
                    {% include '_parts/breadcrumbs.twig' with {
                        breadcrumbColor: breadcrumbColor ?? false,
                        padding: false
                    } %}
                </div>
                <div class="grow flex flex-col justify-center py-10">
                    <div class="flex gap-2 flex-col">
                        <h1 class="h2">{{ entry.title }}</h1>
                        {% if entry.jobTitle %}
                            <p class="text-primary-700 font-medium">{{ entry.jobTitle }}</p>
                        {% endif %}
                    </div>
                    <div class="flex flex-wrap gap-4 mt-8">
                        {% if entry.phone %}
                            <a href="{{ entry.phone }}" class="inline-flex items-center gap-2 px-4 py-2 bg-primary-100 rounded-md hover:primary hover:bg-primary-300 transition-colors">
                                <span class="inline-flex items-center justify-center">
                                    {% include "_parts/icon" with {
                                        icon: 'phone'
                                    } %}
                                </span>
                                <span>{{ entry.phone | slice(4) }}</span>
                            </a>
                        {% endif %}

                        {% if entry.linkedin %}
                            <a href="{{ entry.linkedin }}" target="_blank" rel="noopener noreferrer" class="inline-flex items-center gap-2 px-4 py-2 bg-primary-100 rounded-md hover:bg-primary-300 transition-colors">
                                <span class="inline-flex items-center justify-center">
                                    {% include "_parts/icon" with {
                                        icon: 'linkedin'
                                    } %}
                                </span>
                                <span>LinkedIn</span>
                            </a>
                        {% endif %}
                    </div>
                    {% if entry.richText %}
                        <div class="mt-4 wysiwyg *:last:mb-0">
                            {{ entry.richText }}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-span-full md:col-span-4 place-content-center md:col-start-9 lg:col-start-8 pt-2">
            {% if entry.authorImage and entry.authorImage.one() %}
                {% include '_parts/picture.twig' with {
                    image: entry.authorImage.one(),
                    width: 380,
                    height: 430,
                    class: 'rounded w-full h-full rounded aspect-[327/295] object-cover md:aspect-[421/380]',
                    attributes: entry.authorImage.one().focalPoint ? "style=\"object-position: #{entry.authorImage.one().focalPoint.x * 100}% #{entry.authorImage.one().focalPoint.y * 100}%;\"" : '',
                    loading: 'lazy'
                } %}
            {% endif %}
        </div>
    </div>
</header>
