{% set articles = craft.entries.section('article').articleAuthor(entry.id).limit(6).all() %}

{% if articles | length %}
    <section class="container wrapper c-block c-block--bm-md container--padding flex flex-col gap-6" aria-labelledby="articles-from-expert-heading">
        <h2>
            {{ 'Articles from {expert}' | t({expert: entry.firstName ?? entry.title}) }}
        </h2>

        <ul class="grid grid-cols-[repeat(auto-fill,minmax(23rem,1fr))] gap-8">
            {% for doorwayItem in articles %}
                <li class="[&>a]:h-full">
                    {% include '_parts/article-doorway.twig' with {
                        entry: doorwayItem,
                        excludeAuthor: true,
                    } %}
                </li>
            {% endfor %}
        </ul>
    </section>
{% endif %}
