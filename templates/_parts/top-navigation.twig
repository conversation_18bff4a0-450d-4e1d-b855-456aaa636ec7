{% set topNavLinks = craft.navigation.nodes().handle('topNavigation').level(1).cache(60).all %}
{% set uspLinks = topNavLinks | filter(link => link.asUsp) %}
{% set topLinks = topNavLinks | filter(link => not link.asUsp) %}

<div class="top-navigation">
    <div class="top-navigation__inner container container--padding {{ not uspLinks | length ? 'top-navigation__inner--end' }}">
        {% if uspLinks | length %}
            {% for link in uspLinks %}
                {% include '_parts/navigation/link' with {
                    link,
                    class: 'link--small link--green-icon'
                } %}
            {% endfor %}
        {% endif %}
        <div class="top-navigation__right">
            {% include '_parts/navigation/link-repeater' with {
                orientation: orientation | default('horizontal'),
                links: topLinks,
                class: 'top-navigation__links',
                linkClass: 'top-navigation__link'
            } %}
        </div>
    </div>
</div>
