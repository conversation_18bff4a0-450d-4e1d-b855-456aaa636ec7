{% set thumbnail = doorway.itemImage.one() | default() %}

<a class="doorway {{ class }}" {{ doorway.itemLink.getLinkAttributes() }}
    aria-label="{{ doorway.itemTitle }}"
    {% if doorway.linkNoFollow ? 'nofollow' %} rel="nofollow" {% endif %}>
    {% if thumbnail %}
        {% include '_parts/picture.twig' with {
            class: 'doorway__thumbnail',
            loading: 'lazy',
            image: thumbnail,
            width: 160,
            height: 160,
        } %}
    {% endif %}
    <div class="doorway__content">
        <span class="doorway__title">{{ doorway.itemTitle }}</span>
        <p class="doorway__description">{{ doorway.itemDescription }}</p>
    </div>
    {% include '_parts/icon.twig' with {
        icon: 'chevron-right',
        class: 'doorway__icon'
    } %}
</a>