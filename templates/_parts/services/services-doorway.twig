{% set thumbnail = block.serviceImage.one() | default() %}

<div class="services__doorway {{ thumbnail ? 'services__doorway--with-image' }}">
    {% if thumbnail %}
        {% include '_parts/picture.twig' with {
            class: 'services__doorway-image',
            loading: 'lazy',
            image: thumbnail,
            width: 600,
            quality: 20
        } %}
    {% endif %}
    <div class="services__doorway-details">
        <span class="services__doorway-title h3">{{ block.serviceTitle }}</span>
        {% if block.serviceSubtitle is not empty %}
            <span class="services__doorway-subtitle">{{ block.serviceSubtitle }}</span>
        {% endif %}
    </div>
    {% include '_parts/icon.twig' with {
        icon: 'arrow-right',
        class: 'services__doorway-icon'
    } %}
    <a {{ block.serviceLink.getLinkAttributes() }} class="services__doorway-link" aria-label="{{ block.serviceTitle }}"></a>
</div>