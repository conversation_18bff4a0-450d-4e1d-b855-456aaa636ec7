{% do eagerLoadElements(entry, [
    'keyVisualDoorways',
    'keyVisualSliderImages'
]) %}

{% set doorways = entry.keyVisualDoorways %}
{% set images = entry.keyVisualSliderImages is defined ? entry.keyVisualSliderImages : [] %}

<div class="wrapper w-screen {{ entry.primaryBackground ? 'bg-primary-50 relative after:content-[\'\'] after:absolute after:top-full after:w-full after:bg-primary-50 after:h-12 md:after:h-20 lg:after:h-32 after:-z-1' }} {{ entry.keyVisualCloseContentGap ? 'wrapper--small-bm after:!h-6' }}">
    <div class="relative w-full h-[min(633px,80vh)] overflow-hidden {{ images | length > 1 ? 'home-slider' }}">
        <div class="absolute w-full h-full swiper-wrapper">
            {% for image in images %}
                {% include '_parts/picture.twig' with {
                    image: image,
                    width: 1920,
                    height: 1080,
                    class: 'absolute w-full h-full object-cover swiper-slide'
                } %}
            {% endfor %}
        </div>
        <div class="absolute z-2 top-0 left-0 flex flex-col items-center justify-center py-10 text-center text-white w-full h-full bg-black/50">
            <h1 class="text-inherit w-[min(calc(100vw-3rem),60rem)]">{{ entry.keyVisualTitle }}</h1>
            {% if entry.keyVisualIntro %}
                <div class="
                    mt-6 wysiwyg w-[min(calc(100vw-3rem),60rem)] text-inherit
                    [&_>_*]:!text-inherit
                ">
                    {{ entry.keyVisualIntro }}
                </div>
            {% endif %}
            {% include '_parts/keyvisual/button' %}
        </div>
    </div>
    <div class="
        container container--padding
        relative z-2 -mt-20
    ">
        {% include '_parts/keyvisual/doorways.twig' with {
            doorways: doorways,
            listClass: '
                flex gap-4
                md-max:flex-col
                lg:gap-8 lg:items-center lg:justify-center
            ',
            homeDoorways: true,
        } %}
    </div>
</div>
