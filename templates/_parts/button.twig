{# Options
    component: 'button' or 'a' | default: 'a'
    link: craft link object | only add if component is 'a'
    type: 'primary' or 'secondary' | default: 'primary'
    icon: string | icomoon icon name
    iconPosition: '' or 'left' or 'right' | determines icon location. Required when using icon
    label: string | overwrites link.customText or entry.title if component is 'a'
    class: string
#}

{% set component = component | default('a') %}
{% set isLink = component == 'a' %}
{% set iconPresent = icon is defined and icon != null %}

{% if label is defined %}
    {% set label = label %}
{% elseif isLink %}
    {% set href = link.linkedUrl ? link.linkedUrl : link.linkedId %}
    {% set label = link.customText %}

    {% if href matches '/^\\d+$/' and link.type == 'entry' and label is empty %}
        {% set entry = craft.entries.id(href).one() %}
        {% set label = entry.title %}
    {% endif %}
{% else %}
    {% set label = '' %}
{% endif %}

<{{ component }}
    {{ isLink ? link.getLinkAttributes() }}
    {{ isLink and linkNoFollow is defined and linkNoFollow ? 'rel="nofollow"' }}
    class="btn btn-{{ type | default('primary') }} btn-animation {{ iconPresent and iconPosition ? ('has-icon-' ~ iconPosition) : '' }} {{ class | default('') }}"
    {{ disabled | default(false) ? ' disabled' : '' }}
    {% if not isLink and (name is defined or label is defined) %}aria-label="{{ name is defined ? name : (label | raw) }}"{% endif %}
    data-next-step-button
>
    {% if iconPresent and iconPosition == 'left' %}
        {% include '_parts/icon.twig' with {
            icon: icon
        } %}
    {% endif %}
    <span>{{ label is defined ? (label | raw) : '' }}</span>
    {% if iconPresent and iconPosition == 'right' %}
        {% include '_parts/icon.twig' with {
            icon: icon,
            class: ''
        } %}
    {% endif %}
</{{ component }}>
