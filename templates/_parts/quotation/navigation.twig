{% set uspSimple = entry.uspItemSimple %}

<nav class="quotation__nav {{ class | default('') }}" aria-label="Hoofdnavigatie">
    <a class="quotation__logo mt-6 lg:mt-0" href="{{ siteUrl }}" aria-label="Home pagina">
        <img src="/dist/images/logos/logo-horizontal.svg" height="56" alt="Milieu Service Nederland" />
    </a>
    {% if entry.headerTag is not empty %}
        <div class="usp-ticker-wrapper flex lg:hidden">
            <ul class="usp-ticker">
                {% for usp in uspSimple %}
                    <li class="usp-ticker__item tag tag--primary">
                        {% include '_parts/icon.twig' with {
                            icon: 'check',
                            class: 'text-28 text-primary-100'
                        } %}
                        {{ usp.uspItemSimpleTitle }}
                    </li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}
</nav>
