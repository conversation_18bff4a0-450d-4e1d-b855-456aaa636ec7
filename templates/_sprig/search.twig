{# set variable for sprig to hook into with default for first page load #}
{% set page = page ?? 1 %}
{% set LIMIT_PER_PAGE = 12 %}

{# need above values in hidden inputs for persistent state  #}
{{ hiddenInput('page', page) }}

{% set queryParam = craft.app.request.getParam('q') %}
{% set entryQuery = craft.entries().search(queryParam).includeInSearchResults(1).uri(':notempty:').orderBy('score').limit(LIMIT_PER_PAGE) %}

{% set pageInfo = sprig.paginate(entryQuery, page) %}
{% set results = pageInfo.pageResults %}

{# search result #}
<div class="container container--padding max-w-6xl">

    {# title #}
    <h1 class="xl:text-8xl mt-10 lg:mt-20 mb-8 lg:mb-16">
        {% if queryParam is not defined or queryParam is empty %}
            V<PERSON><PERSON> hier onder je zoekopdracht in
        {% else %}
            {{ 'you searched for' | t | capitalize }} ‘{{ queryParam }}’
        {% endif %}
    </h1>

    {% include "_parts/search-form.twig" with {
        class: (queryParam is empty ? 'mb-6' : ''),
        value: queryParam
    } %}

    {# if results #}
    {% if queryParam is not empty %}
        {% if results | length %}
            <div class="my-6">
                <h4 class="text-tertiary-700">{{ pageInfo.total }} {{ 'results found' | t }}</h4>
                <ul class="card-list card-list--tertiary">

                    {% for result in results %}
                        {% include "_parts/search/result.twig" with {
                            "entry": result
                        } %}
                    {% endfor %}
                </ul>
            </div>

            {# if no results #}
        {% else %}
            <div class="bg-white p-6 my-6 rounded">
                <h4>{{ 'no results found' | t | capitalize }}</h4>
            </div>
        {% endif %}
    {% endif %}

</div>

{% if results | length and queryParam is not empty %}
    <script>
        document.body.addEventListener('htmx:load', function (evt) {
            htmx.on('htmx:afterSwap', function (event) {
                window.scrollTo(0, 50);
            });
        });
    </script>
    {% include "_sprig/controls/pagination" with {
        pageInfo: pageInfo,
    } %}
{% endif %}