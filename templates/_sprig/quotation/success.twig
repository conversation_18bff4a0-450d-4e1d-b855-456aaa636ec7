<script>
    localStorage.removeItem('formUuid')
    localStorage.removeItem('{{ quotationModuleIdentifier }}')
    localStorage.removeItem('configuration')
</script>

{% set successPageEntry = successPageEntry ?? entry %}
{% set isQuotationModuleSecondary = isQuotationModuleSecondary ?? false %}

<div class="success container container--padding">
    {% include '_parts/quotation/navigation.twig' with {
        class: 'success__nav'
    } %}
    <h1 class="success__title quotation__title">{{ successPageEntry.successTitle }}</h1>
    <p class="success__subtitle quotation__subtitle">{{ successPageEntry.successSubtitle | nl2br }}</p>
    <div class="success__ctas">
        {% for cta in successPageEntry.successCtas %}
            <div class="success__cta">
                {% if cta.ctaIcon is defined and cta.ctaIcon is iterable and cta.ctaIcon.label %}
                    {% include '_parts/icon.twig' with {
                        icon: cta.ctaIcon.label,
                        class: 'success__cta-icon' ~ (isQuotationModuleSecondary ? ' success__cta-icon--secondary')
                    } %}
                {% endif %}
                <span class="success__cta-title h3">{{ cta.ctaTitle }}</span>
                <p class="success__cta-subtitle">{{ cta.ctaSubtitle | nl2br }}</p>
            </div>
        {% endfor %}
    </div>
    <div class="success__buttons">
        {% for button in successPageEntry.buttons.all() %}
            {% set link = button.linkItem.page %}
            {% set icon = button.linkItem.icon.label %}

            {% include '_parts/button.twig' with {
                link: link,
                type: button.buttonType,
                icon: icon,
                iconPosition: button.buttonIconPosition,
                class: 'success__button' ~ (isQuotationModuleSecondary ? ' btn-secondary'),
            } %}
        {% endfor %}
    </div>
</div>
