{% extends '_sprig/quotation/step-body.twig' %}

{% set disposalTypesWithContainersSelected = selectedContainers | filter(
    disposalType => disposalType | filter((container, key) => key == 'advice' ? container == 'advice' : container > 0) | length
) %}
{# twigcs use-var disposalTypesWithContainersSelected #}

{% set allContainersSelected = disposalTypesWithContainersSelected | length == disposalTypesForContainers | length %}

{% block stepContent %}
    <span class="step__disposal-error step__disposal-error--global hidden">Kies je container(s)</span>

    {% for disposalType in disposalTypesForContainers %}
        {% set anyContainersSelected = selectedContainers[disposalType.uid] | filter(
            (container, key) => key == 'advice' ? container == 'advice' : container > 0
        ) | length > 0 %}

        {% include '_sprig/quotation/step/disposal-containers.twig' with {
            disposalType: disposalType,
            anyContainersSelected: anyContainersSelected,
            showGlobalError: not allContainersSelected
        } %}
    {% endfor %}
{% endblock %}
