{% extends '_sprig/quotation/step-body.twig' %}

{% set hideAdvice = hideAdvice is defined ? hideAdvice : false %}
{% set singleSelect = singleSelect is defined and singleSelect %}

{% block stepContent %}
<span class="step__disposal-error step__disposal-error--global hidden">Maak een keuze</span>

<div class="step__grid disposal-types">
    {% for disposalType in disposalTypes %}
        {% include '_sprig/quotation/step/disposal-type.twig' with {
            disposalType: disposalType,
            selectedDisposalTypes: selectedDisposalTypes
        } %}
    {% endfor %}
    {% if not hideAdvice %}
        {% include '_sprig/quotation/step/advice-option' with {
            text: 'Selecteer deze optie als je graag advies wilt indien je nog niet weet welke afvalstromen je moet selecteren.',
            value: selectedDisposalTypes ? selectedDisposalTypes[0],
            name: 'selectedDisposalTypes[]',
            class: 'disposal-type__advice',
            checkboxClass: 'disposal-type__advice-checkbox'
        } %}
    {% endif %}
</div>
{% endblock %}