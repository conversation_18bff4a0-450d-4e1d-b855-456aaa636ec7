{% extends '_sprig/quotation/step-body.twig' %}

{% set form = lastStepForm ?? entry.form.one() %}

{% set configuration = '' %}
{% set configurationNew = [] %}

{% if (selectedDisposalTypes | length) and summaryItems[0] == 'advice' %}
    {% set configuration = 'Algeheel advies gevraagd' %}
    {% set configurationNew = [{
        disposalType: 'Algeheel advies gevraagd',
        containers: '',
        disposalFrequency: ''
    }] %}
{% else %}
    {% for summaryItem in summaryItems %}
        {% if loop.index0 %}
            {% set configuration = configuration ~ '\r\n\r\n' %}
        {% endif %}

        {% set configuration = configuration ~ 'Afvalstroom: ' ~ (summaryItem.disposalType.title | escape('html')) ~ '\r\n' %}
        {% set containers = '' %}

        {% if summaryItem.selectedContainers == 'advice' %}
            {% set containers = 'Advies gevraagd' %}
        {% else %}
            {% for selectedContainer in summaryItem.selectedContainers %}
                {% if loop.index0 %}
                    {% set containers = containers ~ ', ' %}
                {% endif %}

                {% set containerEntry = selectedContainer.container.container.one() %}
                {% set containers = containers ~ selectedContainer.amount ~ 'x ' ~ (containerEntry.containerContent | escape('html')) ~ 'L' %}
            {% endfor %}
        {% endif %}

        {% set configuration = configuration ~ 'Containers: ' ~ (containers | raw) ~ '\r\n' %}
        {% set frequency = ((summaryItem.selectedFrequency is not defined or summaryItem.selectedFrequency == 'advice') ? 'Advies gevraagd' : summaryItem.selectedFrequency) %}
        {% set configuration = configuration ~ 'Ledigingsfrequentie: ' ~ (frequency | escape('html')) %}
        {% set configurationNew = configurationNew | merge([{
            disposalType: (summaryItem.disposalType.title | escape('html')),
            containers: (containers | replace({', ': '\r\n'}) | raw),
            disposalFrequency: (frequency | escape('html'))
        }]) %}
    {% endfor %}
{% endif %}

{% do craft.formie.populateFormValues(form, {
    configuration: configuration | escape('html'),
    configurationNew: configurationNew,
}, true) %}

{% block stepContent %}
    {% set submitted = craft.formie.plugin.service.getFlash(form.id, 'submitted') %}
    {% set submissionId = craft.app.request.getParam('submission') %}

    {% if submitted %}
        {# Fetch the submission #}
        {% set submission = submissionId ? craft.formie.submissions.uid(submissionId).one() : null %}

        {% if submission %}
            {# Get the value for the field, from the submission #}
            {% set email = submission.getFieldValue('email') | e('html') %}
            {% set phonenumber = submission.getFieldValue('phone') | e('html') %}

            <script defer>
                window.onload = () => {
                    pushToDatalayer('{{ submission.id | e('js') }}', '{{ email | e('js') }}', '{{ phonenumber | e('js') }}');
                }
            </script>
        {% endif %}
    {% endif %}

    {% if not submissionId %}
        {% set config = form.configJson | json_decode %}
        {% if config.settings.submitMethod != 'ajax' %}
            {% do form.setSettings({
                redirectUrl: craft.app.request.url ~ '&submission={uid}',
                submitAction: 'reload',
            }) %}
        {% endif %}

        <div class="step__form form__wrapper {{ isQuotationModuleSecondary ? 'form__wrapper--secondary' }}">
            {{ craft.formie.renderForm(form) }}
        </div>
        <script>
            document.addEventListener('onFormieInit', (e) => {
                const form = document.querySelector('.step__form')
                const uuidInput = form.querySelector('[name="fields[uuidInput]"]')

                form.addEventListener('onAfterFormieSubmit', (e) => {
                    e.preventDefault()

                    if (e.detail.nextPageId) {
                        return;
                    }

                    let email = form.querySelector('[name="fields[email]"]')
                    let phonenumber = form.querySelector('[name="fields[phone]"]')
                    let submissionId = e.detail.submissionId

                    pushToDatalayer(submissionId, email ? email.value : '', phonenumber ? phonenumber.value : '');
                });

                if (uuidInput) {
                    uuidInput.value = localStorage.getItem('formUuid');
                }
            })
        </script>
    {% endif %}
    <script>
        const pushToDatalayer = (submissionId, email, phonenumber) => {
            window.dataLayer = window.dataLayer || [];

            const formUuid = localStorage.getItem('formUuid') || ''
            dataLayer.push({
                event: 'formSubmission',
                formType: '{{ form.title | e('js') }}',
                formId: '{{ form.id | e('js') }}',
                submissionId: submissionId,
                phonenumber: phonenumber,
                email: email,
                formUuid: formUuid
            });

            setTimeout(() => {
                const urlWithoutSubmission = window.location.href
                    .split('&')
                    .filter(string => !string.includes('submission'))
                    .join('&')
                window.location.replace(urlWithoutSubmission + '&success=true')
            }, 250);
        }
    </script>
{% endblock %}
