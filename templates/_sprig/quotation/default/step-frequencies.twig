{% extends '_sprig/quotation/step-body.twig' %}

{% set disposalTypesWithFrequenciesSelected = selectedFrequencies | filter(selectedFrequency => selectedFrequency is not null) | length %}
{% set allFrequenciesSelected = disposalTypesWithFrequenciesSelected == disposalTypesForFrequencies | length %}
{# twigcs use-var disposalTypesWithFrequenciesSelected #}

{% block stepContent %}
    {% if not allFrequenciesSelected %}
        <span class="step__disposal-error step__disposal-error--global hidden">Kies je ledigingsfrequentie</span>
    {% endif %}

    {% for disposalType in disposalTypesForFrequencies %}
        {% set anyFrequenciesSelected = selectedFrequencies[disposalType.uid] is not null %}
        {% include '_sprig/quotation/step/disposal-frequencies.twig' with {
            disposalType: disposalType,
            anyContainersSelected: anyFrequenciesSelected,
            showGlobalError: not allFrequenciesSelected
        } %}
    {% endfor %}
{% endblock %}
