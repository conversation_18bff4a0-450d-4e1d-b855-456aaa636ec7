{% set subtotal = summaryCostDetails.baseTransportCost %}
{% set transportCosts = summaryCostDetails.baseTransportCost %}

{% if summaryCostDetails.transportRetrievalSelected != false %}
    {% set subtotal = subtotal.add(summaryCostDetails.transportRetrievalSelected) %}
    {% set transportCosts = transportCosts.add(summaryCostDetails.transportRetrievalSelected) %}
{% endif %}

{% if summaryCostDetails.containers is defined %}
    {% for container in summaryCostDetails.containers %}
        {% if container.cost %}
            {% set subtotal = subtotal.add(container.cost.multiply(container.amount)) %}
        {% endif %}
    {% endfor %}
{% endif %}

{% if summaryCostDetails.declarationOfDestructionSelected %}
    {% set subtotal = subtotal.add(summaryCostDetails.declarationOfDestructionSelected) %}
{% endif %}

{% set surchargeAmount = subtotal.multiply((summaryCostDetails.environmentalSurcharge / 100) | t) %}
{% set VATAmount = subtotal.add(surchargeAmount).multiply((summaryCostDetails.vat / 100) | t) %}
{% set total = subtotal.add(surchargeAmount, VATAmount) %}

<div class="summary__cost summary__content summary__content--grey summary__content--down">
    <span class="summary-cost__title">Kosten</span>
    <div class="summary-cost__wrapper">
        {% if summaryCostDetails.containers is defined %}
            {% for container in summaryCostDetails.containers %}
                <span class="summary-cost__detail">{{ container.cost ? (container.cost.multiply(container.amount) | money) : container.cost }}</span>
                <span class="summary-cost__detail">
                    Ledigingstarief container {{ container.entry.containerContent }}L{{ container.amount > 1 ? (' ' ~ container.amount ~ 'x') }}
                </span>
            {% endfor %}
        {% endif %}
        {% if summaryCostDetails.declarationOfDestructionSelected %}
            <span class="summary-cost__detail">{{ summaryCostDetails.declarationOfDestructionSelected | money }}</span>
            <span class="summary-cost__detail">Vernietigingsverklaring</span>
        {% endif %}
        <span class="summary-cost__detail">{{ transportCosts | money }}</span>
        <span class="summary-cost__detail">Transportkosten{{ summaryCostDetails.transportRetrievalSelected != false ? ' 2x' }}</span>
    </div>
    <hr />
    <div class="summary-cost__wrapper">
        <span class="summary-cost__detail summary-cost__detail--bold">{{ subtotal | money }}</span>
        <span class="summary-cost__detail summary-cost__detail--bold">Subtotaal (excl. btw en milieutoeslag)</span>
        <span class="summary-cost__detail">{{ surchargeAmount | money }}</span>
        <span class="summary-cost__detail">Milieutoeslag</span>
        <span class="summary-cost__detail">{{ VATAmount | money }}</span>
        <span class="summary-cost__detail">btw</span>
    </div>
    <hr />
    <div class="summary-cost__wrapper">
        <span class="summary-cost__detail summary-cost__detail--bold summary-cost__detail--black">{{ total | money }}</span>
        <span class="summary-cost__detail summary-cost__detail--bold summary-cost__detail--black">Totaal (incl. btw)</span>
    </div>
</div>