{% extends '_sprig/quotation/step-body.twig' %}

{% set form = lastStepForm ?? entry.form.one() %}

{% set subtotal = summaryCostDetails.baseTransportCost %}
{% set containerCosts = subtotal.subtract(summaryCostDetails.baseTransportCost) %} {# creates empty money value #}
{% set transportCosts = summaryCostDetails.baseTransportCost %}

{% if summaryCostDetails.transportRetrievalSelected != false %}
    {% set subtotal = subtotal.add(summaryCostDetails.transportRetrievalSelected) %}
    {% set transportCosts = transportCosts.add(summaryCostDetails.transportRetrievalSelected) %}
{% endif %}

{% if summaryCostDetails.containers is defined %}
    {% for container in summaryCostDetails.containers %}
        {% if container.cost %}
            {% set subtotal = subtotal.add(container.cost.multiply(container.amount)) %}
            {% set containerCosts = containerCosts.add(container.cost.multiply(container.amount)) %}
        {% endif %}
    {% endfor %}
{% endif %}

{% if summaryCostDetails.declarationOfDestructionSelected %}
    {% set subtotal = subtotal.add(summaryCostDetails.declarationOfDestructionSelected) %}
{% endif %}

{% set surchargeAmount = subtotal.multiply((summaryCostDetails.environmentalSurcharge / 100) | t) %}
{% set VATAmount = subtotal.add(surchargeAmount).multiply((summaryCostDetails.vat / 100) | t) %}
{% set total = subtotal.add(surchargeAmount, VATAmount) %}

{% block stepContent %}
    {% set submitted = craft.formie.plugin.service.getFlash(form.id, 'submitted') %}
    {% set submissionId = craft.app.request.getParam('submission') %}

    {% if not submissionId %}
        {% set config = form.configJson | json_decode %}
        {% if config.settings.submitMethod != 'ajax' %}
            {% do form.setSettings({
                redirectUrl: craft.app.request.url ~ '&submission={uid}',
                submitAction: 'reload',
            }) %}
        {% endif %}

        <div class="step__form form__wrapper {{ isQuotationModuleSecondary ? 'form__wrapper--secondary' }}">
            {{ craft.formie.renderForm(form) }}
        </div>
    {% endif %}

    {% if not submitted %}
        <script>
            let isFormFilled = false

            window.onload = () => {
                if (isFormFilled) return

                isFormFilled = true

                const fieldSelector = 'fields[configuration][rows][new1][fields]'
                const containersField = document.querySelector(`[name="${fieldSelector}[containers]"]`);
                const fillConvenienceField = document.querySelector(`[name="${fieldSelector}[fillConvenience]"]+label`);
                const destructionDeclarationField = document.querySelector(`[name="${fieldSelector}[destructionDeclaration]"]+label`);
                const deliveryDateField = document.querySelector(`[name="${fieldSelector}[deliveryDate]"]`);
                const retrievalTypeField = document.querySelector(`[name="${fieldSelector}[retrievalType]"]`);
                const retrievalDateField = document.querySelector(`[name="${fieldSelector}[retrievalDate]"]`);

                // Fill configuration fields
                {% set containers = '' %}

                {% for container in summaryCostDetails.containers %}
                    {% set containers = containers ~ 'Container ' ~ container.entry.containerContent ~ 'L ' ~ container.amount ~ 'x' ~ (not loop.last ? '\r\n') %}
                {% endfor %}

                containersField.value = `{{ containers }}`

                {% if fillConvenienceSelected == 'true' %}
                    fillConvenienceField.click()
                {% endif %}

                {% if declarationOfDestructionSelected == 'true' %}
                    destructionDeclarationField.click()
                {% endif %}

                deliveryDateField.value = '{{ deliveryDate }}'

                {% for option in entry.desiredCollectionDateOptions.options %}
                    {% if deliveryOption == option.uid %}
                        retrievalTypeField.value = '{{ option.label }}'
                        {% if option.label == 'Later doorgeven' %}
                            {% set selectedDeliveryOption = 'Later doorgeven (bel ons zodra de ophaaldatum bekend is)' %}
                        {% elseif option.label == 'Direct kiezen' %}
                            {% set selectedDeliveryOption = '' %}
                        {% elseif option.label == 'Stop & Go' %}
                            {% set selectedDeliveryOption = 'Stop & Go (direct afvoeren)' %}
                        {% else %}
                            {% set selectedDeliveryOption = option.label %}
                        {% endif %}
                    {% endif %}
                {% endfor %}

                retrievalDateField.value = '{{ retrievalDate }}'

                // Fill in the costs
                const containerCostField = document.querySelector(`[name="${fieldSelector}[containerkosten]"]`);
                const declarationOfDestructionCostField = document.querySelector(`[name="${fieldSelector}[vernietigingsverklaringKosten]"]`);
                const transportCostField = document.querySelector(`[name="${fieldSelector}[transportkosten]"]`);
                const environmentalSurchargeCostField = document.querySelector(`[name="${fieldSelector}[milieutoeslag]"]`);
                const vatCostField = document.querySelector(`[name="${fieldSelector}[btw]"]`);
                const totalCostField = document.querySelector(`[name="${fieldSelector}[totaal]"]`);
                containerCostField.value = '{{ containerCosts | money }}'
                const subtotalCostField = document.querySelector(`[name="fields[subtotaal]"]`);
                const selectedDeliveryOption = document.querySelector(`[name="fields[selectedDeliveryOption]"]`);

                {% if summaryCostDetails.declarationOfDestructionSelected %}
                    declarationOfDestructionCostField.value = '{{ summaryCostDetails.declarationOfDestructionSelected | money }}'
                {% endif %}

                transportCostField.value = '{{ transportCosts | money }}'
                environmentalSurchargeCostField.value = '{{ surchargeAmount | money }}'
                vatCostField.value = '{{ VATAmount | money }}'
                totalCostField.value = '{{ total | money }}'
                subtotalCostField.value = '{{ subtotal | money }}'
                selectedDeliveryOption.value = '{{ selectedDeliveryOption | striptags }}'

                // Postal code address retrieval
                const postalCodeField = document.querySelector('[name="fields[postcode]"]');
                const houseNumberField = document.querySelector('[name="fields[huisnummer]"]');
                const streetField = document.querySelector('[name="fields[straat]"]');
                const cityField = document.querySelector('[name="fields[plaats]"]');

                const invoicePostalCodeField = document.querySelector('[name="fields[postcode1]"]');
                const invoiceHouseNumberField = document.querySelector('[name="fields[huisnummer1]"]');
                const invoiceStreetField = document.querySelector('[name="fields[straat1]"]');
                const invoiceCityField = document.querySelector('[name="fields[plaats1]"]');

                postalCodeField.value = `{{ postalCode }}`

                postalCodeField.addEventListener('keyup', () => postalCodeFieldsTrigger(postalCodeField.value, houseNumberField.value, streetField, cityField))
                houseNumberField.addEventListener('keyup', () => postalCodeFieldsTrigger(postalCodeField.value, houseNumberField.value, streetField, cityField))

                invoicePostalCodeField.addEventListener('keyup', () => postalCodeFieldsTrigger(invoicePostalCodeField.value, invoiceHouseNumberField.value, invoiceStreetField, invoiceCityField))
                invoiceHouseNumberField.addEventListener('keyup', () => postalCodeFieldsTrigger(invoicePostalCodeField.value, invoiceHouseNumberField.value, invoiceStreetField, invoiceCityField))

                let postalCodeTimeout = null

                function postalCodeFieldsTrigger(postalCode, houseNumber, streetField, cityField) {
                    if (postalCodeTimeout) {
                        clearTimeout(postalCodeTimeout)
                    }

                    if (!postalCode || !houseNumber) return

                    postalCodeTimeout = setTimeout(async () => {
                        houseNumber = Number(houseNumber)

                        const {street, city} = await getPostalCodeAddress(postalCode, houseNumber)

                        if (street && city) {
                            streetField.value = street
                            cityField.value = city
                        }
                    }, 300)
                }
                function getPostalCodeAddress(postalCode, houseNumber) {
                    const postalCodeRegex = /^[0-9]{4}\s?[a-zA-Z]{2}$/
                    const postalCodeMatch = postalCode.match(postalCodeRegex)

                    if (postalCodeMatch) {
                        postalCode = postalCode.replace(' ', '')
                    } else {
                        return
                    }
                    if (typeof houseNumber !== 'number') return

                    return new Promise((resolve, reject) => {
                        fetch(`${window.location.origin}/api/craft-endpoints/address/?postalCode=${postalCode}&houseNumber=${houseNumber}`)
                        .then(response => response.json())
                        .then(({ success, data }) => {
                            if (success) {
                                resolve({ street: data.street, city: data.city })
                            } else {
                                throw new Error('Postalcode not found')
                            }
                        })
                        .catch(error => reject(error))
                    })
                }
            }
        </script>
    {% endif %}

    {% include '_datalayer/archive-form-submission-event' with {
        formType: form.title,
        formId: form.id,
        postalCode: postalCode,
        total: (total.amount / 100) | number_format(2, '.', ','),
        shippingCost: (transportCosts.amount / 100) | number_format(2, '.', ','),
        vat: (VATAmount.amount / 100) | number_format(2, '.', ','),
        containerCost: (containerCosts.amount / 100) | number_format(2, '.', ','),
        declarationOfDestructionCost: (summaryCostDetails.declarationOfDestructionSelected) ? (summaryCostDetails.declarationOfDestructionSelected.amount / 100) | number_format(2, '.', ',') : 0,
        transportCost: (transportCosts.amount / 100) | number_format(2, '.', ','),
        environmentalSurcharge: (surchargeAmount.amount / 100) | number_format(2, '.', ',')
    } %}
{% endblock %}
