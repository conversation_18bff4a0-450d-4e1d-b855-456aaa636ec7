{% extends '_sprig/quotation/step-body.twig' %}

{% set showRetrievalDate = false %}
{% set stepButtonClass = 'postal-code-event-trigger' %}

{% block stepContent %}

    <div class="delivery-retrieval__wrapper"
        {% if entry.unavailableDates is defined and entry.unavailableDates | length %}data-unavailable-dates="{{ entry.unavailableDates | map(item => item.date | date('Y-m-d')) | join(", ") }}"{% endif %}
    >
        <div class="delivery-retrieval__item">
            <div class="delivery-retrieval__details">
                <h4 class="delivery-retrieval__title">{{ entry.postalCode.label }}</h4>
                <p class="delivery-retrieval__subtitle">{{ entry.postalCode.subText }}</p>
            </div>
            <input class="delivery-retrieval__input delivery-retrieval__postal-code {{ stepDeliveryRetrievalPartiallyFilled and postalCode is empty ? 'delivery-retrieval__input--error' }}"
                placeholder="1234 AB"
                pattern="[0-9]{4}\s?[A-Za-z]{2}"
                name="postalCode"
                value="{{ postalCode }}" />
        </div>
        <div class="delivery-retrieval__item">
            <div class="delivery-retrieval__details">
                <h4 class="delivery-retrieval__title">{{ entry.desiredDeliveryDate.label }}</h4>
                <p class="delivery-retrieval__subtitle">{{ entry.desiredDeliveryDate.subText }}</p>
            </div>
            <div class="delivery-retrieval__date-wrapper">
                <input class="delivery-retrieval__input delivery-retrieval__input--date delivery-retrieval__delivery-date {{ stepDeliveryRetrievalPartiallyFilled and deliveryDate is empty ? 'delivery-retrieval__input--error' }}"
                    pattern="[0-9]{2}-[0-1][0-9]-[0-9]{4}"
                    name="deliveryDate"
                    value="{{ deliveryDate }}"
                    readonly />
                {% include '_parts/icon.twig' with {
                    icon: 'calendar',
                    class: 'delivery-retrieval__date-icon delivery-retrieval__date-icon--' ~ (isQuotationModuleSecondary ? 'secondary' : 'primary')
                } %}
            </div>
        </div>
        <div class="delivery-retrieval__item delivery-retrieval__item--down">
            <div class="delivery-retrieval__details">
                <h4 class="delivery-retrieval__title">{{ entry.desiredCollectionDateOptions.label }}</h4>
                <p class="delivery-retrieval__subtitle">{{ entry.desiredCollectionDateOptions.subText }}</p>
            </div>
            <div class="delivery-retrieval__options">
                {% for option in entry.desiredCollectionDateOptions.options %}
                    {% if deliveryOption == option.uid and option.isChooseDirectly == '1' %}
                        {% set showRetrievalDate = true %}
                    {% endif %}
                    <div class="deliver-retrieval__option">
                        <input id="{{ option.uid }}"
                            class="deliver-retrieval__input deliver-retrieval__input--{{ isQuotationModuleSecondary ? 'secondary' : 'primary' }}"
                            type="radio"
                            name="deliveryOption"
                            value="{{ option.uid }}"
                            data-show-retrieval-date="{{ option.isChooseDirectly }}"
                            {{ deliveryOption == option.uid ? 'checked' }}
                            {{ active ? 'required' }} />
                        <label class="deliver-retrieval__label" for="{{ option.uid }}">
                            <span class="deliver-retrieval__label-title">{{ option.label }}</span>
                            <p class="deliver-retrieval__label-subtext">{{ option.subText }}</p>
                        </label>
                    </div>
                {% endfor %}
            </div>
        </div>
        <div class="delivery-retrieval__item delivery-retrieval__retrieval {{ not showRetrievalDate ? 'delivery-retrieval__retrieval--hidden' }}">
            <div class="delivery-retrieval__details">
                <h4 class="delivery-retrieval__title">{{ entry.desiredCollectionDate.label }}</h4>
                <p class="delivery-retrieval__subtitle">{{ entry.desiredCollectionDate.subText }}</p>
            </div>
            <div class="delivery-retrieval__date-wrapper">
                <input class="delivery-retrieval__input delivery-retrieval__input--date delivery-retrieval__retrieval-date {{ stepDeliveryRetrievalPartiallyFilled and retrievalDateRequired and retrievalDate is empty ? 'delivery-retrieval__input--error' }}"
                    pattern="[0-9]{2}-[0-1][0-9]-[0-9]{4}"
                    name="retrievalDate"
                    value="{{ retrievalDate }}"
                    readonly />
                {% include '_parts/icon.twig' with {
                    icon: 'calendar',
                    class: 'delivery-retrieval__date-icon delivery-retrieval__date-icon--' ~ (isQuotationModuleSecondary ? 'secondary' : 'primary')
                } %}
            </div>
        </div>
    </div>

    {% include '_datalayer/postal-code-event' %}
{% endblock %}
