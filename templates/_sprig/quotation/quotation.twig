{% set isQuotationModuleSecondary = isQuotationModuleSecondary ?? false %}

<div id="quotation-module" class="quotation {{ isQuotationModuleSecondary ? 'quotation--secondary' }} {{ quotationModuleIdentifier }}">
    {# Save url on each reload #}
    <script>
        var currentUrl = window.location.href
        var urlHasQueries = !!currentUrl.split('?')[1]

        if (urlHasQueries) {
            var saveUrl = true
            var lastQuotationUrl = localStorage.getItem('{{ quotationModuleIdentifier }}')

            if (lastQuotationUrl) {
                var lastQuotationObject = JSON.parse(lastQuotationUrl)

                if (lastQuotationObject.url === currentUrl) {
                    saveUrl = false
                }
            }

            const urlWithoutSubmission = currentUrl
                .split('&')
                .filter(string => !string.includes('submission'))
                .join('&')

            if (saveUrl) {
                localStorage.setItem(
                    '{{ quotationModuleIdentifier }}',
                    JSON.stringify({
                        url: urlWithoutSubmission,
                        timestamp: new Date().getTime(),
                    })
                )
            }
        }
    </script>

    <div class="quotation__usp-header">
        {% include '_sprig/quotation/usp-header.twig' %}
    </div>
    <div class="quotation__wrapper">
        <div class="quotation__column quotation__column--left container--padding">
            {% include '_parts/quotation/navigation.twig' %}
            <h1 class="quotation__title">{{ entry.title }}</h1>
            {% if entry.subtitle is not empty %}
                <p class="quotation__subtitle">{{ entry.subtitle | nl2br }}</p>
            {% endif %}
            <form s-replace="#quotation-module">
                {% block steps %}
                {% endblock %}
            </form>
            {% include (showFormStep ? lastStepTemplate : '_sprig/quotation/step-empty.twig') with {
                active: (currentStep == 'step-form'),
                filled: false,
                disabled: false,
                step: lastStepFields,
                stepIndex: lastStepIndex,
            } %}
        </div>
        <div class="quotation__column quotation__column--right quotation__column--{{ isQuotationModuleSecondary ? 'secondary' : 'primary' }} container--padding">
            {% include '_sprig/quotation/summary.twig' %}
        </div>
    </div>
</div>
