{% set stepButtonClass = stepButtonClass ?? '' %}

<details
    class="step step--details {{ disabled ? 'step--disabled' }} {{ active and stepIndex != 1 ? 'step--active' }}" {{ active ? 'open' }}>
    <summary class="step__header details__header">
        <div class="step__indicator {{ filled ? 'step__indicator--filled' }} {{ active ? 'step__indicator--active' }} {{ isQuotationModuleSecondary ? 'step__indicator--secondary' }}">
            {% if filled and not active %}
                {% include '_parts/icon.twig' with {
                    icon: 'check',
                    class: 'step__indicator-icon'
                } %}
            {% else %}
                {{ stepIndex }}
            {% endif %}
        </div>
        <h3 class="step__title {{ not active and not filled ? 'step__title--inactive' }}">{{ step.stepTitle }}</h3>
        {% if step.stepSubtitle is not empty and active %}
            <p class="step__subtitle">{{ step.stepSubtitle }}</p>
        {% endif %}
    </summary>
    <div class="step__content details__content">
        {% block stepContent %}
        {% endblock %}
        {% if stepIndex != lastStepIndex %}
            {% include '_parts/button.twig' with {
                component: 'button',
                label: filled ? 'Opslaan' : 'Bevestig keuze en ga naar stap ' ~ (stepIndex + 1) ~ '<span class="step__button-inner">/' ~ lastStepIndex ~ '</span>',
                class: 'step__button' ~ (isQuotationModuleSecondary ? ' step__button--secondary btn-secondary') ~ ' ' ~ stepButtonClass,
                name: 'Bevestig keuze en ga naar stap ' ~ (stepIndex + 1) ~ ' van ' ~ lastStepIndex,
                attributes: 'data-next-step-button'
            } %}
        {% endif %}
    </div>
</details>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const toggleError = (elements, show) => elements.forEach(el => {
        el.classList.toggle('hidden', !show);
        el.style.display = show ? 'block' : 'none';
    });

    const isSelected = container => [...container.querySelectorAll('input')]
        .some(input => input.checked || (input.type === 'number' && input.value > 0));

    const validateSelections = (elements) => {
        let minimalSelectedCount = 0;
        let allSelected = false;
        document.querySelectorAll('.step__disposal-error').forEach(el => toggleError([el], true));

        elements.forEach(el => {

            const globalError = el.parentElement.querySelectorAll('.step__disposal-error.step__disposal-error--global');
            const sectionError = el.querySelectorAll('.step__disposal-error.step__disposal-error--below-title');

            isSelected(el) ? (minimalSelectedCount++, toggleError(sectionError, false)) : toggleError(globalError, true);

        });

        if (minimalSelectedCount >= elements.length && minimalSelectedCount > 0) {
            document.querySelectorAll('.step__disposal-error').forEach(el => toggleError([el], false));
            allSelected = true;
        }

        return allSelected;
    };

    const scrollToStepTitle = element => {
        const title = element.querySelector('.step__title');
        if (title) {
          title.scrollIntoView({ behavior: 'smooth' });
        }
    };

    document.querySelectorAll('[data-next-step-button]').forEach(button => {
        button.addEventListener('click', event => {
            const step = button.closest('details');
            const disposalTypes = step.querySelectorAll('.disposal-types');
            const containers = step.querySelectorAll('.disposal-containers');
            const frequencies = step.querySelectorAll('.disposal-frequencies');

            let isValid = disposalTypes.length ? validateSelections(disposalTypes) : true;

            if (containers.length > 0) {
                isValid = isValid && (containers.length ? validateSelections(containers) : true);
            }

            if(frequencies.length > 0) {
                isValid = isValid && (frequencies.length ? validateSelections(frequencies) : true);
            }

            if (!isValid) {
                scrollToStepTitle(step);
                event.preventDefault();
            }
        });
    });
});
</script>
