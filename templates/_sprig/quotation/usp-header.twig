{% set uspSimple = entry.uspItemSimple | slice(0, 4) %}
<div class="fixed w-full top-0 left-0 text-primary-500 text-13 xl:text-16 lg:block hidden z-20 bg-tertiary-100">
    <ul class="w-full p-2 flex justify-center gap-4">
        {% for usp in uspSimple %}
            <li class="flex flex-row items-center">
                {% include '_parts/icon.twig' with {
                    icon: 'check',
                    class: 'text-26 xl:text-28 mr-1'
                } %}
                {{ usp.uspItemSimpleTitle }}
            </li>
        {% endfor %}
    </ul>
</div>
