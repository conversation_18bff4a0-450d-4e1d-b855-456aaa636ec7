{% set urlParts = craft.app.request.absoluteUrl | split('?') %}
{% set queryVariables = urlParts[1] is defined ? urlParts[1] | split('&') : null %}

{% set hideContainerDetailsInSummary = hideContainerDetailsInSummary is defined and hideContainerDetailsInSummary %}
{% set useAlternativeContainerThumbnail = useAlternativeContainerThumbnail is defined and useAlternativeContainerThumbnail %}
{% set limitRepeatInfoPerDisposalType = limitRepeatInfoPerDisposalType is defined and limitRepeatInfoPerDisposalType %}
{% set removeLinkRemovesAll = removeLinkRemovesAll ?? false %}

{% set summaryText = '' %}

{% if summaryItems | length and summaryItems[0] != 'advice' %}
    {% set summaryText = summaryItems | map(summaryItem => summaryItem.disposalType.title) | join('<span>|</span>') %}
{% endif %}

<button class="summary__toggle">
    <span class="summary__toggle-title">Overzicht</span>
    <span class="summary__toggle-open">
        Bekijk
        {% include '_parts/icon.twig' with {
            icon: 'chevron-up',
            class: 'summary__toggle-icon'
        } %}
    </span>
    <span class="summary__toggle-summary">{{ summaryText | default('Nog geen containers geselecteerd') | raw }}</span>
</button>
<div class="summary">
    <span class="summary__title">
        Overzicht
        <button class="summary__close">
            Sluit
            {% include '_parts/icon.twig' with {
                icon: 'chevron-down',
                class: 'summary__close-icon'
            } %}
        </button>
    </span>
    {% if not (summaryItems | length) %}
        <div class="summary__content summary__content--empty">Nog geen afvalstromen geselecteerd</div>
    {% else %}
        {% if summaryItems[0] == 'advice' %}
            <div class="summary__content">Algeheel advies gevraagd</div>
        {% else %}
            {% for summaryItem in summaryItems %}
                {% set containersAvailable = summaryItem.selectedContainers is defined and summaryItem.selectedContainers != 'advice' and summaryItem.selectedContainers | length %}
                {% set containersAdvice = summaryItem.selectedContainers is defined and summaryItem.selectedContainers == 'advice' %}
                {% if removeLinkRemovesAll %}
                    {% set removeLink = urlParts[0] ~ '?' %}
                {% else %}
                    {% set removeLink = urlParts[0] ~ '?' ~ (queryVariables | filter(variable => summaryItem.disposalType.uid not in variable) | join('&')) %}
                {% endif %}

                {% if containersAvailable %}
                    {% for selectedContainer in summaryItem.selectedContainers %}
                        {% set container = selectedContainer.container %}
                        {% set containerEntry = container.container.one() %}
                        {% set sizes = containerEntry.containerSizes %}
                        {% set containerThumbnail = useAlternativeContainerThumbnail ? container.alternativeThumbnail.one() : container.thumbnail.one() %}

                        <div class="summary__content">
                            <div class="summary__details">
                                {% if not (limitRepeatInfoPerDisposalType and not loop.first) %}
                                    <span class="summary__disposal-title">{{ summaryItem.disposalType.title }}</span>
                                {% endif %}
                                <span class="summary__container-type">
                                    {{ containerEntry.containerType ?? 'Rolcontainer' }} {{ containerEntry.containerContent }}L
                                </span>
                                {% if additionalContainerInformation is defined %}
                                    {{ additionalContainerInformation }}
                                {% endif %}
                                <span class="summary__container-amount">
                                    {{ selectedContainer.amount }} {{ selectedContainer.amount > 1 ? 'stuks' : 'stuk' }}
                                </span>
                                {% if not (limitRepeatInfoPerDisposalType and not loop.last) %}
                                    {% if additionalSummaryInformation is defined %}
                                        {{ additionalSummaryInformation }}
                                    {% endif %}
                                    <a class="summary__disposal-remove" href="{{ removeLink }}">{{ removeLinkText ?? 'Afvalstroom verwijderen' }}</a>
                                {% endif %}
                            </div>
                            {% include '_parts/picture.twig' with {
                                class: 'summary__container-image',
                                image: containerThumbnail,
                                width: 140,
                                height: 140,
                                loading: 'lazy',
                                quality: 70,
                                sizes: ['0.25x', '0.5x', '1x'],
                            } %}
                        </div>
                        {% if not hideContainerDetailsInSummary %}
                            <div class="summary__content summary__content--grey summary__content--mobile-down">
                                <div class="summary__details">
                                    <span class="summary__details-title">Inhoud en afmetingen</span>
                                    <span class="summary__detail">
                                        {% include '_parts/icon.twig' with {
                                            icon: 'container',
                                            class: 'summary__detail-icon'
                                        } %}
                                        {{ containerEntry.containerContent }} liter
                                    </span>
                                    <span class="summary__detail">
                                        {% include '_parts/icon.twig' with {
                                            icon: 'length',
                                            class: 'summary__detail-icon'
                                        } %}
                                        {{ sizes.height }} x {{ sizes.width }} x {{ sizes.depth }} cm<br />
                                        <span class="summary__subdetail">h x b x d cm</span>
                                    </span>
                                </div>
                                <div class="summary__details">
                                    {% if summaryItem.selectedFrequency is defined and summaryItem.selectedFrequency is not empty %}
                                        <span class="summary__details-title">Ledigingsfrequentie</span>
                                        <span class="summary__detail">
                                            {% include '_parts/icon.twig' with {
                                                icon: 'calendar',
                                                class: 'summary__detail-icon'
                                            } %}
                                            {{ summaryItem.selectedFrequency == 'advice' ? 'Advies gevraagd' : summaryItem.selectedFrequency }}
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% else %}
                    <div class="summary__content">
                        <div class="summary__details">
                            {% if not (limitRepeatInfoPerDisposalType and not loop.first) %}
                                <span class="summary__disposal-title">{{ summaryItem.disposalType.title }}</span>
                            {% endif %}
                            {% if containersAdvice %}
                                <span class="summary__containers-advice">Advies gevraagd</span>
                            {% endif %}
                            {% if not (limitRepeatInfoPerDisposalType and not loop.last) %}
                                <a class="summary__disposal-remove" href="{{ removeLink }}">{{ removeLinkText ?? 'Afvalstroom verwijderen' }}</a>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
                {{ summaryCostOverview ?? '' }}
            {% endfor %}
        {% endif %}
    {% endif %}
</div>
