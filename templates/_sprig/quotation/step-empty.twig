<div class="step step--empty">
    <div class="step__header">
        <div class="step__indicator {{ filled ? 'step__indicator--filled' }} {{ isQuotationModuleSecondary ? 'step__indicator--secondary' }}">
            {% if filled %}
                {% include '_parts/icon.twig' with {
                    icon: 'check',
                    class: 'step__indicator-icon'
                } %}
            {% else %}
                {{ stepIndex }}
            {% endif %}
        </div>
        <h3 class="step__title {{ not active and not filled ? 'step__title--inactive' }}">{{ step.stepTitle }}</h3>
    </div>
</div>