{% set MAX_CONTAINERS_IN_VIEW = 7 %}
{% set hideTitles = hideTitles is defined and hideTitles %}
{% set hideAdvice = hideAdvice is defined and hideAdvice %}
{% set alternateContentsText = alternateContentsText is defined and alternateContentsText %}
{% set maxContainersPerType = maxContainersPerType ?? false %}
{% set showContainerCost = showContainerCost ?? false %}
{% set isQuotationModuleSecondary = isQuotationModuleSecondary ?? false %}

<div class="disposal-containers">
    <span class="step__disposal-title">{{ disposalType.title }}</span>
    <div class="disposal-containers__section">
        {% if not hideTitles %}
            <span class="step__disposal-error step__disposal-error--below-title hidden">Kies je container(s) voor {{ disposalType.title }}</span>
        {% endif %}

        <div class="disposal-containers__grid step__grid" {% if maxContainersPerType %}data-max-containers="{{ maxContainersPerType }}"{% endif %}>
            {% for container in disposalType.containers %}
                {% set containerEntry = container.container.one() %}
                {% set selectedContainerAmount = selectedContainers[disposalType.uid][containerEntry.uid] %}
                {% set containerThumbnail = container.thumbnail.one() %}
                {% set contents = containerEntry.containerContent %}
                {% set sizes = containerEntry.containerSizes %}
                {% set bagsIndication = containerEntry.containerBagsIndication %}
                {% set movingBoxIndication = containerEntry.movingBoxIndication %}
                {% set binderIndication = containerEntry.binderIndication %}

                <div class="disposal-containers__item-wrapper step__card
                    {{ selectedContainerAmount ? 'step__card--selected' }}
                    {{ loop.index > MAX_CONTAINERS_IN_VIEW ? 'disposal-containers__item-wrapper--hidden disposal-containers__item-wrapper--overflow' }}
                    {{ isQuotationModuleSecondary ? 'step__card--secondary' }}">
                    {% include '_parts/picture.twig' with {
                        class: 'disposal-containers__item-image',
                        image: containerThumbnail,
                        width: 80,
                        height: 80,
                        loading: 'lazy',
                        quality: 30,
                        sizes: ['0.25x', '0.5x', '1x'],
                    } %}
                    <span class="disposal-containers__item-title">{{ contents }} liter</span>
                    <span class="disposal-containers__item-sizes">
                        {{ sizes.height }} x {{ sizes.width }} x {{ sizes.depth }} cm
                    </span>
                    {% if bagsIndication != 0 %}
                        <span class="disposal-containers__item-bags">
                            {% if alternateContentsText %}
                                {{ movingBoxIndication }} verhuis{{ movingBoxIndication == 1 ? 'doos' : 'dozen' }} of {{ binderIndication }} ordner{{ binderIndication != 1 ? 's' }}
                            {% else %}
                                {{ bagsIndication }} vuilniszak{{ bagsIndication != 1 ? 'ken' }}
                            {% endif %}
                        </span>
                    {% endif %}
                    <div class="disposal-containers__input-wrapper">
                        <button type="button" class="disposal-containers__input-button disposal-containers__input-button--minus {{ isQuotationModuleSecondary ? 'disposal-containers__input-button--secondary' }}" {{ selectedContainerAmount < 1 ? 'disabled' }}><span class="sr-only">Verwijder een container</span></button>
                        <input type="number"
                            class="disposal-containers__input"
                            min="0"
                            name="selectedContainers[{{ disposalType.uid }}][{{ containerEntry.uid }}]]"
                            value="{{ selectedContainerAmount }}"
                            {% if maxContainersPerType %}max="{{ maxContainersPerType }}" readonly{% endif %} />
                        <button type="button" class="disposal-containers__input-button disposal-containers__input-button--plus {{ isQuotationModuleSecondary ? 'disposal-containers__input-button--secondary' }}"><span class="sr-only">Voeg een container toe</span></button>
                    </div>
                    {% if showContainerCost and containerEntry.cost %}
                        <span class="disposal-containers__item-favorite tag tag--small tag--{{ isQuotationModuleSecondary ? 'secondary' : 'primary' }}">{{ containerEntry.cost | money }} p.s.</span>
                    {% elseif container.favorite %}
                        <span class="disposal-containers__item-favorite tag tag--small tag--{{ isQuotationModuleSecondary ? 'secondary' : 'primary' }}">MEEST GEKOZEN</span>
                    {% endif %}
                </div>
            {% endfor %}
            {% if not hideAdvice %}
                {% include '_sprig/quotation/step/advice-option' with {
                    text: 'Selecteer deze optie als je graag advies wilt indien je nog niet weet welke container je nodig hebt.',
                    value: selectedContainers[disposalType.uid].advice,
                    name: 'selectedContainers[' ~ disposalType.uid ~ '][advice]',
                    class: 'disposal-containers__advice',
                    checkboxClass: 'disposal-containers__advice-checkbox'
                } %}
            {% endif %}
        </div>
    </div>
    {% if (disposalType.containers | length) > MAX_CONTAINERS_IN_VIEW %}
        <button type="button" class="disposal-containers__overflow">
            <span class="disposal-containers__overflow-text disposal-containers__overflow-text--open">Toon alle maten</span>
            <span class="disposal-containers__overflow-text disposal-containers__overflow-text--closed">Toon minder maten</span>
            {% include '_parts/icon' with {
                icon: 'chevron-down',
                class: 'disposal-containers__overflow-icon'
            } %}
        </button>
    {% endif %}
</div>
