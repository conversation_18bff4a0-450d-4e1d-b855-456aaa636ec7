{% set hideTitles = hideTitles is defined and hideTitles %}
{% set isQuotationModuleSecondary = isQuotationModuleSecondary ?? false %}

<div class="disposal-frequencies">
    <div class="disposal-frequencies__section">
        <span class="step__disposal-title">{{ disposalType.title }}</span>
        {% if not hideTitles %}
            {% if not anyFrequenciesSelected %}
                <span class="step__disposal-error step__disposal-error--below-title hidden">Kies je ledigingsfrequentie voor {{ disposalType.title }}</span>
            {% endif %}
        {% endif %}

        <div class="step__grid">
            {% set frequencies = disposalType.emptyingFrequencies.all() %}
            {% for frequency in frequencies %}
                <input id="{{ disposalType.uid }}-{{ frequency.uid }}" class="disposal-frequencies__input" type="radio" name="selectedFrequencies[{{ disposalType.uid }}]" value="{{ frequency.uid }}" {{ selectedFrequencies[disposalType.uid] == frequency.uid ? 'checked' }} />
                <label class="disposal-frequencies__item step__card {{ isQuotationModuleSecondary ? 'step__card--secondary' : '' }}" for="{{ disposalType.uid }}-{{ frequency.uid }}">
                    {{ frequency.styledText is not empty ? (frequency.styledText | nl2br) : frequency.title }}
                </label>
            {% endfor %}
            {% include '_sprig/quotation/step/advice-option' with {
                text: 'Selecteer deze optie als je graag advies wilt over de ledigingsfrequentie.',
                value: selectedFrequencies[disposalType.uid],
                name: 'selectedFrequencies[' ~ disposalType.uid ~ ']',
                class: 'disposal-frequencies__item',
                checkboxClass: 'disposal-frequencies__input',
                type: 'radio'
            } %}
        </div>
    </div>
</div>
