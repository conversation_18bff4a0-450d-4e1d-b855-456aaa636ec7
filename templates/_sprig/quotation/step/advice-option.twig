<input class="advice-option__checkbox {{ checkboxClass }}" type="{{ type is defined ? type : 'checkbox' }}" name="{{ name }}" id="{{ name }}" value="advice" {{ value == 'advice' ? 'checked' }} />
<label class="advice-option step__card {{ isQuotationModuleSecondary ? 'step__card--secondary' }} {{ class }}" for="{{ name }}">
    {% include '_parts/icon.twig' with {
        icon: 'advice',
        class: 'advice-option__icon' ~ (isQuotationModuleSecondary ? ' advice-option__icon--secondary')
    } %}
    <span class="advice-option__title {{ isQuotationModuleSecondary ? 'advice-option__title--secondary' }}">Adviseer mij!</span>
    {% if text is defined %}
        <p class="advice-option__text">{{ text }}</p>
    {% endif %}
</label>