{% set disposalTypeImage = disposalType.disposalTypeThumbnail.one() %}
{% set smallImages = smallImages is defined and smallImages %}

<div class="disposal-type__wrapper">
    <input type="{{ singleSelect ? 'radio' : 'checkbox' }}"
        id="{{ disposalType.uid }}"
        class="disposal-type__checkbox"
        name="{{ singleSelect ? 'selectedDisposalTypes' : 'selectedDisposalTypes[]' }}"
        value="{{ disposalType.uid }}"
        {{ disposalType.uid in selectedDisposalTypes ? 'checked' }}
    />
    <label class="step__card {{ isQuotationModuleSecondary ? 'step__card--secondary' }}" for="{{ disposalType.uid }}">
        {% include '_parts/picture.twig' with {
            class: 'disposal-type__image' ~ (smallImages ? ' disposal-type__image--small' : ''),
            image: disposalTypeImage,
            width: smallImages ? 40 : 80,
            height: smallImages ? 40 : 80,
            loading: 'lazy',
            quality: 30,
            sizes: ['0.25x', '0.5x', '1x'],
        } %}
        <span class="disposal-type__title">{{ disposalType.title }}</span>
    </label>
</div>