{% set fillConvenience = {
    label: entry.fillConvenience.label,
    subText: entry.fillConvenience.subText,
    featuredImage: entry.fillConvenience.featuredImage is not empty ? entry.fillConvenience.featuredImage.one() : false
} %}
{% set withoutFillConvenience = {
    label: entry.withoutFillConvenience.label,
    subText: entry.withoutFillConvenience.subText,
    featuredImage: entry.withoutFillConvenience.featuredImage is not empty ? entry.withoutFillConvenience.featuredImage.one() : false
} %}

<div class="step__grid step__grid--large">
    <input id="container-fill-convenience-true"
        class="disposal-container-type__input"
        type="radio"
        name="fillConvenienceSelected"
        value="true"
        {{ fillConvenienceSelected == 'true' ? 'checked' : '' }} />
    <label class="disposal-container-type__item step__card {{ isQuotationModuleSecondary ? 'step__card--secondary' }}" for="container-fill-convenience-true">
        {% if fillConvenience.featuredImage %}
            {% include '_parts/picture.twig' with {
                image: fillConvenience.featuredImage,
                loading: 'lazy',
                width: 80,
                height: 80,
                class: 'disposal-container-type__image'
            } %}
        {% endif %}
        <span class="disposal-container-type__label">{{ fillConvenience.label }}</span>
        <span class="disposal-container-type__subtext">{{ fillConvenience.subText }}</span>
    </label>
    <input id="container-fill-convenience-false"
        class="disposal-container-type__input"
        type="radio"
        name="fillConvenienceSelected"
        value="false"
        {{ fillConvenienceSelected == 'false' ? 'checked' : '' }} />
    <label class="disposal-container-type__item step__card {{ isQuotationModuleSecondary ? 'step__card--secondary' }}" for="container-fill-convenience-false">
        {% if withoutFillConvenience.featuredImage %}
            {% include '_parts/picture.twig' with {
                image: withoutFillConvenience.featuredImage,
                loading: 'lazy',
                width: 80,
                height: 80,
                class: 'disposal-container-type__image'
            } %}
        {% endif %}
        <span class="disposal-container-type__label">{{ withoutFillConvenience.label }}</span>
        <span class="disposal-container-type__subtext">{{ withoutFillConvenience.subText }}</span>
    </label>
</div>