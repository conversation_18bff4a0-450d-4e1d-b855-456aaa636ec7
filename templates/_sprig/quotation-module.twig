{% set entry = craft.entries.id(entryId).one() %}

{# Step disposal types #}
{% set disposalTypes = entry.disposalTypes.all() %}
{% set selectedDisposalTypes = selectedDisposalTypes ?? [] %}

{% set currentStep = 'step-disposal-types' %}

{# Step disposal types is filled if there is at least one disposal type selected #}
{% set stepDisposalTypesFilled = selectedDisposalTypes is not empty %}

{% if stepDisposalTypesFilled %}
    {% set currentStep = 'step-containers' %}
{% endif %}

{# Step containers #}
{% set disposalTypesForContainers = disposalTypes | filter(disposalType => disposalType.uid in selectedDisposalTypes) %}

{% set selectedContainersFromQuery = {} %}

{% for disposalType in disposalTypesForContainers %}
    {% set advice_selected = selectedContainers[disposalType.uid].advice is defined and selectedContainers[disposalType.uid].advice == 'advice' %}

    {% set containers = {
        advice: advice_selected ? 'advice' : false
    } %}

    {% for container in disposalType.containers %}
        {% set containerEntry = container.container.one() %}

        {% set containers = containers | merge({(containerEntry.uid): advice_selected ? 0 : selectedContainers[disposalType.uid][containerEntry.uid] ?? 0}) %}
    {% endfor %}

    {% set selectedContainersFromQuery = selectedContainersFromQuery | merge({(disposalType.uid): containers}) %}
{% endfor %}

{% set selectedContainers = selectedContainersFromQuery %}

{# Step containers is filled if there is a container/advice choice for each disposalType selected in step disposal types #}
{% set stepContainersFilled = stepDisposalTypesFilled and (selectedContainers | filter(
    disposalType => disposalType | filter(
        (container, key) => key == 'advice' ? container == 'advice' : container > 0
    ) | length
) | length) == (disposalTypesForContainers | length) %}

{% if stepContainersFilled %}
    {% set currentStep = 'step-frequencies' %}
{% endif %}

{# Step frequencies #}
{% set disposalTypesForFrequencies = disposalTypesForContainers | filter(
    disposalType => selectedContainers[disposalType.uid].advice != 'advice'
) %}

{% set selectedFrequenciesFromQuery = {} %}

{% for disposalType in disposalTypesForFrequencies %}
    {% set selectedFrequenciesFromQuery = selectedFrequenciesFromQuery | merge({(disposalType.uid): selectedFrequencies[disposalType.uid] ?? null}) %}
{% endfor %}

{% set selectedFrequencies = selectedFrequenciesFromQuery %}

{# Step frequencies is filled when every disposalType that has containers selected in step containers also has a frequency/advice selected #}
{% set stepFrequenciesFilled = stepDisposalTypesFilled and stepContainersFilled and (selectedFrequencies | filter(selectedFrequency => selectedFrequency is not null) | length) == (disposalTypesForFrequencies | length) %}

{% if stepFrequenciesFilled %}
    {% set currentStep = 'step-form' %}
{% endif %}

{# Summary #}
{% set summaryItems = [] %}

{% if (selectedDisposalTypes | length) and selectedDisposalTypes[0] == 'advice' %}
    {% set summaryItems = ['advice'] %}
{% endif %}

{% for disposalType in disposalTypesForContainers %}
    {% set summaryItems = summaryItems | merge([{
        disposalType: disposalType
    }]) %}
{% endfor %}

{% if selectedContainers is not empty  %}
    {% set newSummaryItems = [] %}

    {% for summaryItem in summaryItems %}
        {% set selectedContainerObjects = [] %}
        {% set selectedContainerAmountsForType = selectedContainers[summaryItem.disposalType.uid] %}

        {% for key, amount in selectedContainerAmountsForType %}
            {% set selectedContainerObject = [] %}

            {% if key == 'advice' and amount == 'advice' %}
                {% set selectedContainerObjects = 'advice' %}
            {% elseif amount > 0 %}
                {% set selectedContainerObject = {
                    container: (summaryItem.disposalType.containers | filter(container => container.container.one().uid == key) | first),
                    amount: amount
                } %}
            {% endif %}

            {% if selectedContainerObject %}
                {% set selectedContainerObjects = selectedContainerObjects | merge([selectedContainerObject]) %}
            {% endif %}
        {% endfor %}

        {% set summaryItem = summaryItem | merge({selectedContainers: selectedContainerObjects}) %}
        {% set newSummaryItems = newSummaryItems | merge([summaryItem]) %}
    {% endfor %}

    {% set summaryItems = newSummaryItems %}
{% endif %}

{% if selectedFrequencies is not empty %}
    {% set newSummaryItems = [] %}

    {% for summaryItem in summaryItems %}
        {% set selectedFrequencyForDisposalType = '' %}

        {% if summaryItem.selectedContainers == 'advice' or selectedFrequencies[summaryItem.disposalType.uid] == 'advice' %}
            {% set selectedFrequencyForDisposalType = 'advice' %}
        {% elseif selectedFrequencies[summaryItem.disposalType.uid] is not null %}
            {% set frequencyEntry = summaryItem.disposalType.emptyingFrequencies.uid(selectedFrequencies[summaryItem.disposalType.uid]).one() %}
            {% set selectedFrequencyForDisposalType = frequencyEntry.styledText is not empty ? (frequencyEntry.styledText | replace('\n', ' ')) : frequencyEntry.title %}
        {% endif %}

        {% set summaryItem = summaryItem | merge({selectedFrequency: selectedFrequencyForDisposalType}) %}
        {% set newSummaryItems = newSummaryItems | merge([summaryItem]) %}
    {% endfor %}

    {% set summaryItems = newSummaryItems %}
{% endif %}

{# Success #}
{% set success = success ?? false %}
{% set showFormStep = stepDisposalTypesFilled and stepContainersFilled and stepFrequenciesFilled %}
{% set showSuccessPage = stepDisposalTypesFilled and stepContainersFilled and stepFrequenciesFilled and success %}

{# Load in the templates #}
{% extends '_sprig/quotation/' ~ (showSuccessPage ? 'success' : 'quotation') ~ '.twig' %}

{% set quotationModuleIdentifier = 'quotation--default' %}
{% set lastStepTemplate = '_sprig/quotation/default/step-form.twig' %}
{% set lastStepFields = {
    stepTitle: entry.stepForm.stepTitleForm,
    stepSubtitle: entry.stepForm.stepSubtitleForm
} %}
{% set lastStepIndex = 4 %}

{% block steps %}
    {% include '_sprig/quotation/shared/step-disposal-types.twig' with {
        active: (currentStep == 'step-disposal-types'),
        filled: stepDisposalTypesFilled,
        disabled: false,
        step: {
            stepTitle: entry.stepDisposalTypes.stepTitleDisposalTypes,
            stepSubtitle: entry.stepDisposalTypes.stepSubtitleDisposalTypes
        },
        stepIndex: 1,
    } %}
    {% include '_sprig/quotation/shared/step-containers.twig' with {
        active: (currentStep == 'step-containers'),
        filled: stepContainersFilled,
        disabled: not stepDisposalTypesFilled or selectedDisposalTypes[0] == 'advice',
        step: {
            stepTitle: entry.stepContainers.stepTitleContainers,
            stepSubtitle: entry.stepContainers.stepSubtitleContainers
        },
        stepIndex: 2,
    } %}
    {% include '_sprig/quotation/default/step-frequencies.twig' with {
        active: (currentStep == 'step-frequencies'),
        filled: stepFrequenciesFilled,
        disabled: not (stepDisposalTypesFilled and stepContainersFilled and not (disposalTypesForFrequencies | length) == 0),
        step: {
            stepTitle: entry.stepFrequencies.stepTitleDeliveryFrequencies,
            stepSubtitle: entry.stepFrequencies.stepSubtitleDeliveryFrequencies
        },
        stepIndex: 3,
    } %}
{% endblock %}

{# Prevent "unused" variable errors #}
{# twigcs use-var summaryItems #}
{# twigcs use-var showSuccessPage #}