<details class="filters__type" {% if filter or openByDefault | default(false) %}open{% endif %}>
    <summary class="filters__type-title py-4">
        {{ title }}
        {% include '_parts/icon.twig' with {
            icon: 'chevron-down',
            class: 'filters__type-title-icon'
        } %}
    </summary>
    <div class="mt-1 mb-4">
        {% for option in options %}
            <label class="filters__checkbox">
                <input type="checkbox" name="{{ name }}[]" value="{{ option.slug }}"
                        {% if option.slug in filter %}checked{% endif %}>
                {{ option.title }}
            </label>
        {% endfor %}
    </div>
</details>
