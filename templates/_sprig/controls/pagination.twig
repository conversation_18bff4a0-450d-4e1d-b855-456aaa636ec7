{# pagination #}
{% if pageInfo.totalPages > 1 %}
    <ul class="pagination container container--padding">
        {% if page > 1 %}
            {# go to previous page #}
            <li class="pagination__item pagination__item--navigation">
                <button sprig s-val:page="{{ page - 1 }}" s-push-url="?page={{ page - 1 }}"
                        class="pagination__button pagination__button--prev">
                    {% include '_parts/icon.twig' with {
                        icon: 'arrow-left',
                        class: 'pagination__button-icon'
                    } %}
                    <span class="sr-only">{{ 'to previous page' | t | capitalize }}</span>
                </button>
            </li>
        {% endif %}

        {% for i in pageInfo.getDynamicRange(5) %}
            <li class="pagination__item">
                {# current page #}
                {% if i == page %}
                    <span class="pagination__button pagination__button--current">
                    <span class="sr-only">{{ 'current page' | t | capitalize }}: </span>{{ i }}
                </span>
                {% else %}

                    {# other pages #}
                    <button sprig s-val:page="{{ i }}" class="pagination__button" s-push-url="?page={{ i }}">
                        <span class="sr-only">{{ 'go to page' | t | capitalize }} </span>{{ i }}
                    </button>
                {% endif %}
            </li>
        {% endfor %}

        {% if page < pageInfo.totalPages %}
            {# go to next page #}
            <li class="pagination__item pagination__item--navigation">
                <button sprig s-val:page="{{ page + 1 }}" s-push-url="?page={{ page + 1 }}"
                        class="pagination__button pagination__button--next">
                    {% include '_parts/icon.twig' with {
                        icon: 'arrow-right',
                        class: 'pagination__button-icon'
                    } %}
                    <span class="sr-only">{{ 'to next page' | t | capitalize }}</span>
                </button>
            </li>
        {% endif %}
    </ul>
{% endif %}