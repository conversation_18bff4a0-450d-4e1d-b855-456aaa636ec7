<details class="filters__type filters__type--dropdown">
    {% include "_sprig/controls/filter-title" with {
        title: title,
    } %}
    <div class="filters__type-form">
        {% for option in options %}
            <label class="filters__checkbox">
                <input type="checkbox" name="{{ name }}[]" value="{{ option.slug }}" {% if option.slug in filter %}checked{% endif %}>
                {{ option.title }}
            </label>
        {% endfor %}
        {% include '_parts/button.twig' with {
            component: 'button',
            label: 'Filter toepassen' | t,
            class: 'filters__type-button'
        } %}
    </div>
</details>
