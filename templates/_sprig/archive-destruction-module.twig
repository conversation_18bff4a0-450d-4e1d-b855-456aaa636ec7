{% set entry = craft.entries.id(entryId).one() %}
{% set moduleReference = entry.periodicModuleReference.one() %}

{# Step disposal types #}
{% set selectedDisposalTypes = selectedDisposalTypes ?? false %}

{# Change values of the disposal types showed on the disposaly type step depending on filled fields #}
{% set singularDisposaltype = entry.singularDisposaltype.one() %}

{% set updatedSingularDisposaltype = singularDisposaltype | merge({
    title: entry.singularDisposaltypeTitle is not empty ? entry.singularDisposaltypeTitle : singularDisposaltype.title,
    disposalTypeThumbnail: entry.singularDisposaltypeImage is not empty ? entry.singularDisposaltypeImage : singularDisposaltype.disposalTypeThumbnail
}) %}

{% set periodicDisposaltype = entry.periodicDisposaltype.one() %}

{% set updatedPeriodicDisposaltype = periodicDisposaltype | merge({
    title: entry.periodicDisposaltypeTitle is not empty ? entry.periodicDisposaltypeTitle : periodicDisposaltype.title,
    disposalTypeThumbnail: entry.periodicdisposaltypeImage is not empty ? entry.periodicdisposaltypeImage : periodicDisposaltype.disposalTypeThumbnail
}) %}

{% set disposalTypes = [updatedSingularDisposaltype, updatedPeriodicDisposaltype] %}

{% set periodicOptionSelected = selectedDisposalTypes is defined and selectedDisposalTypes == periodicDisposaltype.uid %}

{% set currentStep = 'step-disposal-types' %}

{# Step disposal types is filled if either periodic or singular is selected #}
{% set stepDisposalTypesFilled = selectedDisposalTypes is not empty %}

{% if stepDisposalTypesFilled %}
    {% set currentStep = 'step-containers' %}
{% endif %}

{# Step containers #}
{% set disposalTypesForContainers = stepDisposalTypesFilled ? periodicOptionSelected ? [periodicDisposaltype] : [singularDisposaltype] : [] %}

{% set selectedContainersFromQuery = {} %}

{% for disposalType in disposalTypesForContainers %}
    {% set advice_selected = selectedContainers[disposalType.uid].advice is defined and selectedContainers[disposalType.uid].advice == 'advice' %}

    {% set containers = {
        advice: advice_selected ? 'advice' : false
    } %}

    {% for container in disposalType.containers %}
        {% set containerEntry = container.container.one() %}

        {% set containers = containers | merge({(containerEntry.uid): advice_selected ? 0 : selectedContainers[disposalType.uid][containerEntry.uid] ?? 0}) %}
    {% endfor %}

    {% set selectedContainersFromQuery = selectedContainersFromQuery | merge({(disposalType.uid): containers}) %}
{% endfor %}

{% set selectedContainers = selectedContainersFromQuery %}

{# Step containers is filled if there is a container/advice choice for each disposalType selected in step disposal types #}
{% set stepContainersFilled = stepDisposalTypesFilled and (selectedContainers | filter(
    disposalType => disposalType | filter(
        (container, key) => key == 'advice' ? container == 'advice' : container > 0
    ) | length
) | length) == (disposalTypesForContainers | length) %}

{% if stepContainersFilled %}
    {% set currentStep = 'step-' ~ (periodicOptionSelected ? 'frequencies' : 'container-type') %}
{% endif %}

{# Step frequencies (periodic only(disposalTypesForFrequencies ? stepFrequ : false)) #}
{% set disposalTypesForFrequencies = disposalTypesForContainers | filter(
    disposalType => selectedContainers[disposalType.uid].advice != 'advice'
) %}

{% set selectedFrequenciesFromQuery = {} %}

{% for disposalType in disposalTypesForFrequencies %}
    {% set selectedFrequenciesFromQuery = selectedFrequenciesFromQuery | merge({(disposalType.uid): selectedFrequencies[disposalType.uid] ?? null}) %}
{% endfor %}

{% set selectedFrequencies = selectedFrequenciesFromQuery %}

{# Step frequencies is filled when every disposalType that has containers selected in step containers also has a frequency/advice selected #}
{% set stepFrequenciesFilled = stepDisposalTypesFilled and stepContainersFilled and (selectedFrequencies | filter(selectedFrequency => selectedFrequency is not null) | length) == (disposalTypesForFrequencies | length) %}

{% if stepFrequenciesFilled %}
    {% set currentStep = 'step-form' %}
{% endif %}

{# Step container type #}
{% set fillConvenienceSelected = fillConvenienceSelected ?? 'true' %}
{% set declarationOfDestructionSelected = declarationOfDestructionSelected ?? '' %}

{# Step container type is filled if both fillConvenienceSelected and declarationOfDestructionSelected have a choice selecred #}
{% set stepContainerTypeFilled = (fillConvenienceSelected == 'true' or fillConvenienceSelected == 'false') and (declarationOfDestructionSelected == 'true' or declarationOfDestructionSelected == 'false') %}

{% if stepContainersFilled and stepContainerTypeFilled %}
    {% set currentStep = 'step-delivery-retrieval' %}
{% endif %}

{# Step delivery and retrieval #}
{% set postalCode = postalCode ?? '' %}
{% set deliveryDate = deliveryDate ?? '' %}
{% set deliveryOption = deliveryOption ?? '' %}
{% set retrievalDate = retrievalDate ?? '' %}
{% set retrievalDateRequired = false %}
{% set transportRetrievalSelected = false %}

{% if deliveryDate != '' %}
    {% set deliveryDate = deliveryDate | date("l d F y") | capitalize %}
{% endif %}

{% if retrievalDate != '' %}
    {% set retrievalDate = retrievalDate | date("l d F y") | capitalize %}
{% endif %}

{% if deliveryOption is not empty %}
    {% for option in entry.desiredCollectionDateOptions.options %}
        {% if deliveryOption == option.uid %}
            {% if option.cost is not null and option.cost.amount > 0 %}
                {% set transportRetrievalSelected = option.cost %}
            {% endif %}
            {% if option.isChooseDirectly == '1' %}
                {% set retrievalDateRequired = true %}
            {% endif %}
        {% endif %}
    {% endfor %}
{% endif %}

{# Step delivery and retrieval is filled when all required dates and options have a selection #}
{% set stepDeliveryRetrievalFilled = postalCode is not empty and deliveryDate is not empty and deliveryOption is not empty and (retrievalDateRequired ? retrievalDate is not empty : true) %}

{# Partial filled variable for errors #}
{% set stepDeliveryRetrievalPartiallyFilled = not stepDeliveryRetrievalFilled and (postalCode is not empty or deliveryDate is not empty or deliveryOption is not empty or retrievalDate is not empty) %}

{% if stepContainersFilled and stepContainerTypeFilled and stepDeliveryRetrievalFilled %}
    {% set currentStep = 'step-form' %}
{% endif %}

{# -------------- Summary -------------- #}
{% set summaryItems = [] %}
{% set summaryCostOverview = false %}
{% set alternativeContainerThumbnail = null %}

{# Set empty summaryCostOverview if no periodic #}
{% if not periodicOptionSelected %}
    {% set summaryCostDetails = {
        environmentalSurcharge: entry.environmentalSurcharge,
        vat: entry.vat,
        declarationOfDestructionSelected: declarationOfDestructionSelected == 'true' ? entry.declarationOfDestruction.cost : false,
        baseTransportCost: entry.baseCostContainerDelivery,
        transportRetrievalSelected: transportRetrievalSelected
    } %}
{% endif %}

{% for disposalType in disposalTypesForContainers %}
    {% set summaryItems = summaryItems | merge([{
        disposalType: disposalType
    }]) %}
{% endfor %}

{% if selectedContainers is not empty %}
    {% set newSummaryItems = [] %}

    {% for summaryItem in summaryItems %}
        {% set selectedContainerObjects = [] %}
        {% set selectedContainerAmountsForType = selectedContainers[summaryItem.disposalType.uid] %}

        {% for key, amount in selectedContainerAmountsForType %}
            {% set selectedContainerObject = [] %}

            {% if key == 'advice' and amount == 'advice' %}
                {% set selectedContainerObjects = 'advice' %}
            {% elseif amount > 0 %}
                {% set selectedContainerObject = {
                    container: (summaryItem.disposalType.containers | filter(container => container.container.one().uid == key) | first),
                    amount: amount
                } %}
            {% endif %}

            {% if selectedContainerObject %}
                {% set selectedContainerObjects = selectedContainerObjects | merge([selectedContainerObject]) %}
            {% endif %}
        {% endfor %}

        {% set summaryItem = summaryItem | merge({selectedContainers: selectedContainerObjects}) %}
        {% set newSummaryItems = newSummaryItems | merge([summaryItem]) %}
    {% endfor %}

    {% set summaryItems = newSummaryItems %}

    {# Add information of the container costs if not periodical #}
    {% if not periodicOptionSelected %}
        {% set summaryDisposalTypes = [] %}

        {% for summaryItem in summaryItems %}
            {% set summaryDisposalTypes = summaryDisposalTypes | merge(summaryItem.selectedContainers | map(selectedItem => {
                entry: selectedItem.container.container.one(),
                amount: selectedItem.amount,
                cost: selectedItem.container.container.one().cost
            })) %}
        {% endfor %}

        {% set summaryCostDetails = summaryCostDetails | merge({
            containers: summaryDisposalTypes
        }) %}
    {% endif %}
{% endif %}

{% if selectedFrequencies is not empty %}
    {% set newSummaryItems = [] %}

    {% for summaryItem in summaryItems %}
        {% set selectedFrequencyForDisposalType = '' %}

        {% if summaryItem.selectedContainers == 'advice' or selectedFrequencies[summaryItem.disposalType.uid] == 'advice' %}
            {% set selectedFrequencyForDisposalType = 'advice' %}
        {% elseif selectedFrequencies[summaryItem.disposalType.uid] is not null %}
            {% set frequencyEntry = summaryItem.disposalType.emptyingFrequencies.uid(selectedFrequencies[summaryItem.disposalType.uid]).one() %}
            {% set selectedFrequencyForDisposalType = frequencyEntry.styledText is not empty ? (frequencyEntry.styledText | replace('\n', ' ')) : frequencyEntry.title %}
        {% endif %}

        {% set summaryItem = summaryItem | merge({selectedFrequency: selectedFrequencyForDisposalType}) %}
        {% set newSummaryItems = newSummaryItems | merge([summaryItem]) %}
    {% endfor %}

    {% set summaryItems = newSummaryItems %}
{% endif %}

{# Change overview if singular option is selected #}
{% if not periodicOptionSelected %}
    {% set limitRepeatInfoPerDisposalType = true %}

    {# Change container image in overview if fill convenience is set to false #}
    {% set useAlternativeContainerThumbnail = fillConvenienceSelected == 'false' %}

    {# Add a line for fill convenience to overview under the container size in the overview #}
    {% set additionalContainerInformation %}
        {% if fillConvenienceSelected is not empty %}
            <span class="summary__container-type">
                {{ fillConvenienceSelected == 'true' ? 'Met' : 'Zonder' }} vulgemak
            </span>
        {% endif %}
    {% endset %}

    {% set summaryCostOverview %}
        {% include '_sprig/quotation/archive-destruction/cost-overview.twig' with {
            summaryCostDetails: summaryCostDetails
        } %}
    {% endset %}

    {% set selectedDeliveryOption = '' %}

    {% for option in entry.desiredCollectionDateOptions.options %}
        {% if deliveryOption == option.uid %}
            {% set selectedDeliveryOption = option.label %}
        {% endif %}
    {% endfor %}

    {% set additionalSummaryInformation %}
        {% if deliveryDate is not empty %}
            <span class="summary__delivery-retrieval">{{ deliveryDate }} • {{ retrievalDateRequired ? retrievalDate : selectedDeliveryOption }}</span>
        {% endif %}
    {% endset %}
{% endif %}

{# -------------- Success -------------- #}
{% set success = success ?? false %}
{% set showFormStep = stepDisposalTypesFilled and stepContainersFilled and (periodicOptionSelected ? stepFrequenciesFilled : (stepContainerTypeFilled and stepDeliveryRetrievalFilled)) %}
{% set showSuccessPage = stepDisposalTypesFilled and stepContainersFilled and (periodicOptionSelected ? stepFrequenciesFilled : (stepContainerTypeFilled and stepDeliveryRetrievalFilled)) and success %}
{% set successPageEntry = periodicOptionSelected ? moduleReference : entry %}

{# Load in the templates #}
{% extends '_sprig/quotation/' ~ (showSuccessPage ? 'success' : 'quotation') ~ '.twig' %}

{% set isQuotationModuleSecondary = true %}
{% set quotationModuleIdentifier = 'quotation--arhive-destruction' %}
{% set lastStepTemplate = '_sprig/quotation/' ~ (periodicOptionSelected ? 'default' : 'archive-destruction') ~ '/step-form.twig' %}
{% set lastStepFields = periodicOptionSelected ? {
    stepTitle: moduleReference.stepForm.stepTitleForm,
    stepSubtitle: moduleReference.stepForm.stepSubtitleForm
} : {
    stepTitle: entry.stepForm.stepTitleForm,
    stepSubtitle: entry.stepForm.stepSubtitleForm
} %}
{% set lastStepIndex = periodicOptionSelected ? 4 : 5 %}
{% set lastStepForm = periodicOptionSelected ? moduleReference.form.one() : entry.form.one() %}
{% set removeLinkText = not periodicOptionSelected ? 'Selectie verwijderen' : null %}
{% set removeLinkRemovesAll = true %}
{% set hideContainerDetailsInSummary = not periodicOptionSelected %}

{% block steps %}
    {% include '_sprig/quotation/shared/step-disposal-types.twig' with {
        active: (currentStep == 'step-disposal-types'),
        filled: stepDisposalTypesFilled,
        hideAdvice: true,
        disabled: false,
        singleSelect: true,
        smallImages: true,
        step: {
            stepTitle: entry.stepDisposalTypes.stepTitleDisposalTypes,
            stepSubtitle: entry.stepDisposalTypes.stepSubtitleDisposalTypes
        },
        stepIndex: 1,
    } %}
    {% include '_sprig/quotation/shared/step-containers.twig' with {
        active: (currentStep == 'step-containers'),
        filled: stepContainersFilled,
        disabled: not stepDisposalTypesFilled,
        step: periodicOptionSelected ? {
            stepTitle: moduleReference.stepContainers.stepTitleContainers,
            stepSubtitle: moduleReference.stepContainers.stepSubtitleContainers
        } : {
            stepTitle: entry.stepContainers.stepTitleContainers,
            stepSubtitle: entry.stepContainers.stepSubtitleContainers
        },
        hideTitles: true,
        hideAdvice: not periodicOptionSelected,
        alternateContentsText: not periodicOptionSelected,
        maxContainersPerType: periodicOptionSelected ? false : 12,
        showContainerCost: periodicOptionSelected ? false : true,
        stepIndex: 2,
    } %}
    {% if periodicOptionSelected %}
        {% include '_sprig/quotation/default/step-frequencies.twig' with {
            active: (currentStep == 'step-frequencies'),
            filled: stepFrequenciesFilled,
            disabled: not (stepDisposalTypesFilled and stepContainersFilled and not (disposalTypesForFrequencies | length) == 0),
            step: {
                stepTitle: moduleReference.stepFrequencies.stepTitleDeliveryFrequencies,
                stepSubtitle: moduleReference.stepFrequencies.stepSubtitleDeliveryFrequencies
            },
            stepIndex: 3,
        } %}
    {% else %}
        {% include '_sprig/quotation/archive-destruction/step-container-type.twig' with {
            active: (currentStep == 'step-container-type'),
            filled: stepContainerTypeFilled,
            disabled: not (stepDisposalTypesFilled and stepContainersFilled),
            step: {
                stepTitle: entry.stepContainerType.stepTitleContainerType,
                stepSubtitle: entry.stepContainerType.stepSubtitleContainerType
            },
            stepIndex: 3,
        } %}
        {% include '_sprig/quotation/archive-destruction/step-delivery-and-retrieval.twig' with {
            active: (currentStep == 'step-delivery-retrieval'),
            filled: stepDeliveryRetrievalFilled,
            disabled: not (stepDisposalTypesFilled and stepContainersFilled and stepContainerTypeFilled),
            step: {
                stepTitle: entry.stepDeliveryAndRetrieval.stepTitleDeliveryDate,
                stepSubtitle: entry.stepDeliveryAndRetrieval.stepSubtitleDeliveryDate
            },
            stepIndex: 4,
        } %}
    {% endif %}
{% endblock %}

{# Prevent "unused" variable errors #}
{# twigcs use-var summaryItems #}
{# twigcs use-var showSuccessPage #}
