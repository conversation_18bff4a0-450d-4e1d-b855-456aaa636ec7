{% set entry = craft.entries.id(entryId).one() %}
{% set category = categoryId ? craft.categories().group('articleType').id(categoryId).one : null %}

{% if category %}
    {% set articleType = category.slug %}
{% endif %}

{# set variable for sprig to hook into with default for first page load #}
{% set page = page ?? 1 %}
{% set LIMIT_PER_PAGE = 12 %}

{# need above values in hidden inputs for persistent state  #}
{{ hiddenInput('page', page) }}

{% set query = craft.entries().section('article').with(['thumbnail', 'articleType', 'disposalType', 'companyType', 'themeType']).orderBy('postDate DESC').limit(LIMIT_PER_PAGE) %}

{# ----- Search START ---- #}
{% set search = s ?? '' %}
{% if search %}
    {% set query = query.search(search) %}
{% endif %}
{# ----- Search END ---- #}

{# ----- Type START ----- #}
{% set articleType = articleType ?? '' %}
{% set typeOptions = craft.categories().group('articleType').orderBy('title').all() %}
{% set typeEntries = typeOptions | where(i => i.slug in articleType) | unique %}
{% if typeEntries %}
    {% set query = query.andRelatedTo(typeEntries) %}
{% endif %}
{# ----- Type END ----- #}

{# ----- Disposal type START ----- #}
{% set disposalType = disposalType ?? '' %}
{% set disposalOptions = craft.categories().group('disposalType').orderBy('title').all() %}
{% set disposalEntries = disposalOptions | where(i => i.slug in disposalType) | unique %}
{% if disposalEntries %}
    {% set query = query.andRelatedTo(disposalEntries) %}
{% endif %}
{# ----- Disposal type END ----- #}

{# ----- Company type START ----- #}
{% set companyType = companyType ?? '' %}
{% set companyOptions = craft.categories().group('companyType').orderBy('title').all() %}
{% set companyEntries = companyOptions | where(i => i.slug in companyType) | unique %}
{% if companyEntries %}
    {% set query = query.andRelatedTo(companyEntries) %}
{% endif %}
{# ----- Company type END ----- #}

{# ----- Theme type START ----- #}
{% set themeType = themeType ?? '' %}
{% set themeOptions = craft.categories().group('themeType').orderBy('title').all() %}
{% set themeEntries = themeOptions | where(i => i.slug in themeType) | unique %}
{% if themeEntries %}
    {% set query = query.andRelatedTo(themeEntries) %}
{% endif %}
{# ----- Theme type END ----- #}

{% set pageInfo = sprig.paginate(query, page) %}
{% set newsEntries = pageInfo.pageResults %}

{% set filters = {
    'articleType': articleType is iterable ? articleType | unique : articleType,
    'disposalType': disposalType is iterable ? disposalType | unique : disposalType,
    'companyType': companyType is iterable ? companyType | unique : companyType,
    'themeType': themeType is iterable ? themeType | unique : themeType,
} %}

{% set quickLinks = entry.quickLinks.with(['linkItem']).all() %}

{# twigcs use-var filters #}
{% do sprig.pushUrl('?' ~ remove_empty(filters) | url_encode) %}

<form id="news-filters" s-replace="#entries" sprig>
    <header class="relative flex justify-center pt-[4.75rem] pb-[5.75rem] overflow-hidden mb-5 lg:mb-10">
        <div class="flex items-center text-center flex-col px-6 max-w-[43.5rem]">
            <h1 class="mb-2">{{ category.title ?? entry.title }}</h1>
            {{ entry.description }}

            {% if quickLinks and quickLinks | length %}
                <div class="mt-6 gap-4 w-full grid grid-cols-2 md:flex flex-wrap justify-center">
                    {% for link in quickLinks %}
                        {% include "_parts/keyvisual/quick-link" with {
                            link: link.linkItem.getUrl(),
                            icon: (link.icon and link.icon is iterable and link.icon.label) ? link.icon.label : null,
                            label: link.linkItem.getText()
                        } %}
                    {% endfor %}
                </div>
            {% endif %}
            <div class="mt-6 w-full relative">
                <input
                    type="search"
                    name="s"
                    placeholder="{{ 'What are you looking for?' | t }}"
                    class="w-full h-[3.5rem] bg-white border border-tertiary-300 p-4 pr-16 text-18 rounded"
                    value="{{ search }}"
                />
                <button type="submit" class="flex items-center justify-center absolute top-0 rounded-r right-0 w-[3.5rem] h-[3.5rem] text-primary-500">
                    <span class="sr-only">{{ 'Search' | t }}</span>
                    {% include '_parts/icon' with {
                        icon: 'search',
                        class: 'text-24'
                    } %}
                </button>
            </div>
        </div>
        <span class="w-full absolute left-1/2 top-0 -translate-x-1/2 -z-1 scale-150 lg:w-2/3 lg:-translate-y-1/3">
            <img class="block aspect-square w-full blur-[105px] motion-safe:animate-spin-slow" alt="" width="0" height="0" src="/dist/images/keyvisual/background.svg" />
        </span>
    </header>
</form>

<div class="news__content container container--padding grid grid-cols-12 gap-8">
    <form
        x-data="{
            open:false
        }"
        class="
            col-span-full relative
            md-max:sticky md-max:top-20
            lg:col-span-3
        "
        :class="{
            'md-max:z-50': open,
        }"
        id="news-filters-list"
        s-replace="#entries"
        sprig
    >
        <button
            class="
                border-primary-500 w-full py-2 text-primary-500 border bg-white px-4 rounded
                lg:hidden
            "
            type="button"
            @click="open = !open"
            aria-controls="news-filters-popup"
            :aria-expanded="open"
        >Filters</button>
        <div
            class="
                fixed left-0 top-0 w-full h-full bg-black/50 z-40
                lg:hidden
            "
            :class="{
                'md-max:hidden': !open,
            }"
            @click="open = false"
        ></div>
        <div
            class="
                news__filters filters
                md-max:fixed md-max:top-1/2 md-max:left-1/2 md-max:-translate-x-1/2 md-max:-translate-y-1/2 md-max:w-[calc(100vw-3rem)] md-max:z-50
            "
            id="news-filters-popup"
            :class="{
                'md-max:hidden': !open,
            }"
        >
            <div class="filter__items">
                {% include "_sprig/controls/filter-title" with {
                    title: 'Filters' | t,
                    class: 'filters__type-title--big'
                } %}
                <div class="filters__type-form [&>_:not([hidden])]:border-t border-solid border-black">
                    {% if not category and typeOptions | length %}
                        <div>
                            {% include '_sprig/controls/filter-type-fieldset' with {
                                options: typeOptions,
                                name: 'articleType',
                                filter: articleType,
                                title: 'Type' | t,
                                openByDefault: remove_empty(filters) | length == 0,
                            } %}
                        </div>
                    {% endif %}

                    {% if disposalOptions | length %}
                        <div>
                            {% include '_sprig/controls/filter-type-fieldset' with {
                                options: disposalOptions,
                                name: 'disposalType',
                                filter: disposalType,
                                title: 'Afvalstroom' | t,
                                openByDefault: category is defined,
                            } %}
                        </div>
                    {% endif %}

                    {% if companyOptions | length %}
                        <div>
                            {% include '_sprig/controls/filter-type-fieldset' with {
                                options: companyOptions,
                                name: 'companyType',
                                filter: companyType,
                                title: 'Bedrijfstype' | t
                            } %}
                        </div>
                    {% endif %}

                    {% if themeOptions | length %}
                        <div>
                            {% include '_sprig/controls/filter-type-fieldset' with {
                                options: themeOptions,
                                name: 'themeType',
                                filter: themeType,
                                title: 'Thema' | t
                            } %}
                        </div>
                    {% endif %}
                </div>
                <button
                    class="
                        absolute right-1 top-1 w-12 h-12 flex items-center justify-center
                        lg:hidden
                    "
                    @click="open = false"
                    aria-controls="news-filters-popup"
                    :aria-expanded="open"
                >
                    <span class="sr-only">{{ 'Close' | t }}</span>
                    {% include '_parts/icon' with {
                        icon: 'close',
                        class: 'text-24'
                    } %}
                </button>
            </div>
            <button id="filter-submit-button" class="hidden invisible">submit</button>
        </div>
    </form>

    <div class="col-span-full lg:col-span-9">
        <div id="entries">
            {% if articleType or disposalType or companyType or themeType %}
                <a
                    href="{{ entry.url }}"
                    class="inline-block mb-4 text-base text-primary-500 underline"
                >{{ 'Clear all filters' | t }}</a>
            {% endif %}
            {% if newsEntries | length %}
                {% include "_parts/news/set" with {
                    "newsEntries": newsEntries,
                } %}
            {% else %}
                <div class="bg-white p-6 rounded">
                    <h4>{{ 'no results found' | t | capitalize }}</h4>
                </div>
            {% endif %}
            {% include "_sprig/controls/pagination" with {
                pageInfo: pageInfo,
            } %}
        </div>
    </div>
</div>

<script>
    const details = document.querySelectorAll("details");
    const closeButtons = document.querySelectorAll("[data-close]");

    // Close details before opening other detail element
    details.forEach((i) => {
        i.addEventListener('click', (e) => {
            Array.from(details).map((y) => y !== e.currentTarget && closeDetails(y));
        });
    });

    closeButtons.forEach((i) => {
        i.addEventListener('click', (e) => {
            if (e.target.closest('details').hasAttribute('open')) {
                e.target.closest('details').removeAttribute('open');
            }
        });
    });

    const filterWrapper = document.getElementById('news-filters-list');
    const submitButton = document.getElementById('filter-submit-button');
    const filterOptions = filterWrapper.querySelectorAll('input');
    filterOptions.forEach((i) => {
      i.addEventListener('change', (e) => {
        submitButton.click();
      })
    });

    // Close details on escape
    window.addEventListener("keydown", (e) => {
        if (e.key === "Escape") {
            Array.from(details).map(closeDetails);
        }
    });

    const closeDetails = (i) => i.removeAttribute("open");
</script>
