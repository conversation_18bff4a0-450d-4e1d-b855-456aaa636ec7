<script>
    const form = document.querySelector('.step__form').querySelector('form')
    const uuidInput = form.querySelector('[name="fields[uuidinput]"]')
    uuidInput.value = localStorage.getItem('formUuid');

    form.addEventListener('onAfterFormieSubmit', (e) => {
        e.preventDefault()

        if (e.detail.nextPageId) {
            return;
        }

        let email = form.querySelector('[name="fields[emailAdres]"]')
        let phonenumber = form.querySelector('[name="fields[telefoonnummer]"]')
        let submissionId = e.detail.submissionId

        const response = pushFormSubmissionToDatalayer({
            submissionId,
            formType: '{{ formType }}',
            formId: '{{ formId }}',
            email: email ? email.value : '',
            phonenumber: phonenumber ? phonenumber.value : '',
            postalCode: '{{ postalCode }}',
            total: '{{ total }}',
            shippingCost: '{{ shippingCost }}',
            containerCost: '{{ containerCost }}',
            declarationOfDestructionCost: '{{ declarationOfDestructionCost }}',
            transportCost: '{{ transportCost }}',
            environmentalSurcharge: '{{ environmentalSurcharge }}',
            vat: '{{ vat }}'
        });

        response.then(res => {
            if (res) {
                const urlWithoutSubmission = window.location.href
                .split('&')
                .filter(string => !string.includes('submission'))
                .join('&')
                window.location.replace(urlWithoutSubmission + '&success=true')
            }
        }).catch(error => {
            console.log(error);
        })
    });

    const pushFormSubmissionToDatalayer = async (data) => {
        return new Promise(resolve => {
            window.dataLayer = window.dataLayer || [];

            const formUuid = localStorage.getItem('formUuid') || ''

            dataLayer.push({
                event: 'archiveFormSubmission',
                formUuid: formUuid,
                ...data
            });

            resolve(true)
        });
    }
</script>