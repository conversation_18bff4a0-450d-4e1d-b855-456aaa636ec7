<script>
    window.onload = () => {
        const postalCodeEventButton = document.querySelector('.postal-code-event-trigger')
        const form = document.querySelector('[s-replace="#quotation-module"]')

        if (postalCodeEventButton) {
            postalCodeEventButton.addEventListener('click', async (e) => {
                e.preventDefault();

                const postalCode = document.querySelector('[name="postalCode"]')
                const deliveryDate = document.querySelector('[name="deliveryDate"]')
                const retrievalDate = document.querySelector('[name="retrievalDate"]')

                 const response = pushPostalCodeEventToDatalayer({
                    postalCode: postalCode ? postalCode.value : '',
                    deliveryDate: deliveryDate ? deliveryDate.value : '',
                    retrievalDate: retrievalDate ? retrievalDate.value : ''
                });

                response.then(res => {
                    if (res) {
                        form.submit();
                    }
                }).catch(error => {
                    console.log(error);
                })
            });
        }

        const pushPostalCodeEventToDatalayer = async (data) => {
            return new Promise(resolve => {
                window.dataLayer = window.dataLayer || [];

                dataLayer.push({
                    event: 'postalCodeEvent',
                    ...data
                });

                resolve(true);
            });
        }
    }
</script>