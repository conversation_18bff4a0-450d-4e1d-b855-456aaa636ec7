{% extends "_layout/base.twig" %}

{% set errorContent = {
    keyVisualType: 'media',
    keyVisualAlignment: 'simple',
    keyVisualTitle: error404.keyVisualTitle,
    keyVisualHighlightedWords: error404.keyVisualHighlightedWords,
    keyVisualImage: error404.keyVisualImage,
    keyVisualHideScrollText: true,
    keyVisualCloseContentGap: false,
    keyVisualIntro: null,
    keyVisualVideo: null,
    keyVisualScrollText: null
} %}
{# twigcs use-var entry #}

{% block content %}
    {% include '_parts/keyvisual' with {
        entry: errorContent
    } %}
{% endblock %}
