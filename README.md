# Craft CMS template

## Installation

1. Clone this repo to your local machine.
2. In the project root run `make up` to start.

### Make commands

| Commands               | Description                                                            |
| ---------------------- | ---------------------------------------------------------------------- |
| make up                | Starts ddev and installs all that is needed                            |
| make dev               | Start vite dev server                                                  |
| make build             | Execute the frontend build                                             |
| make composer $command | Used to execute composer commands inside ddev `make composer update`   |
| make craft $command    | Used to execute composer commands inside ddev `make craft migrate/all` |
| make pull              | Sync the environment using Pullit                                      |
| make apply             | Execute craft project-config/apply                                     |

## Useful links

| Link title                                      | Description                 |
| ----------------------------------------------- | --------------------------- |
| [Documentation](https://craftcms.com/docs/4.x/) | Craft CMS 4.x Documentation |

## Environments

| Environment | URL                                                    | Branch   |
| ----------- | ------------------------------------------------------ | -------- |
| Test        | https://test.milieuservicenederland.nl.kiwicloud.nl/   | `test`   |
| Accept      | https://accept.milieuservicenederland.nl.kiwicloud.nl/ | `accept` |
