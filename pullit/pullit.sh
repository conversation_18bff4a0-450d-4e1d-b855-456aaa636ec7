#/bin/bash
#title              : pullit.sh
#description        : This script will download the latest backup database dump and import it locally,
#                     or download live database dump if --live flag is provided.
#author             : <PERSON><PERSON> <<EMAIL>>
#date               : 14-05-2025
#version            : 0.6.0
#usage              : bash pullit.sh [--live]
#==============================================================================

RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

function green() {
  printf "${GREEN}$*${NC}\n"
}

function red() {
  printf "${RED}$*${NC}\n"
}

if ! [ -x "$(command -v jq)" ]; then
  red "Error: jq is not installed."
  green "Install this using: \`brew install jq\`" >&2
  exit 1
fi
/usr/bin/clear

# Default flags
USE_LIVE_DUMP=false
export PROJECT_FILE=production.project.json

# Parse flags
while true; do
  case "$1" in
    --test )
      export PROJECT_FILE=test.project.json
      shift
      ;;
    --accept )
      export PROJECT_FILE=accept.project.json
      shift
      ;;
    --production )
      export PROJECT_FILE=production.project.json
      shift
      ;;
    --live )
      USE_LIVE_DUMP=true
      shift
      ;;
    --)
      shift
      break
      ;;
    -?*)
      printf 'WARN: Unknown option in pullit command (ignored): %s\n' "$1" >&2
      shift
      ;;
    *)
      break
      ;;
  esac
done

# ------------------------------------------------------------------------------
# SET VARIABLES
# ------------------------------------------------------------------------------
export EXTERNAL_MYSQL_USER=$(jq -r .externalDatabaseUser $PROJECT_FILE)
export LOCAL_MYSQL_DBNAME=$(jq -r .localDatabaseName $PROJECT_FILE)
export REMOTE_USERNAME=$(jq -r .remoteUsername $PROJECT_FILE)
export REMOTE_HOST_IP=$(jq -r .remoteHostIp $PROJECT_FILE)
export REMOTE_PATH=$(jq -r .remotePath $PROJECT_FILE)
export TOWER_USERNAME=$(jq -r .towerUsername ~/.pullit.json)
export TOWER=$<EMAIL>

green "Connecting over SSH with user $TOWER_USERNAME"
echo "------------------------------------------------"

if [ "$USE_LIVE_DUMP" = true ]; then
  green "Downloading live database dump for user $EXTERNAL_MYSQL_USER"
  # Retrieve password
  export DB_PASSWORD=$(ssh -o StrictHostKeyChecking=no -o LogLevel=QUIET -tJ $TOWER $TOWER_USERNAME@$REMOTE_HOST_IP "sudo -u $REMOTE_USERNAME -i cat /etc/my.$EXTERNAL_MYSQL_USER.pass")
  if [ -z "$DB_PASSWORD" ]; then
    red "Failed to retrieve database password."
    exit 1
  fi

  ssh -o StrictHostKeyChecking=no -o LogLevel=QUIET -C -tJ $TOWER $TOWER_USERNAME@$REMOTE_HOST_IP "export MYSQL_PWD=$DB_PASSWORD; mysqldump -u$EXTERNAL_MYSQL_USER $REMOTE_USERNAME" --no-tablespaces > db_dump.sql

  if [ $? -ne 0 ]; then
    red "Failed to download live database dump."
    exit 1
  fi

  green "Importing live database dump into DDEV database '$LOCAL_MYSQL_DBNAME'..."
  ddev import-db --file=./db_dump.sql

  if [ $? -ne 0 ]; then
    red "Database import failed."
    exit 1
  fi

  green "Import done, removing dumpfile."
  rm -f db_dump.sql

else
  # Use backup file
  REMOTE_BACKUP_PATH="/home/<USER>/mysql-backup-${REMOTE_USERNAME}.sql.gz"

  green "Downloading latest backup database dump for user $EXTERNAL_MYSQL_USER"
  scp -o StrictHostKeyChecking=no -o LogLevel=QUIET -J $TOWER $TOWER_USERNAME@$REMOTE_HOST_IP:$REMOTE_BACKUP_PATH ./mysql-backup.sql.gz

  if [ $? -ne 0 ]; then
    red "Failed to download backup file from $REMOTE_BACKUP_PATH"
    exit 1
  fi

  green "Decompressing backup file..."
  gunzip -f ./mysql-backup.sql.gz

  if [ $? -ne 0 ]; then
    red "Failed to decompress mysql-backup.sql.gz"
    exit 1
  fi

  green "Importing database dump into DDEV database '$LOCAL_MYSQL_DBNAME'..."
  ddev import-db --file=./mysql-backup.sql

  if [ $? -ne 0 ]; then
    red "Database import failed."
    exit 1
  fi

  green "Import done, removing dumpfile."
  rm -f ./mysql-backup.sql
fi

echo "------------------------------------------------"

green "Importing files from project"
for key in $(jq -r .paths[] $PROJECT_FILE); do
  rsync --exclude="*."{zip,docx,pdf,pptx,docx,xls,xlsx} -azvL -e "ssh -o StrictHostKeyChecking=no -o LogLevel=QUIET -J $TOWER" $TOWER_USERNAME@$REMOTE_HOST_IP:$REMOTE_PATH$key ../$key
done
echo "------------------------------------------------"
