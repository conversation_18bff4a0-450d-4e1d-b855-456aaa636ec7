name: msn
type: craftcms
docroot: web
php_version: "8.1"
webserver_type: nginx-fpm
router_http_port: "80"
router_https_port: "443"
xdebug_enabled: false
additional_hostnames: []
additional_fqdns: []
database:
  type: mariadb
  version: "10.4"
hooks:
  post-start:
  - exec-host: ddev init
project_tld: nl.internal
upload_dirs:
  - uploads
use_dns_when_possible: true
composer_version: "2"
web_environment: []
nodejs_version: "20"
