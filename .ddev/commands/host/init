#!/bin/bash

## Description: Initializes the project
## Usage: init
## Example: "ddev init"

cd "${BASH_SOURCE%/*}/.." || exit

LOG=${DDEV_APPROOT}/.ddev/.init

if [ -e $LOG ]
then
    echo "Will not execute init script on the host system again (remove log file $LOG to re-execute)"
else
    echo "Running init script on the host system (all output below is written to $LOG)"

    date | tee -a $LOG
    echo "Start script" | tee -a $LOG
    cd ${DDEV_APPROOT}

    #
    # Clone .dist files
    #
    echo "> Cloning .dist files..." | tee -a $LOG
    cp ./.env.example ./.env | tee -a $LOG

    #
    # Perform database and file sync
    #
    echo "> Performing sync..." | tee -a $LOG
    ddev sync | tee -a $LOG
    echo "> Sync complete." | tee -a $LOG

    #
    # Install composer packages
    #
    echo "> Installing composer packages..." | tee -a $LOG
    ddev composer install | tee -a $LOG
    echo "> Composer complete." | tee -a $LOG

    #
    # Create frontend files
    #
    echo "> Creating frontend..." | tee -a $LOG
    ddev npm install | tee -a $LOG
    ddev npm run build | tee -a $LOG
    echo "> Frontend complete." | tee -a $LOG

    date | tee -a $LOG
    echo "End script" | tee -a $LOG
    echo "All above output was written to $LOG"
fi
