{"name": "@redkiwi/msn", "version": "0.0.1", "dependencies": {"@typeform/embed": "^5.5.0", "air-datepicker": "^3.5.3", "alpinejs": "^3.14.9", "lazysizes": "^5.3.2", "sass": "1.66.1", "swiper": "^11.2.6", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-manifest-sri": "^0.2.0", "vite-plugin-restart": "^0.4.2"}, "engines": {"node": ">=16"}, "scripts": {"build": "NODE_ENV=production vite build", "serve": "NODE_ENV=dev vite", "prettier": "prettier --write .", "migrate": "sass-migrator module --migrate-deps src/scss/**/*.scss"}}