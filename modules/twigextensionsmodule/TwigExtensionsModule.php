<?php
/**
 * TwigExtensionsModule module for Craft CMS 3.x
 *
 * A module to add functions and filters to Twig
 *
 * @link      https://www.redkiwi.nl/
 * @copyright Copyright (c) 2022 Alex Blackburn
 */

namespace modules\twigextensionsmodule;

use Craft;
use modules\twigextensionsmodule\twigextensions\GetCurrentReviewsTwigExtension;
use modules\twigextensionsmodule\twigextensions\GetVersionedFileTwigExtension;
use modules\twigextensionsmodule\twigextensions\GetVideoInfoTwigExtension;
use modules\twigextensionsmodule\twigextensions\GetWoocommerceRestApiTwigExtension;
use modules\twigextensionsmodule\twigextensions\RemoveEmptyTwigExtension;
use yii\base\Module;

/**
 * <AUTHOR>
 * @package   TwigExtensionsModule
 * @since     1.0.0
 */
class TwigExtensionsModule extends Module
{
    /**
     * Static property that is an instance of this module class so that it can be accessed via
     * TwigExtensionsModule::$instance
     *
     * @var TwigExtensionsModule
     */
    public static TwigExtensionsModule $instance;

    /**
     * @param string $id
     * @param Module $parent
     * @param mixed[] $config
     */
    public function __construct($id, $parent = null, array $config = [])
    {
        Craft::setAlias('@modules/twigextensionsmodule', $this->getBasePath());

        // Set this as the global instance of this module class
        static::setInstance($this);

        parent::__construct($id, $parent, $config);
    }

    /**
     * Initialize the module
     */
    public function init(): void
    {
        parent::init();
        self::$instance = $this;

        // Add in our Twig extensions
        Craft::$app->view->registerTwigExtension(new GetCurrentReviewsTwigExtension());
        Craft::$app->view->registerTwigExtension(new GetVersionedFileTwigExtension());
        Craft::$app->view->registerTwigExtension(new GetVideoInfoTwigExtension());
        Craft::$app->view->registerTwigExtension(new RemoveEmptyTwigExtension());
        Craft::$app->view->registerTwigExtension(new GetWoocommerceRestApiTwigExtension());
    }
}
