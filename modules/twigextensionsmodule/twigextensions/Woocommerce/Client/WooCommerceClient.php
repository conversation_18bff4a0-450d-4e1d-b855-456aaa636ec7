<?php

declare(strict_types=1);

namespace modules\twigextensionsmodule\twigextensions\Woocommerce\Client;

use Automattic\WooCommerce\Client;

class WooCommerceClient
{
    private Client $client;

    private string $version = 'wc/v3';

    public function __construct()
    {
        $this->client = new Client(
            getenv('WOOCOMMERCE_REST_API_URL') ? getenv('WOOCOMMERCE_REST_API_URL') : '' ,
            getenv('WOOCOMMERCE_REST_API_CONSUMER_KEY') ? getenv('WOOCOMMERCE_REST_API_CONSUMER_KEY') : '',
            getenv('WOOCOMMERCE_REST_API_CONSUMER_SECRET') ? getenv('WOOCOMMERCE_REST_API_CONSUMER_SECRET') :'',
            [
                'version' => $this->version
            ]
        );
    }

    public function __invoke(): Client
    {
        return $this->client;
    }

    public function getClient(): Client
    {
        return $this->client;
    }
}
