<?php

declare(strict_types=1);

namespace modules\twigextensionsmodule\twigextensions\Woocommerce\Helper;

class WoocommerceHelper
{
    /**
     * @var array|string[]
     */
    public array $productAttributes = ['3m', '6m', '10m', '10md'];

    /**
     * @param array<int, \stdClass, string, float> $products
     * @param int $taxRate
     * @param array $categories
     * @return array
     * @phpstan-ignore-next-line
     */
    public function getProductsListWithAttributes(array|\stdClass $products, int $taxRate, array $categories): array
    {
        $productList = [];
        if (!empty($products)) {
            $categoryName = '';
            $categoryId = 0;
            /** @phpstan-ignore-next-line */
            foreach ($products as $product) {
                foreach ($product as $key => $value) {
                    if ($key === 'status' && $value !== 'publish') {
                        continue;
                    }
                    if ($key === 'categories') {
                        foreach ($value as $category) {
                            $categoryName = $category->name;
                            $categoryId = $category->id;
                        }
                    }
                    if ($key === 'attributes') {
                        foreach ($value as $attribute) {
                            if ($attribute->name === 'product_type') {
                                foreach ($attribute->options as $option) {
                                    foreach ($this->productAttributes as $productAttribute) {
                                        if ($option === $productAttribute) {
                                            $tax = ($product->price * $taxRate) / (100 + $taxRate);
                                            $ex_tax = $product->price - $tax;
                                            $productList[$productAttribute][$this->getCategoryMenuOrder($categories, $categoryId)] = [
                                                'id' => $product->id,
                                                'price' => number_format((float)$product->price, 2, ','),
                                                'tax' => number_format($tax, 2, ','),
                                                'ex_tax' => number_format($ex_tax, 2, ','),
                                                'name' => $product->name,
                                                'category' => $categoryName,
                                            ];
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return $this->orderByCategoryMenuOrder($productList);
    }

    /**
     * @param array<int, \stdClass> $taxes
     * @return int
     */
    public function getTaxRate(array $taxes): int
    {
        $rate = 0;
        if (!empty($taxes)) {
            foreach ($taxes as $tax) {
                if ($tax->rate > 0) {
                    $rate = (int)$tax->rate;
                }
            }
        }

        return $rate;
    }

    /**
     * @param array<string, array<int, array<string, mixed>>> $productList
     * @return array
     * @phpstan-ignore-next-line
     */
    public function orderByCategoryMenuOrder(array $productList): array
    {
        $newList = [];
        if (!empty($productList)) {
            foreach ($productList as $productAttribute => $products) {
                ksort($products);
                $newList[$productAttribute] = $products;
            }
        }

        return $newList;
    }

    /**
     * @param array<int, \stdClass> $categories
     * @param int $id
     * @return int
     */
    public function getCategoryMenuOrder(array $categories, int $id): int
    {
        $menuOrder = 0;
        foreach ($categories as $category) {
            if ($id === $category->id) {
                $menuOrder = (int)$category->menu_order;
            }
        }

        return $menuOrder;
    }
}
