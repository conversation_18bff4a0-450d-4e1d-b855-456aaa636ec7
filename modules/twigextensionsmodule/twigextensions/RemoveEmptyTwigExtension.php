<?php
/**
 * RemoveEmptyTwigExtension module for Craft CMS 4.x
 *
 * A module to remove empty items from an array.
 *
 * @link      https://www.redkiwi.nl/
 * @copyright Copyright (c) 2022 Jen<PERSON> de <PERSON>d
 */

namespace modules\twigextensionsmodule\twigextensions;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

/**
 * RemoveEmptyTwigExtension
 *
 * <AUTHOR>
 * @package   TwigExtensionsModule
 * @since     1.0.0
 */
class RemoveEmptyTwigExtension extends AbstractExtension
{
    /**
     * Returns the name of the extension.
     */
    public function getName(): string
    {
        return 'RemoveEmpty';
    }

    /**
     * Returns an array of Twig functions, used in Twig templates via:
     * {% set this = remove_empty(arrayOfItems) %}
     *
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('remove_empty', [$this, 'removeEmpty']),
            new TwigFunction('empty_array_values', [$this, 'emptyArrayValues']),
        ];
    }

    /**
     * Remove empty items from an array
     *
     * @param mixed[] $items
     * @return mixed[]
     */
    public function removeEmpty(array $items): array
    {
        foreach ($items as $key => $value) {
            if (empty($value)) {
                unset($items[$key]);
            }
        }

        return $items;
    }

    /**
     * Remove values from array items
     *
     * @param mixed[] $items
     * @return mixed[]
     */
    public function emptyArrayValues(array $items): array
    {
        foreach ($items as $key => $value) {
            if (!empty($value)) {
                $items[$key] = '';
            }
        }

        return $items;
    }
}
