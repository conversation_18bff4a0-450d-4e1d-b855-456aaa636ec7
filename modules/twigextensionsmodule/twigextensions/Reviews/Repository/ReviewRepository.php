<?php

declare(strict_types=1);

namespace modules\twigextensionsmodule\twigextensions\Reviews\Repository;

use GuzzleHttp\Client;
use modules\twigextensionsmodule\twigextensions\Reviews\Client\KiyohClient;

class ReviewRepository
{
    private Client $client;

    public function __construct()
    {
        $this->client = (new KiyohClient())();
    }

    public function fetchAll(): \StdClass
    {
        $request = $this->client->get('v1/review/feed.json', [
            'query' => [
                'hash' => getenv('KIYOH_HASH'),
            ],
            'timeout' => 3,
            'connect_timeout' => 3
        ]);

        if ($request->getStatusCode() !== 200) {
            throw new \Exception($request->getReasonPhrase());
        }

        return json_decode($request->getBody()->getContents());
    }
}
