<?php

declare(strict_types=1);

namespace modules\twigextensionsmodule\twigextensions\Reviews\Client;

use \Craft;
use GuzzleHttp\Client;

class KiyohClient
{
    private Client $client;

    public function __construct()
    {
        $this->client = Craft::createGuzzleClient([
            'base_uri' => getenv('KIYOH_URI'),
        ]);
    }

    public function __invoke(): Client
    {
        return $this->client;
    }
}
