<?php

declare(strict_types=1);

namespace modules\twigextensionsmodule\twigextensions\Reviews\Helper;

class ReviewHelper
{
    public function sortByDate(\stdClass $reviews): void
    {
        usort($reviews->reviews, function ($a, $b) {
            return strtotime($b->dateSince) - strtotime($a->dateSince);
        });
    }

    public function extractMinimumRating(\stdClass $reviews, int $minimumRate, ?int $maxItems = 10): void
    {
        $reviews->reviews = array_slice(array_filter($reviews->reviews, function ($review) use ($minimumRate) {
            if ($review->rating >= $minimumRate) {
                foreach ($review->reviewContent as $content) {
                    switch ($content->questionGroup) {
                        case 'DEFAULT_ONELINER':
                            $review->oneliner = $content->rating;
                            break;
                        case 'DEFAULT_OPINION':
                            $review->comment = $content->rating;
                            break;
                    }
                }
                return $review;
            } else {
                return false;
            }
        }), 0, $maxItems);
    }
}
