<?php
/**
 * GetVideoInfoTwigExtension module for Craft CMS 3.x
 *
 * A module to retrieve the video host and ID from a video URL.
 *
 * @link      https://www.redkiwi.nl/
 * @copyright Copyright (c) 2022 Alex Blackburn
 */

namespace modules\twigextensionsmodule\twigextensions;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

/**
 * GetVideoInfoTwigExtension
 *
 * <AUTHOR>
 * @package   TwigExtensionsModule
 * @since     1.0.0
 */
class GetVideoInfoTwigExtension extends AbstractExtension
{
    /**
     * Returns the name of the extension.
     *
     * @return string The extension name
     */
    public function getName(): string
    {
        return 'GetVideoInfo';
    }

    /**
     * Returns an array of Twig functions, used in Twig templates via:
     * {% set this = someFunction('something') %}
     *
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('get_video_info', [$this, 'getVideoInfo']),
            new TwigFunction('get_youtube_data_from_api', [$this, 'getYoutubeDataFromAPI']),
        ];
    }

    /**
     * Get youtube data by using the id in a youtube url
     *
     * @param string $videoUrl
     *
     * @return mixed[]
     */
    public function getYoutubeDataFromAPI(string $videoUrl): array
    {
        if (!str_contains($videoUrl, 'youtu')) {
            return [
                'error' => 'Invalid URL, make sure the provided URL is a YouTube url'
            ];
        } elseif (!$_ENV["GOOGLE_API_KEY"]) {
            return [
                'error' => 'No GOOGLE_API_KEY present'
            ];
        }

        $videoId = $this->getYouTubeId($videoUrl);
        $url = "https://youtube.googleapis.com/youtube/v3/videos?part=snippet%2CcontentDetails%2Cstatistics&id=${videoId}&key=${_ENV['GOOGLE_API_KEY']}";

        $rawVideoData = [];
        $decodedVideoData = [];

        if (($rawVideoData = @file_get_contents($url)) === false) {
            $error = error_get_last();

            return [
                'error' => $error
            ];
        } else {
            $decodedVideoData = json_decode($rawVideoData, true);
        }

        return $decodedVideoData;
    }

    /**
     * Parse the video URL for information
     *
     * @param string $videoUrl
     *
     * @return mixed[]
     */
    public function getVideoInfo(string $videoUrl): array
    {
        if (str_contains($videoUrl, 'vimeo')) {
            return [
                'host' => 'vimeo',
                'id' => $this->getVimeoId($videoUrl),
            ];
        } elseif (str_contains($videoUrl, 'youtu')) {
            return [
                'host' => 'youtube',
                'id' => $this->getYouTubeId($videoUrl),
            ];
        } elseif (str_contains($videoUrl, 'wistia') || str_contains($videoUrl, 'wi.st')) {
            return [
                'host' => 'wistia',
                'id' => $this->getWistiaId($videoUrl),
            ];
        }

        return [];
    }

    /**
     * @param string $videoUrl
     *
     * @return string
     */
    private function getVimeoId(string $videoUrl): string
    {
        if (preg_match('/vimeo.com\/([0-9]+)\/?/', $videoUrl, $matches)) {
            return $matches[1];
        }

        return '';
    }

    /**
     * @param string $videoUrl
     *
     * @return string
     */
    private function getYouTubeId(string $videoUrl): string
    {
        if (preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/ ]{9,15})/', $videoUrl, $matches)) {
            return $matches[1];
        }

        return '';
    }

    /**
     * @param string $videoUrl
     *
     * @return string
     */
    private function getWistiaId(string $videoUrl): string
    {
        if (preg_match('/https?:\/\/[^.]+\.(wistia\.com|wi\.st)\/(medias|embed)\/(.*)/', $videoUrl, $matches)) {
            return end($matches);
        }

        return '';
    }
}
