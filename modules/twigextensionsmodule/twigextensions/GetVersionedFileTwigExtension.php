<?php
/**
 * GetVersionedFileTwigExtension module for Craft CMS 3.x
 *
 * A module to retrieve versioned assets from a generated manifest.json.
 *
 * @link      https://www.redkiwi.nl/
 * @copyright Copyright (c) 2022 Jen<PERSON> de <PERSON>d
 */

namespace modules\twigextensionsmodule\twigextensions;

use Craft;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

/**
 * GetVersionedFileTwigExtension
 *
 * <AUTHOR>
 * @package   TwigExtensionsModule
 * @since     1.0.0
 */
class GetVersionedFileTwigExtension extends AbstractExtension
{
    protected string $publicDir;
    protected string $manifestName;
    /**
     * @var array<string|string>|null
     */
    protected array|null $manifest = null;

    public function __construct(string $publicDir = "/build", string $manifestName = 'manifest.json')
    {
        $this->publicDir = rtrim($publicDir, '/');
        $this->manifestName = $manifestName;
    }

    /**
     * Returns the name of the extension.
     *
     * @return string The extension name
     */
    public function getName(): string
    {
        return 'GetVersionedFile';
    }

    /**
     * Returns an array of Twig functions, used in Twig templates via:
     * {% set this = someFunction('something') %}
     *
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('get_versioned_file', [$this, 'getVersionedFilePath']),
        ];
    }

    /**
     * Gets the public url/path to a versioned manifest file.
     *
     * @param string $file
     *
     * @return string
     *
     * @throws \InvalidArgumentException
     */
    public function getVersionedFilePath(string $file): string
    {
        $manifest = $this->getManifest();

        if (!isset($manifest[$file])) {
            throw new \InvalidArgumentException("File {$file} not defined in asset manifest.");
        }
        //Only return the file path relative to the public folder (e.g css/style.css) and not (/public/css/style)
        return rtrim($this->publicDir . '/' . $manifest[$file], '/');
    }

    /**
     * Returns the manifest file content as array.
     *
     * @return array<string, string>
     */
    protected function getManifest(): array
    {
        if (null === $this->manifest) {
            $manifestPath = Craft::getAlias("@webroot") . $this->publicDir . '/' . $this->manifestName;
            if ($manifestData = file_get_contents($manifestPath)) {
                $this->manifest = json_decode($manifestData, true);
            } else {
                $this->manifest = [];
            }
        }

        return $this->manifest;
    }
}
