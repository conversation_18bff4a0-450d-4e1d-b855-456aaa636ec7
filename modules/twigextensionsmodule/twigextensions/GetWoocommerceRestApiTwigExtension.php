<?php

declare(strict_types=1);

namespace modules\twigextensionsmodule\twigextensions;

use \Craft;
use modules\twigextensionsmodule\twigextensions\Woocommerce\Client\WooCommerceClient;
use modules\twigextensionsmodule\twigextensions\Woocommerce\Helper\WoocommerceHelper;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class GetWoocommerceRestApiTwigExtension extends AbstractExtension
{
    private const PRODUCTS_CACHE_KEY = 'woocommerce_products';
    private const PRODUCT_TAXES_CACHE_KEY = 'woocommerce_taxes';
    private const PRODUCT_CATEGORIES_CACHE_KEY = 'woocommerce_product_categories';
    private const CACHE_DURATION = 60 * 60 * 24;
    private const PRODUCTS_ENDPOINT = 'products';
    private const PRODUCT_CATEGORIES_ENDPOINT = 'categories';
    private const TAXES_ENDPOINT = 'taxes';
    private WooCommerceClient $wooCommerceClient;
    private WoocommerceHelper $woocommerceHelper;

    public function __construct()
    {
        $this->wooCommerceClient = new WooCommerceClient();
        $this->woocommerceHelper = new WoocommerceHelper();
    }

    public function getName(): string
    {
        return 'GetWoocommerceRestApi';
    }

    /**
     * Returns an array of Twig functions, used in Twig templates via:
     * {% set data = get_woocommerce_products() %}
     *
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('get_woocommerce_products', [$this, 'getProducts']),
        ];
    }

    public function getProducts(): mixed
    {
        if (Craft::$app->getCache()->exists(self::PRODUCTS_CACHE_KEY)) {
            return Craft::$app->getCache()->get(self::PRODUCTS_CACHE_KEY);
        }

        $taxes = $this->getTaxes();
        $categories = $this->getProductCategories();

        try {
            $products = $this->wooCommerceClient->getClient()->get(
                $this->getProductsEndpoint()
                , [
                'per_page' => 50,
            ]);
        } catch (\Exception $e) {
            Craft::error($e->getMessage());
            return null;
        }

        $list = $this->woocommerceHelper->getProductsListWithAttributes(
            $products,
            $this->woocommerceHelper->getTaxRate($taxes),
            $categories
        );

        Craft::$app->getCache()->set(self::PRODUCTS_CACHE_KEY, $list, self::CACHE_DURATION);

        return $list;
    }

    public function getProductsEndpoint(): string
    {
        return self::PRODUCTS_ENDPOINT;
    }

    public function getTaxesEndpoint(): string
    {
        return self::TAXES_ENDPOINT;
    }

    public function getCategoriesEndpoint(): string
    {
        return self::PRODUCTS_ENDPOINT . '/' . self::PRODUCT_CATEGORIES_ENDPOINT;
    }

    public function getTaxes(): mixed
    {
        if (Craft::$app->getCache()->exists(self::PRODUCT_TAXES_CACHE_KEY)) {
            return Craft::$app->getCache()->get(self::PRODUCT_TAXES_CACHE_KEY);
        }

        try {
            $taxes = $this->wooCommerceClient->getClient()->get($this->getTaxesEndpoint());
        } catch (\Exception $e) {
            Craft::error($e->getMessage());
            return null;
        }

        Craft::$app->getCache()->set(self::PRODUCT_TAXES_CACHE_KEY, $taxes, self::CACHE_DURATION);

        return $taxes;
    }

    public function getProductCategories(): mixed
    {
        if (Craft::$app->getCache()->exists(self::PRODUCT_CATEGORIES_CACHE_KEY)) {
            return Craft::$app->getCache()->get(self::PRODUCT_CATEGORIES_CACHE_KEY);
        }

        try {
            $categories = $this->wooCommerceClient->getClient()->get($this->getCategoriesEndpoint());
        } catch (\Exception $e) {
            Craft::error($e->getMessage());
            return null;
        }

        Craft::$app->getCache()->set(self::PRODUCT_CATEGORIES_CACHE_KEY, $categories, self::CACHE_DURATION);

        return $categories;
    }
}
