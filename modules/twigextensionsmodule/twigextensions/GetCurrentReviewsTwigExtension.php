<?php

declare(strict_types=1);

namespace modules\twigextensionsmodule\twigextensions;

use \Craft;
use modules\twigextensionsmodule\twigextensions\Reviews\Helper\ReviewHelper;
use modules\twigextensionsmodule\twigextensions\Reviews\Repository\ReviewRepository;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class GetCurrentReviewsTwigExtension extends AbstractExtension
{
    private const CACHE_KEY = 'reviews';
    private const SCORE_CACHE_KEY = 'kiyoh_score';
    private const CACHE_DURATION = 60 * 60 * 24;

    private ReviewRepository $reviewRepository;
    private ReviewHelper $reviewHelper;

    public function __construct()
    {
        $this->reviewRepository = new ReviewRepository();
        $this->reviewHelper = new ReviewHelper();
    }

    public function getName(): string
    {
        return 'GetCurrentReviews';
    }

    /**
     * Returns an array of Twig functions, used in Twig templates via:
     * {% set data = get_current_reviews(10) %}
     * {% set score = get_kiyoh_score() %}
     *
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('get_current_reviews', [$this, 'getCurrentReviews']),
            new TwigFunction('get_kiyoh_score', [$this, 'getKiyohScore']),
        ];
    }

    public function getCurrentReviews(int $minimumRating, ?int $maxItems): ?\StdClass
    {
        $cacheKey = self::CACHE_KEY . '_' . $minimumRating;

        if($maxItems !== null) {
            $cacheKey .= '_' . $maxItems;
        }

        if (Craft::$app->getCache()->exists($cacheKey)) {
            return Craft::$app->getCache()->get($cacheKey);
        }

        try {
            $reviews = $this->reviewRepository->fetchAll();
        } catch (\Exception $e) {
            Craft::error($e->getMessage());
            return null;
        }

        $this->reviewHelper->sortByDate($reviews);
        $this->reviewHelper->extractMinimumRating($reviews, $minimumRating, $maxItems);

        Craft::$app->getCache()->set($cacheKey, $reviews, self::CACHE_DURATION);

        return $reviews;
    }

    /**
     * Gets the overall Kiyoh review score, cached for one hour
     *
     * @return float|null
     */
    public function getKiyohScore(): ?float
    {
        $cacheKey = self::SCORE_CACHE_KEY;
        if (Craft::$app->getCache()->exists($cacheKey)) {
            return Craft::$app->getCache()->get($cacheKey);
        }

        try {
            $reviews = $this->reviewRepository->fetchAll();
            $score = property_exists($reviews, 'last12MonthAverageRating') ? (float)$reviews->last12MonthAverageRating : null;
            if($score === null) {
                $score = property_exists($reviews, 'averageRating') ? (float)$reviews->averageRating : null;
            }

            if ($score !== null) {
                Craft::$app->getCache()->set($cacheKey, $score, self::CACHE_DURATION);
            }

            return $score;
        } catch (\Exception $e) {
            Craft::error($e->getMessage());
            return null;
        }
    }
}
