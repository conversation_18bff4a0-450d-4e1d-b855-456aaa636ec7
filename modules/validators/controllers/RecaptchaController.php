<?php

namespace modules\validators\controllers;

use Craft;
use craft\web\Controller;
use craft\web\Request;
use yii\web\Response;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class RecaptchaController extends Controller
{
    protected array|bool|int $allowAnonymous = true;
    private const FORM_ID = 'aanmeldformulier_website';
    private const SOURCE = 'aanmeldformulier_website';
    private string $recaptchaSecret;
    private string $customerIoApiVersion;
    private string $customerIoApiKey;
    private string $customerIoSiteId;
    private Client $client;

    public function init(): void
    {
        parent::init();
        $this->recaptchaSecret = getenv('RECAPTCHA_SECRET_KEY') ?: '';
        $this->customerIoApiVersion = getenv('CUSTOMER_IO_API_VERSION') ?: '';
        $this->customerIoApiKey = getenv('CUSTOMER_IO_API_KEY') ?: '';
        $this->customerIoSiteId = getenv('CUSTOMER_IO_SITE_ID') ?: '';

        if (empty($this->recaptchaSecret)) {
            Craft::error('RECAPTCHA_SECRET environment variable not set!', __METHOD__);
        }
        $this->client = Craft::createGuzzleClient();
    }

    public function actionSubmitNewsletter(): Response
    {
        $request = Craft::$app->getRequest();

        if (!($request instanceof Request) || $request->getIsConsoleRequest()) {
            Craft::error('This controller is intended for web requests only.', __METHOD__);
            return $this->redirectWithError('', 'invalid_request');
        }

        $this->requirePostRequest();

        // Retrieve the redirect URL once.
        $redirectUrl = $request->getBodyParam('redirect', '');

        if (!$this->validateRecaptcha($request->getBodyParam('g-recaptcha-response'))) {
            Craft::error('reCAPTCHA validation failed', __METHOD__);
            return $this->redirectWithError($redirectUrl, 'recaptcha');
        }

        $data = ['email' => $request->getBodyParam('email')];

        if (!$this->sendToCustomerIo($data)) {
            Craft::error('Failed to send data to Customer.io', __METHOD__);
            return $this->redirectWithError($redirectUrl, 'submission');
        }

        return $this->redirectWithSuccess($redirectUrl);
    }

    private function validateRecaptcha(?string $token): bool
    {
        $request = Craft::$app->getRequest();
        $userIP = ($request instanceof Request) ? $request->getUserIP() : '';

        if (empty($token)) {
            Craft::error('No reCAPTCHA token received.', __METHOD__);
            return false;
        }

        try {
            $result = $this->makeRequest(
                'https://www.google.com/recaptcha/api/siteverify',
                [
                    'form_params' => [
                        'secret' => $this->recaptchaSecret,
                        'response' => $token,
                        'remoteip' => $userIP
                    ],
                    'timeout' => 30,
                ]
            );

            if (!is_string($result['body'])) {
                Craft::error('Invalid response body received during reCAPTCHA validation.', __METHOD__);
                return false;
            }

            $response = json_decode($result['body'], true);
            return isset($response['success']) && $response['success'] === true;
        } catch (\Exception $e) {
            Craft::error('reCAPTCHA validation error: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * @param array<string, mixed> $data
     */
    private function sendToCustomerIo(array $data): bool
    {
        $apiVersion = $this->customerIoApiVersion;
        $formID = self::FORM_ID;
        $url = "https://track-eu.customer.io/api/{$apiVersion}/forms/{$formID}/submit";

        $data['Nieuwsbrief_bron'] = self::SOURCE;

        try {
            $result = $this->makeRequest($url,  [
                'json' => [
                    'data' => $data
                ],
                'headers' => [
                    'Authorization' => 'Basic ' . base64_encode($this->customerIoSiteId . ':' . $this->customerIoApiKey),
                    'Content-Type' => 'application/json'
                ]
            ]);
            return ($result['status'] >= 200 && $result['status'] < 300) || $result['status'] == 302;
        } catch (\Exception $e) {
            Craft::error('CustomerIO submit error: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * @param array<string, mixed> $data
     * @return array{body: string|bool, status: int}
     */
    private function makeRequest(string $url, array $data): array
    {
        try {
            $response = $this->client->post($url, $data);

            return [
                'body' => (string)$response->getBody(),
                'status' => $response->getStatusCode()
            ];
        } catch (GuzzleException $e) {
            Craft::error('Request error: ' . $e->getMessage(), __METHOD__);
            return ['body' => false, 'status' => 500];
        }
    }

    private function redirectWithError(string $url, string $error): Response
    {
        $redirectUrl = filter_var($url, FILTER_VALIDATE_URL) ? $url : '/';
        return $this->redirect($redirectUrl . (str_contains($redirectUrl, '?') ? '&' : '?') . 'error=' . $error);
    }

    private function redirectWithSuccess(string $url): Response
    {
        $queryParams = [];

        if (filter_var($url, FILTER_VALIDATE_URL) && $parsedUrl = parse_url($url)) {
            parse_str($parsedUrl['query'] ?? '', $queryParams);
            $newUrl = $parsedUrl['path'] ?? '';
        } else {
            Craft::error('Invalid URL provided for redirect.', __METHOD__);
            return $this->redirect('/?subscribed=true');
        }

        $queryParams['subscribed'] = 'true';
        $newUrl .= '?' . http_build_query($queryParams);

        return $this->redirect($newUrl);
    }
}