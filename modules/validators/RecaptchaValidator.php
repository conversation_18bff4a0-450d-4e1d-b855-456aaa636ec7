<?php
namespace modules\validators;

use Craft;
use yii\base\Module;

class RecaptchaValidator extends Module
{
    public static function getInstance(): self
    {
        $module = Craft::$app->getModule('validators');
        if (!($module instanceof self)) {
            throw new \RuntimeException('Validators module is not properly configured');
        }
        return $module;
    }

    public function init(): void
    {
        parent::init();
        $this->id = 'validators';
        $this->controllerNamespace = Craft::$app->getRequest()->getIsConsoleRequest()
            ? 'modules\\validators\\console\\controllers'
            : 'modules\\validators\\controllers';
        Craft::setAlias('@modules/validators', __DIR__);
    }
}