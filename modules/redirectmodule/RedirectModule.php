<?php

namespace modules\redirectmodule;

use Craft;
use yii\base\Module;
use craft\web\Request;
use craft\web\Response;

class RedirectModule extends Module
{
    /**
     * @param string $id
     * @param Module $parent
     * @param mixed[] $config
     */
    public function __construct($id, $parent = null, array $config = [])
    {
        Craft::setAlias('@modules/redirectmodule', $this->getBasePath());

        // Set this as the global instance of this module class
        static::setInstance($this);

        parent::__construct($id, $parent, $config);
    }

    public function init(): void
    {
        parent::init();
        if (Craft::$app->getRequest()->getIsConsoleRequest()) {
            return;
        }

        // Now that we're sure it's a web request, tell PHPStan so.
        /** @var Request $request */
        $request = Craft::$app->getRequest();

        Craft::$app->on('beforeRequest', function () use ($request) {
            if (!$request->getIsGet() || $request->getIsAjax()) {
                return;
            }

            if ($request->getIsActionRequest() || $request->getIsCpRequest()) {
                return;
            }

            if (strpos($request->getPathInfo(), '.xml') !== false) {
                return;
            }

            $url = $request->getUrl();

            if (strpos($url, '/' . getenv('CP_TRIGGER')) === false) {
                $parsed = parse_url($url);
                $path = $parsed['path'] ?? '/';

                if ($path !== '/' && substr($path, -1) !== '/') {
                    $path .= '/';
                }

                $query = isset($parsed['query']) ? '?' . $parsed['query'] : '';
                $newUrl = $path . $query;

                if ($newUrl !== $url) {
                    /** @var Response $response */
                    $response = Craft::$app->getResponse();
                    $response->redirect($newUrl, 301)->send();
                }
            }
        });
    }
}
