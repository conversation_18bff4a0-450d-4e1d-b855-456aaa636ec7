{"name": "redkiwi/milieu-service-nederland", "description": "Milieu Service Nederland website", "minimum-stability": "dev", "prefer-stable": true, "require": {"automattic/woocommerce": "^3.1", "craftcms/cms": "^4.14", "craftcms/element-api": "^4.2.0", "craftcms/feed-me": "^5.11.0", "craftcms/redactor": "3.1.0", "doublesecretagency/craft-inventory": "^3.1.0", "mmikkel/cp-field-inspect": "1.4.4", "nystudio107/craft-eagerbeaver": "^4.0", "nystudio107/craft-retour": "^4.1.25", "nystudio107/craft-seomatic": "^4.1.13", "nystudio107/craft-templatecomments": "^4.0.0", "nystudio107/craft-vite": "^4.0.10", "putyourlightson/craft-elements-panel": "^2.0", "putyourlightson/craft-sprig": "^2.14.1", "redkiwi/craft-endpoints": "dev-master", "sebastianlenz/linkfield": "2.1.5", "spicyweb/craft-neo": "^4.4.1", "spicyweb/craft-quick-field": "^2.0.5", "studioespresso/craft-dumper": "5.0.1", "verbb/field-manager": "3.0.9", "verbb/formie": "^2.1.47", "verbb/icon-picker": "^2.0.20", "verbb/image-resizer": "^3.0.13", "verbb/navigation": "^2.1", "verbb/smith": "^2.0.1", "verbb/super-table": "^3.0.15", "vlucas/phpdotenv": "v5.5.0", "wbrowar/craft-admin-bar": "4.3.0"}, "require-dev": {"friendsoftwig/twigcs": "^6.5.0", "nystudio107/craft-autocomplete": "^1.12.2", "php-parallel-lint/php-parallel-lint": "v1.4.0", "phpro/grumphp": "^v2.12.0", "studio-stomp/phpstan-craftcms": "dev-main", "yiisoft/yii2-shell": "^2.0.6"}, "repositories": [{"type": "path", "url": "dev/redkiwi-endpoints"}, {"type": "composer", "url": "https://composer.craftcms.com", "canonical": false}], "autoload": {"psr-4": {"modules\\": "modules/"}}, "autoload-dev": {"psr-4": {"App\\TwigCs\\": ".tools/TwigCs"}}, "config": {"allow-plugins": {"craftcms/plugin-installer": true, "yiisoft/yii2-composer": true, "phpro/grumphp": true}, "optimize-autoloader": true, "platform": {"php": "8.1"}, "sort-packages": true}, "scripts": {"lint": "vendor/bin/twigcs templates --ruleset \"\\App\\TwigCs\\TwigCsRuleset\"", "twigcs": "vendor/bin/twigcs templates --ruleset \"\\App\\TwigCs\\TwigCsRuleset\" --reporter junit > storage/twig-cs-report.xml", "phpstan": "php -d memory_limit=-1 vendor/bin/phpstan analyse modules", "craft-update": ["@pre-craft-update", "@post-craft-update"], "pre-craft-update": [], "post-craft-update": ["@php craft install/check && php craft up --interactive=0 || exit 0", "@php craft install/check && php craft clear-caches/all --interactive=0 || exit 0", "@php craft install/check && php craft invalidate-tags/all --interactive=0 || return 0"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php craft setup/welcome"], "pre-update-cmd": "@pre-craft-update", "pre-install-cmd": "@pre-craft-update", "post-update-cmd": "@post-craft-update", "post-install-cmd": "@post-craft-update"}}