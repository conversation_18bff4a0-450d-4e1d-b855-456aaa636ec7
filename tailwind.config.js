/** @type {import('tailwindcss').Config} */
const defaultTheme = require('tailwindcss/defaultTheme')
const plugin = require('tailwindcss/plugin')
module.exports = {
    mode: 'jit',
    corePlugins: {
        container: false,
    },
    plugins: [
        require('@tailwindcss/typography'),
    ],
    content: [
        './src/scss/**/*.scss',
        './src/ts/**/*.ts',
        './templates/**/*.twig',
    ],
    theme: {
        fontFamily: {
            regular: ['Ubuntu Regular', ...defaultTheme.fontFamily.sans],
            regularItalic: ['Ubuntu Italic', ...defaultTheme.fontFamily.sans],
            medium: ['Ubuntu Medium', ...defaultTheme.fontFamily.sans],
            mediumItalic: [
                'Ubuntu Medium Italic',
                ...defaultTheme.fontFamily.sans,
            ],
            bold: ['Ubuntu Bold', ...defaultTheme.fontFamily.sans],
            boldItalic: ['Ubuntu Bold Italic', ...defaultTheme.fontFamily.sans],
        },
        fontSize: {
            10: ['0.625rem'],
            11: ['0.06875rem'],
            12: ['0.75rem'],
            13: ['0.8125rem'],
            14: ['0.875rem'],
            15: ['0.9375rem'],
            base: ['1rem'],
            16: ['1rem'],
            18: ['1.125rem'],
            20: ['1.25rem'],
            21: ['1.3125rem'],
            22: ['1.375rem'],
            24: ['1.5rem'],
            26: ['1.625rem'],
            28: ['1.75rem'],
            32: ['2rem'],
            36: ['2.25rem'],
            40: ['2.5rem'],
            48: ['3rem'],
            56: ['3.5rem'],
            64: ['4rem'], // key visual h1
        },
        lineHeight: {
            none: 1,
            14: ['0.875rem'],
            16: ['1rem'],
            18: ['1.125rem'], // tags
            22: ['1.375rem'],
            23: ['1.4375rem'],
            24: ['1.5rem'],
            25: ['1.5625rem'],
            28: ['1.75rem'],
            32: ['2rem'],
            40: ['2.5rem'],
            46: ['2.875rem'],
            48: ['3rem'],
            56: ['3.5rem'],
            64: ['4rem'],
            74: ['4.625rem'], // key visual h1
        },
        screens: {
            sm: '30rem', // 480px
            'sm-max': {max: '47.9375rem'}, // 767px
            md: '48rem', // 768px
            'md-max': {max: '63.9375rem'}, // 1023px
            lg: '64rem', // 1024px
            'nav-mobile': {max: '74.9375rem'}, // 1199px
            'nav-desktop': '75rem', // 1200px
            'lg-max': {max: '79.9375rem'}, // 1279px
            xl: '80rem', // 1280px
            'xl-max': {max: '89.9375rem'}, // 1439px
            '2xl': '90rem', // 1440px
            '2xl-max': {max: '99.9375rem'}, // 1599px
            '3xl': '100rem', // 1600px
        },
        extend: {
            animation: {
                'spin-slow': 'spin 12s linear infinite', // key visual background shapes
                'spin-slowest': 'spin 24s linear infinite', // platform visual arrows
                'ping-slow': 'slight-ping 1.5s linear infinite', // platform visual button ping
            },
            keyframes: {
                'slight-ping': {
                    '75%, 100%': {
                        transform: 'scale(1.8)',
                        opacity: '0',
                    },
                },
            },
            colors: {
                primary: {
                    // green
                    50: '#E5F5EC',
                    100: '#CCEBD8',
                    300: '#80CE9E',
                    500: '#009D3D',
                    700: '#007E31',
                    900: '#004F1F',
                },
                secondary: {
                    // blue
                    100: '#CCECF9',
                    300: '#80CFF1',
                    500: '#009FE3',
                    700: '#007BB0',
                    900: '#003044',
                    1000: '#E5F5FC',
                },
                tertiary: {
                    //  gray
                    100: '#FAFAFA',
                    300: '#E6E7E8',
                    500: '#BDBDBD',
                    600: '#999999',
                    700: '#646464',
                    900: '#454545',
                },
                basic: {
                    white: '#FFFFFF',
                    light: '#FAFAFA',
                    lightgray: '#F2F2F2',
                    black: '#1A1A1A',
                    error: '#DF0A14',
                    lightError: '#FBE6E7',
                },
                waste: {
                    rough: '#414B50', // grof
                    bio: '#009D3D',
                    paper: '#009FE3', // papier
                    chemical: '#8A39F8', // chemisch
                    plastic: '#E7711B',
                    debris: '#EE8940', // puin
                    wood: '#955442', // hout
                    glass: '#E5B803', // glas
                    minipress: '#9B8C75', //minipers
                    metal: '#A6A6A6', // metaal
                },
            },
            container: false,
            zIndex: {
                '-1': '-1',
                1: '1',
                2: '2',
            },
        },
    },
    variants: {
        extend: {},
    },
}
